FROM innocody-base:latest

# Add system dependencies
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
        python3.11-dev \
        libmpich-dev && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/*

# Set CUDA path
RUN echo "export PATH=/usr/local/cuda/bin:\$PATH" > /etc/profile.d/50-smc.sh

# Python dependencies
COPY requirements.server /tmp/requirements.txt
RUN pip install -r /tmp/requirements.txt && \
    pip install optimum[onnxruntime,openvino]

# Environment Variables
ENV INNOCODY_PERM_DIR="/perm_storage"
ENV INNOCODY_TMP_DIR="/tmp"
ENV RDMAV_FORK_SAFE=0
ENV RDMAV_HUGEPAGES_SAFE=0

# Application port
EXPOSE 8008

# Copy app files
COPY . /

CMD ["python", "-m", "self_hosting_machinery.watchdog.docker_watchdog"]
