# Default: do nothing global — mold must be per-target
[build]
# Optional: set your default build target
# target = "x86_64-unknown-linux-gnu"

# =====================
# Linux Targets
# =====================
[target.x86_64-unknown-linux-gnu]
linker = "clang"
rustflags = [
    "-C", "link-arg=--target=x86_64-unknown-linux-gnu",
    "-C", "link-arg=-fuse-ld=mold"
]

[target.aarch64-unknown-linux-gnu]
linker = "clang"
rustflags = [
    "-C", "link-arg=--target=aarch64-unknown-linux-gnu",
    "-C", "link-arg=-fuse-ld=mold"
]

# =====================
# Windows (MSVC) Targets
# =====================
[target.x86_64-pc-windows-msvc]
linker = "rust-lld"
rustflags = ["-Ctarget-feature=+crt-static"]

[target.aarch64-pc-windows-msvc]
linker = "rust-lld"
rustflags = ["-Ctarget-feature=+crt-static"]

# =====================
# macOS Targets
# =====================
[target.x86_64-apple-darwin]
linker = "clang"

[target.aarch64-apple-darwin]
linker = "clang"
