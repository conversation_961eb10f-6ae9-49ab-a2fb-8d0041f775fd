#!/bin/sh
INNOCODY_CASSANDRA_DIR="$INNOCODY_PERM_DIR/cassandra"
if [ ! -d "$INNOCODY_CASSANDRA_DIR" ]; then
    mkdir -p "$INNOCODY_CASSANDRA_DIR"
    chown cassandra:cassandra "$INNOCODY_CASSANDRA_DIR"
    if [ ! -z "$(ls /var/lib/cassandra 2>/dev/null)" ]; then
        cp -rp /var/lib/cassandra/* "$INNOCODY_CASSANDRA_DIR"
    fi
    cp -rp /var/log/cassandra "$INNOCODY_CASSANDRA_DIR/log"
fi

# Patch cassandra config to work with INNOCODY_CASSANDRA_DIR
sed -i "s|/var/lib/cassandra|$INNOCODY_CASSANDRA_DIR|g" /etc/cassandra/cassandra.yaml

# Patch cassandra.in.sh for memory and log adjustments
INNOCODY_CASSANDRA_INCLUDE=/usr/sbin/cassandra.in.sh
cp /usr/share/cassandra/cassandra.in.sh "$INNOCODY_CASSANDRA_INCLUDE"
echo "MAX_HEAP_SIZE=256M" >> "$INNOCODY_CASSANDRA_INCLUDE"
echo "HEAP_NEWSIZE=100M" >> "$INNOCODY_CASSANDRA_INCLUDE"
echo "CASSANDRA_LOG_DIR=$INNOCODY_CASSANDRA_DIR/log" >> "$INNOCODY_CASSANDRA_INCLUDE"

# Clean up stale pidfile if necessary
if service cassandra status | grep -q 'could not access pidfile'; then
    rm -f /var/run/cassandra/cassandra.pid
fi

# Start Cassandra if not running
if service cassandra status | grep -q 'not running'; then
    service cassandra start
    echo "Starting Cassandra..."
fi

# Wait for port 9042 to be available (CQL native transport)
MAX_RETRIES=5
RETRY_DELAY=2
retry_count=0

echo "Waiting for Cassandra to accept connections on port 9042..."

while ! nc -z localhost 9042 2>/dev/null; do
    if [ $retry_count -ge $MAX_RETRIES ]; then
        echo "Cassandra did not open port 9042 within expected time."
        exit 1
    fi
    echo "Port 9042 not open yet... retrying ($((retry_count + 1))/$MAX_RETRIES)"
    sleep $RETRY_DELAY
    retry_count=$((retry_count + 1))
done

echo "Cassandra is accepting connections on port 9042."
