[profile.ci]
inherits = "dev"
opt-level = 0                  # No optimization, fastest builds
debug = true                   # Full debug info
incremental = true             # Reuse previous builds
codegen-units = 256            # Parallel codegen for fast builds
lto = false                    # No LTO in dev

[profile.release]
opt-level = 3                  # Maximize runtime performance
lto = "fat"                    # Best performance & size (slower to compile)
codegen-units = 1              # Max optimization across crate boundaries
debug = false                  # No debug symbols in final binary
strip = true                   # Strip symbols automatically (requires nightly as of Rust 1.77+)
panic = "abort"                # Smaller binary, disables unwinding
incremental = true            # Don’t keep incremental state for release builds

[package]
name = "innocody-lsp"
version = "0.0.3"
edition = "2021"
build = "build.rs"
# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[build-dependencies]
shadow-rs = "1.2.0"

[target.'cfg(windows)'.dependencies]
winreg = "0.55.0"

[dependencies]
ahash = "0.8.12"
astral-tokio-tar = "0.5.2"
axum = { version = "0.6.20", features = ["default", "http2"] }
async-process = "2.3.1"
async-stream = "0.3.6"
async-trait = "0.1.88"
backtrace = "0.3.75"
base64 = "0.22.1"
chrono = { version = "0.4.41", features = ["serde"] }
diff = "0.1.13"
dunce = "1.0.5"
dyn_partial_eq = "=0.1.2"
futures = "0.3"
git2 = "0.20.2"
glob = "0.3.2"
hashbrown = "0.15.4"
headless_chrome = "1.0.17"
home = "0.5"
html2text = "0.12.6"
hyper = { version = "0.14", features = ["server", "stream"] }
image = "0.25.6"
indexmap = { version = "1.9.3", features = ["serde-1"] }
itertools = "0.14.0"
lazy_static = "1.5.0"
libsqlite3-sys = "0.28.0"
log = "0.4.27"
md5 = "0.7"
notify = { version = "8.1.0", features = ["serde"] }
parking_lot = { version = "0.12.4", features = ["serde"] }
pnet_datalink = "0.35.0"
process-wrap = { version = "8.2.1", features = ["tokio1"] }
rand = "0.8.5"
rayon = "1.10.0"
regex = "1.11.1"
reqwest = { version = "0.12", default-features = false, features = ["json", "stream", "rustls-tls-webpki-roots", "charset", "http2"] }
reqwest-eventsource = "0.6.0"
resvg = "0.44.0"
ropey = "1.6"
rusqlite = { version = "0.31.0", features = ["bundled"] }
rust-embed = "8.7.2"
percent-encoding = "2.3"
serde = { version = "1", features = ["rc", "derive"] }
serde_cbor = "0.11.2"
serde_json = { version = "1", features = ["preserve_order"] }
serde_yaml = "0.9.34"
# all features = ["compression", "docs", "event_log", "failpoints", "io_uring", "lock_free_delays", "measure_allocs", "miri_optimizations", "mutex", "no_inline", "no_logs", "pretty_backtrace", "testing"]
shadow-rs = { version = "1.2.0", features = [], default-features = false }
sha2 = "0.10.9"
shell-words = "1.1.0"
shell-escape = "0.1.5"
select = "0.6.1"
similar = "2.7.0"
sled = { version = "0.34", default-features = false, features = [] }
sqlite-vec = { version = "0.1.6" }
strip-ansi-escapes = "0.2.1"
strsim = "0.11.1"
structopt = "0.3"
tempfile = "3.20.0"
tokenizers = "0.21.2"
tokio = { version = "1.46.1", features = ["fs", "io-std", "io-util", "macros", "rt-multi-thread", "signal", "process"] }
tokio-rusqlite = "0.5.1"
tokio-util = { version = "0.7.15", features = ["compat"] }
tokio-tungstenite = "0.21.0"
tower = { version = "0.4", features = ["full"] }
tower-http = { version = "0.4.4", features = ["cors"] }
tower-lsp = "0.20"
tracing = "0.1"
tracing-appender = "0.2.3"
tracing-subscriber = { version = "0.3", features = ["env-filter", "json"] }
tree-sitter = "0.22"
tree-sitter-cpp = "0.22"
tree-sitter-java = "0.21"
tree-sitter-python = "0.21"
tree-sitter-rust = "0.21"

# ── Tree-sitter grammars for JavaScript / TypeScript / HTML ──
# ⚠️ KEEP ALL THREE ON THE SAME MINOR LINE (0.22.x) TO AVOID cc-crate CONFLICTS!
tree-sitter-javascript = "0.21"
tree-sitter-typescript = "0.21"
tree-sitter-html       = "0.20"

typetag = "0.2"
url = "2.5.4"
uuid = { version = "1", features = ["v4", "serde"] }
walkdir = "2.5"
which = "7.0.3"
zerocopy = "0.8.26"

# There you can use a local copy
# rmcp = { path = "../../../rust-sdk/crates/rmcp/", "features" = ["client", "transport-child-process", "transport-sse"] }
rmcp = { version = "0.2.1", features = ["client", "transport-child-process", "transport-sse-client", "reqwest"] }
