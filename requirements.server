#
# This file is autogenerated by pip-compile with Python 3.10
# by the following command:
#
#    pip-compile --extra-index-url=https://download.pytorch.org/whl/cpu --output-file=requirements.server setup.py
#
--extra-index-url https://download.pytorch.org/whl/cpu

accelerate==1.8.1
    # via
    #   innocody-self-hosting (setup.py)
    #   peft
aiofiles==24.1.0
    # via innocody-self-hosting (setup.py)
aiohappyeyeballs==2.6.1
    # via aiohttp
aiohttp==3.12.13
    # via
    #   aiohttp-cors
    #   innocody-self-hosting (setup.py)
    #   litellm
    #   ray
aiohttp-cors==0.8.1
    # via ray
aiosignal==1.3.2
    # via aiohttp
annotated-types==0.7.0
    # via pydantic
anyio==4.9.0
    # via
    #   httpx
    #   openai
    #   starlette
    #   watchfiles
async-timeout==5.0.1
    # via aiohttp
attrs==25.3.0
    # via
    #   aiohttp
    #   jsonlines
    #   jsonschema
    #   referencing
binpacking==1.5.2
    # via innocody-self-hosting (setup.py)
bitsandbytes==0.46.0
    # via innocody-self-hosting (setup.py)
blobfile==3.0.0
    # via innocody-self-hosting (setup.py)
blosc2==2.0.0
    # via tables
cachetools==5.5.2
    # via google-auth
cdifflib==1.2.9
    # via innocody-self-hosting (setup.py)
certifi==2025.6.15
    # via
    #   httpcore
    #   httpx
    #   requests
cffi==1.17.1
    # via cryptography
charset-normalizer==3.4.2
    # via requests
click==8.2.1
    # via
    #   litellm
    #   ray
    #   uvicorn
cloudpickle==3.1.1
    # via innocody-self-hosting (setup.py)
colorful==0.5.6
    # via ray
contourpy==1.3.2
    # via matplotlib
cryptography==45.0.4
    # via innocody-self-hosting (setup.py)
cycler==0.12.1
    # via matplotlib
cython==3.1.2
    # via tables
dataclasses==0.6
    # via innocody-self-hosting (setup.py)
dataclasses-json==0.6.7
    # via innocody-self-hosting (setup.py)
# deepspeed==0.17.1
    # via innocody-self-hosting (setup.py)
distlib==0.3.9
    # via virtualenv
distro==1.9.0
    # via openai
einops==0.8.1
    # via
    #   deepspeed
    #   innocody-self-hosting (setup.py)
exceptiongroup==1.3.0
    # via anyio
fastapi==0.115.2
    # via
    #   innocody-self-hosting (setup.py)
    #   ray
filelock==3.18.0
    # via
    #   blobfile
    #   huggingface-hub
    #   innocody-self-hosting (setup.py)
    #   ray
    #   torch
    #   transformers
    #   virtualenv
fonttools==4.58.4
    # via matplotlib
frozenlist==1.7.0
    # via
    #   aiohttp
    #   aiosignal
fsspec==2025.5.1
    # via
    #   huggingface-hub
    #   torch
future==1.0.0
    # via binpacking
giturlparse==0.12.0
    # via innocody-self-hosting (setup.py)
google-api-core==2.25.1
    # via opencensus
google-auth==2.40.3
    # via
    #   google-api-core
    #   innocody-self-hosting (setup.py)
googleapis-common-protos==1.70.0
    # via google-api-core
grpcio==1.73.1
    # via ray
h11==0.16.0
    # via
    #   httpcore
    #   uvicorn
hf-xet==1.1.5
    # via huggingface-hub
hjson==3.1.0
    # via deepspeed
httpcore==1.0.9
    # via httpx
httptools==0.6.4
    # via uvicorn
httpx==0.28.1
    # via
    #   litellm
    #   openai
huggingface-hub==0.33.1
    # via
    #   accelerate
    #   innocody-self-hosting (setup.py)
    #   peft
    #   sentence-transformers
    #   tokenizers
    #   transformers
idna==3.10
    # via
    #   anyio
    #   httpx
    #   requests
    #   yarl
importlib-metadata==8.7.0
    # via
    #   litellm
    #   opentelemetry-api
jinja2==3.1.6
    # via
    #   litellm
    #   torch
jiter==0.10.0
    # via openai
joblib==1.5.1
    # via scikit-learn
jsonlines==4.0.0
    # via innocody-self-hosting (setup.py)
jsonschema==4.24.0
    # via
    #   litellm
    #   ray
jsonschema-specifications==2025.4.1
    # via jsonschema
kiwisolver==1.4.8
    # via matplotlib
kshingle==0.10.0
    # via innocody-self-hosting (setup.py)
litellm==1.73.1
    # via innocody-self-hosting (setup.py)
llvmlite==0.44.0
    # via numba
lxml==5.4.0
    # via blobfile
markupsafe==3.0.2
    # via jinja2
marshmallow==3.26.1
    # via dataclasses-json
matplotlib==3.10.3
    # via innocody-self-hosting (setup.py)
more-itertools==10.7.0
    # via innocody-self-hosting (setup.py)
mpi4py==4.1.0
    # via innocody-self-hosting (setup.py)
mpmath==1.3.0
    # via sympy
msgpack==1.1.1
    # via
    #   blosc2
    #   deepspeed
    #   ray
multidict==6.5.1
    # via
    #   aiohttp
    #   yarl
mypy-extensions==1.1.0
    # via typing-inspect
networkx==3.4.2
    # via torch
ninja==********
    # via
    #   deepspeed
    #   innocody-self-hosting (setup.py)
numba==0.61.2
    # via kshingle
numexpr==2.11.0
    # via tables
numpy==1.26.4
    # via
    #   accelerate
    #   bitsandbytes
    #   contourpy
    #   deepspeed
    #   innocody-self-hosting (setup.py)
    #   kshingle
    #   matplotlib
    #   numba
    #   numexpr
    #   pandas
    #   peft
    #   scikit-learn
    #   scipy
    #   tables
    #   transformers
openai==1.91.0
    # via litellm
opencensus==0.11.4
    # via ray
opencensus-context==0.1.3
    # via opencensus
opentelemetry-api==1.34.1
    # via
    #   opentelemetry-exporter-prometheus
    #   opentelemetry-sdk
    #   opentelemetry-semantic-conventions
opentelemetry-exporter-prometheus==0.55b1
    # via ray
opentelemetry-proto==1.34.1
    # via ray
opentelemetry-sdk==1.34.1
    # via
    #   opentelemetry-exporter-prometheus
    #   ray
opentelemetry-semantic-conventions==0.55b1
    # via opentelemetry-sdk
packaging==25.0
    # via
    #   accelerate
    #   deepspeed
    #   huggingface-hub
    #   marshmallow
    #   matplotlib
    #   peft
    #   ray
    #   tables
    #   transformers
pandas==2.3.0
    # via innocody-self-hosting (setup.py)
peft==0.15.2
    # via innocody-self-hosting (setup.py)
pillow==11.2.1
    # via
    #   matplotlib
    #   sentence-transformers
platformdirs==4.3.8
    # via virtualenv
prometheus-client==0.22.1
    # via
    #   opentelemetry-exporter-prometheus
    #   ray
propcache==0.3.2
    # via
    #   aiohttp
    #   yarl
proto-plus==1.26.1
    # via google-api-core
protobuf==5.29.5
    # via
    #   google-api-core
    #   googleapis-common-protos
    #   opentelemetry-proto
    #   proto-plus
    #   ray
psutil==7.0.0
    # via
    #   accelerate
    #   deepspeed
    #   peft
py-cpuinfo==9.0.0
    # via
    #   deepspeed
    #   tables
py-spy==0.4.0
    # via ray
pyasn1==0.6.1
    # via
    #   pyasn1-modules
    #   rsa
pyasn1-modules==0.4.2
    # via google-auth
pycparser==2.22
    # via cffi
pycryptodomex==3.23.0
    # via blobfile
pydantic==2.11.7
    # via
    #   deepspeed
    #   fastapi
    #   innocody-self-hosting (setup.py)
    #   litellm
    #   openai
    #   ray
pydantic-core==2.33.2
    # via pydantic
pygments==2.19.2
    # via innocody-self-hosting (setup.py)
pyparsing==3.2.3
    # via matplotlib
python-dateutil==2.9.0.post0
    # via
    #   matplotlib
    #   pandas
python-dotenv==1.1.1
    # via
    #   litellm
    #   uvicorn
python-multipart==0.0.20
    # via innocody-self-hosting (setup.py)
pytz==2025.2
    # via pandas
pyyaml==6.0.2
    # via
    #   accelerate
    #   huggingface-hub
    #   peft
    #   ray
    #   transformers
    #   uvicorn
ray[serve]==2.47.1
    # via innocody-self-hosting (setup.py)
referencing==0.36.2
    # via
    #   jsonschema
    #   jsonschema-specifications
regex==2024.11.6
    # via
    #   tiktoken
    #   transformers
requests==2.32.4
    # via
    #   google-api-core
    #   huggingface-hub
    #   innocody-self-hosting (setup.py)
    #   ray
    #   tiktoken
    #   transformers
rpds-py==0.25.1
    # via
    #   jsonschema
    #   referencing
rsa==4.9.1
    # via google-auth
safetensors==0.5.3
    # via
    #   accelerate
    #   innocody-self-hosting (setup.py)
    #   peft
    #   transformers
scikit-learn==1.7.0
    # via sentence-transformers
scipy==1.15.3
    # via
    #   innocody-self-hosting (setup.py)
    #   scikit-learn
    #   sentence-transformers
scyllapy==1.3.0
    # via innocody-self-hosting (setup.py)
sentence-transformers==4.1.0
    # via innocody-self-hosting (setup.py)
setproctitle==1.3.6
    # via innocody-self-hosting (setup.py)
six==1.17.0
    # via
    #   opencensus
    #   python-dateutil
smart-open==7.1.0
    # via ray
sniffio==1.3.1
    # via
    #   anyio
    #   openai
starlette==0.37.2
    # via
    #   fastapi
    #   innocody-self-hosting (setup.py)
    #   ray
sympy==1.14.0
    # via torch
tables==3.8.0
    # via innocody-self-hosting (setup.py)
termcolor==3.1.0
    # via innocody-self-hosting (setup.py)
threadpoolctl==3.6.0
    # via scikit-learn
tiktoken==0.9.0
    # via litellm
tokenizers==0.21.2
    # via
    #   innocody-self-hosting (setup.py)
    #   litellm
    #   transformers
torch==2.7.0+cpu
    # via
    #   accelerate
    #   bitsandbytes
    #   deepspeed
    #   innocody-self-hosting (setup.py)
    #   peft
    #   sentence-transformers
torchinfo==1.8.0
    # via innocody-self-hosting (setup.py)
tqdm==4.67.1
    # via
    #   deepspeed
    #   huggingface-hub
    #   innocody-self-hosting (setup.py)
    #   openai
    #   peft
    #   sentence-transformers
    #   transformers
transformers==4.52.4
    # via
    #   innocody-self-hosting (setup.py)
    #   peft
    #   sentence-transformers
triton==3.3.1
    # via innocody-self-hosting (setup.py)
typing-extensions==4.14.0
    # via
    #   anyio
    #   exceptiongroup
    #   fastapi
    #   huggingface-hub
    #   multidict
    #   openai
    #   opentelemetry-api
    #   opentelemetry-sdk
    #   opentelemetry-semantic-conventions
    #   pydantic
    #   pydantic-core
    #   referencing
    #   sentence-transformers
    #   torch
    #   typing-inspect
    #   typing-inspection
    #   uvicorn
typing-inspect==0.9.0
    # via dataclasses-json
typing-inspection==0.4.1
    # via pydantic
tzdata==2025.2
    # via pandas
ujson==5.10.0
    # via innocody-self-hosting (setup.py)
urllib3==2.5.0
    # via
    #   blobfile
    #   requests
uvicorn[standard]==0.34.3
    # via
    #   innocody-self-hosting (setup.py)
    #   ray
uvloop==0.21.0
    # via
    #   innocody-self-hosting (setup.py)
    #   uvicorn
virtualenv==20.31.2
    # via ray
watchfiles==1.1.0
    # via
    #   ray
    #   uvicorn
websockets==15.0.1
    # via uvicorn
wrapt==1.17.2
    # via smart-open
yarl==1.20.1
    # via aiohttp
zipp==3.23.0
    # via importlib-metadata
zstandard==0.23.0
    # via innocody-self-hosting (setup.py)

# The following packages are considered to be unsafe in a requirements file:
# setuptools
