# Innocody Engine

This is a small executable written in Rust, a part of the Innocody project. Its main job is to live
inside your IDE quietly and keep AST and VecDB indexes up to date. It is well-written: it will not break if
you edit your files quickly or switch branches, it caches vectorization model responses so you
don't have to wait for VecDB to complete indexing, AST supports connection graph between definitions
and usages in many popular programming languages.

Yes, it looks like an LSP server to IDE, hence the name. It can also work within a python program,
check out the [Text UI](#cli) below, you can talk about your project in the command line!

---


# Table of Contents

- [Installation](#installation)
- [AST](#ast)
- [CLI](#cli)

# Key Features

* Integrates with the IDE you are already using, like VSCode or JetBrains
* Offers assistant functionality: code completion and chat
* Keeps track of your source files, keeps AST and vector database up to date
* Integrates browser, databases, debuggers for the model to use
* Ask it anything! It will use the tools available to make changes to your project


## Installation

Installable by the end user:

 * [VS Code](https://gitlab-v2.innotech-vn.com/the-ai-team/innocody/innocody-vscode)

### Compiling and Running

```
cargo build
target/debug/innocody-lsp --address-url http://localhost:8008/ --http-port 8001 --lsp-port 8002 --ast --vecdb --no-shutdown
```

For run only
```
cargo run -- --address-url http://localhost:8008/ --http-port 8001 --lsp-port 8002 --ast --vecdb --logs-stderr --no-shutdown
```

Try `--help` for more options.

## Things to Try

Code completion:

```bash
curl http://127.0.0.1:8001/v1/code-completion -k \
  -H 'Content-Type: application/json' \
  -d '{
  "inputs": {
    "sources": {"hello.py": "def hello_world():"},
    "cursor": {
      "file": "hello.py",
      "line": 0,
      "character": 18
    },
    "multiline": true
  },
  "stream": false,
  "parameters": {
    "temperature": 0.1,
    "max_new_tokens": 20
  }
}'
```

RAG status:

```bash
curl http://127.0.0.1:8001/v1/rag-status
```

Chat, the not-very-standard version, it has deterministic_messages in response for all your @-mentions. The more standard version
is at /v1/chat/completions.

```bash
curl http://127.0.0.1:8001/v1/chat -k \
  -H 'Content-Type: application/json' \
  -d '{
  "messages": [
    {"role": "user", "content": "Who is Bill Clinton? What is his favorite programming language?"}
  ],
  "stream": false,
  "temperature": 0.1,
  "max_tokens": 20
}'
```

## Caps File

The capabilities file stores describes how to access AI models.
The `--address-url` parameter controls where to get this file.
If it's a URL, the executable fetches `$URL/innocody-caps` to know what to do. This is especially useful to connect to Innocody Self-Hosting Server,
because the configuration does not need to be copy-pasted among engineers who use the server.


## AST

Supported languages:

- [x] Java
- [x] JavaScript
- [x] TypeScript
- [x] Python
- [x] Rust
- [ ] C#

You can still use Innocody for other languages, just the AST capabilities will be missing.

## CLI

You can compile and use Innocody Agent from command line with this repo alone, and it's a not an afterthought, it works great!

```
cargo build --release
cp target/release/innocody-lsp python_binding_and_cmdline/innocody/bin/
pip install -e python_binding_and_cmdline/
```






