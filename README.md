This repo consists Innocody WebUI for hosting of code models, that you can later use inside Innocody plugins for code completion and chat.

### Running Innocody Server in a Docker Container

1. Build base image 
```
docker build -t innocody-base -f Dockerfile.base .
```

2. Build servẻ image 
```
docker build -t innocody-server .
```

3. Run the image 
```
docker run --rm --init -p 8008:8008 -v perm-storage:/perm_storage innocody-server
```

`perm-storage` is a volume that is mounted inside the container. All the configuration files, downloaded weights and logs are stored here.

To upgrade the docker, delete it using `docker kill XXX` (the volume `perm-storage` will retain your
data)

Now you can visit http://127.0.0.1:8008 to see the server Web GUI.

### Setting Up Plugins

Download Innocody for [VS Code](https://marketplace.visualstudio.com/items?itemName=innotechvn.innocody).

Go to plugin settings and set up a custom inference URL `http://127.0.0.1:8008`

## Custom installation

You can also install innocody repo without docker:

```
pip install -e .
INNOCODY_PERM_DIR="./database" python -m self_hosting_machinery.watchdog.docker_watchdog
```