FROM python:3.11-slim

ENV INSTALL_OPTIONAL=TRUE
ENV MAX_JOBS=8

RUN apt-get update && \
    apt-get install -y git curl iputils-ping wget default-jdk ruby ruby-dev build-essential libicu-dev cmake pkg-config libssh2-1-dev && \
    gem install bundler

RUN git clone https://github.com/smallcloudai/linguist.git /tmp/linguist \
    && cd /tmp/linguist \
    && bundle install \
    && rake build_gem

ENV PATH="${PATH}:/tmp/linguist/bin"
ENV CUDA_HOME=/usr/local/cuda

RUN pip install --no-cache-dir torch==2.7.0 --index-url https://download.pytorch.org/whl/cpu && \
    pip install xformers ninja packaging==24.1 setuptools==70.0.0 setuptools-scm==8.1.0 cython
