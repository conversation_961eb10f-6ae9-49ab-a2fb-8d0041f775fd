<div class="pane">
  <h2>Gated Models Token</h2>
  <div class="settings-info">Token is used to download gated (need authentication to download) models from Hugginface Hub. Obtain necessary token at Hugginface, accept terms and conditions if any and you can be able to add gated model at <span class="settings-tab-model-link fake-link" data-tab="model-hosting">Model Hosting</span> page.</div>
  <div class="mt-3 mb-3">
    <label for="huggingface_api_key" class="form-label">Huggingface API Token</label>
    <input type="text" name="huggingface_api_key" value="" class="form-control" id="huggingface_api_key">
  </div>
</div>

<div class="pane">
  <h2>Private Git Repositories keys</h2>
  <div class="settings-info">Generate new SSH key and open your <a href="https://github.com/settings/keys" target="_blank">Github Settings</a>. Click "New SSH key" button, and paste generated SSH key. "Key Name" serves to differentiate one key from another.</div>
  <div class="settings-row">
    <button data-bs-toggle="modal" data-bs-target="#settings-tab-ssh-modal" type="button" id="settings-new" class="btn btn-primary">Generate new SSH Key</button>
    <div class="mt-2 settings-all-keys"><br><br>No keys created yet.</div>
  </div>
</div>

<div class="modal fade" id="settings-tab-ssh-modal" tabindex="-1" aria-labelledby="settings-tab-ssh-modal" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">Add SSH Key</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <div class="mb-3">
          <label class="form-label">Key Name</label>
          <input type="text" id="tab-settings-key-title-input" class="form-control">
        </div>
        <div class="mb-3 d-none tab-settings-ssh-keywrap">
          <label class="form-label">Your SSH Key</label>
          <div class="tab-settings-ssh">
            <code></code>
          </div>
          <div class="form-text">
            Please copy and paste this Key into your Git repository settings.<br><b>This key will be showed only
              once!</b>
          </div>
        </div>
        <div id="status-ssh" class="uploaded-status"></div>
        <div class="modal-footer">
          <button type="button" class="btn settings-tab-ssh-close btn-secondary d-none" data-bs-dismiss="modal">Close</button>
          <button class="settings-tab-ssh-submit btn btn-primary">Add SSH Key</button>
        </div>
      </div>
    </div>
  </div>
</div>