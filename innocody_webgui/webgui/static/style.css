@font-face {
    font-family: 'Manrope';
    src: url('assets/fonts/Manrope-Regular.woff2') format('woff2');
    font-weight: 400;
    font-style: normal;
}

@font-face {
    font-family: 'Manrope';
    src: url('assets/fonts/Manrope-Bold.woff2') format('woff2');
    font-weight: 700;
    font-style: normal;
}
@font-face {
    font-family: 'Azeret Mono';
    src: url('assets/fonts/AzeretMono-Regular.woff2') format('woff2');
    font-weight: 400;
    font-style: normal;
}

@font-face {
    font-family: 'Azeret Mono';
    src: url('assets/fonts/AzeretMono-Bold.woff2') format('woff2');
    font-weight: 700;
    font-style: normal;
}

html {
    min-height: 100%;
}
body {
    font-family: "Manrope", sans-serif;
    font-size: .94rem;
    background-image: linear-gradient(120deg, #fdfbfb 0%, #e<PERSON>ee 100%);
    min-height: 100%;
}
code {
    font-family: "Azeret Mono", monospace;
}
.navbar-brand {
    display: flex;
    align-items: center;
}
.navbar-brand svg {
    width: 132px;
    height: auto;
}
.pane {
    background-color: #fff;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    border: 1px solid rgba(0,0,0,0.15);
    position: relative;
}
.temp-disabled {
    opacity: 0.2;
    pointer-events: none !important;
    position: relative;
    z-index: 0;
}
.temp-info {
    text-transform: uppercase;
    font-weight: 600;
    letter-spacing: 1px;
    position: absolute;
    border: 3px solid #999;
    color: #999;
    padding: 10px;
    border-radius: 5px;
    transform: rotate(-15deg);
}
.temp-info-modal {
    text-transform: uppercase;
    font-weight: 600;
    letter-spacing: 1px;
    position: absolute;
    border: 3px solid #999;
    color: #999;
    padding: 10px;
    border-radius: 5px;
    transform: rotate(-15deg);
}
.navbar {
    border-bottom: 1px solid rgba(0,0,0,0.15);
    background-color: #fff;
    padding: 15px 0;
}
.navbar-nav {
    width: 100%;
}
.header-tabs {
    display: flex;
    align-items: center;
    width: 100%;
}
.header-tabs a {
    margin-right: 30px;
}
.header-tabs ul {
    width: 100%;
}
h2 {
    margin-top: 10px;
}
.log-container {
    margin-top: 15px;
    height: 300px;
    overflow-y: scroll;
    border: 1px solid #ccc;
    padding: 10px;
    font-family: monospace;
    overflow-x: auto;
}
.log-container p {
    margin-bottom: 0;
}

.table-models tr:hover {
    cursor: pointer;
}
.table-models tbody td:first-child {
    min-width: 300px;
    overflow: hidden;
    white-space: nowrap;
}
.table-models thead th:nth-child(2),
.table-models tbody th:nth-child(2) {
    width: 90px;
    overflow: hidden;
    white-space: nowrap;
}

.upload-tab-table-files tbody td,
.upload-tab-table-files thead th,
.upload-tab-table-files tbody td,
.upload-tab-table-files thead th {
    text-align: center;
}
.upload-tab-table-files thead th:first-child {
    min-width: 300px;
    overflow: hidden;
    white-space: nowrap;
    text-align: left;
}
.upload-tab-table-files tbody td:first-child {
    min-width: 300px;
    overflow: hidden;
    white-space: nowrap;
    text-align: left;
}
.upload-tab-table-types thead th:first-child {
    min-width: 100px;
    overflow: hidden;
    white-space: nowrap;
    text-align: left;
}
.upload-tab-table-types tbody td:first-child {
    min-width: 100px;
    overflow: hidden;
    white-space: nowrap;
    text-align: left;
}
.store-input {
    width: 70px;
    margin: 0 10px;
}
.store-block {
    display: flex;
    align-items: center;
}
.fine-gfx {
    width: 100% !important;
}
.run-table tr {
    cursor: pointer;
}
.table-types thead th {
    position: sticky;
    top: 0;
    background-color: #fff;
}
.run-table-wrapper {
    max-height: 500px;
    overflow: auto;
}
.uploaded-total {
    margin-top: 15px;
    font-size: 0.8rem;
    opacity: 0.4;
}
.uploaded-status {
    font-size: 0.8rem;
    opacity: 0.7;
    font-weight: bold;
    margin-bottom: 15px;
}
.finetune-spinner {
    margin-right: 5px;
    width: 10px;
    height: 10px;
}
.table-types tr td:first-of-type {
    width: 50%;
}
.upload-spinner {
    margin-right: 5px;
}
.table-models td:nth-child(2),
.table-models td:nth-child(3),
.table-models th:nth-child(2),
.table-models th:nth-child(3) {
    text-align: center;
}

.table-gpus td:nth-child(2),
.table-gpus td:nth-child(3),
.table-gpus th:nth-child(2),
.table-gpus th:nth-child(3) {
    text-align: center;
}
h3 {
    font-size: 1.3rem;
    font-weight: bold;
}
.file-status {
    font-weight: normal;
    width: 100px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    margin-right: 5px;
}
.upload-tab-table-files td:nth-child(2),
.upload-tab-table-files th:nth-child(2) {
    width: 100px;
}

.tooltip-inner {
    white-space:pre;
    max-width:none;
    text-align: left;
}
.cpu-pane {
    margin-bottom: 20px;
    min-width: 207.66px;
    max-width: 207.66px;
}
.gpus-pane {
    margin-bottom: 20px;
}
.gpus-list {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr 1fr 1fr 1fr;
    grid-gap: 10px;
}
@media (min-width: 576px) and (max-width: 767.98px) {
    .gpus-list  {
        grid-template-columns: 1fr 1fr;
    }
}
@media (min-width: 768px) and (max-width: 991.98px) {
    .gpus-list  {
        grid-template-columns: 1fr 1fr 1fr;
    }
}

@media (min-width: 992px) and (max-width: 1199.98px) {
    .gpus-list  {
        grid-template-columns: 1fr 1fr 1fr 1fr;
    }
}
.device-item {
    display: flex;
    justify-content: flex-start;
    text-align: center;
    border: 1px solid rgba(0,0,0,0.15);
    border-radius: 8px;
    padding: 15px 10px;
    background-color: #fff;
    width: 100%;
    overflow: hidden;
    flex-direction: column;
}
.device-title {
    font-size: 0.8rem;
    text-align: left;
    margin-bottom: 10px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    padding-bottom: 5px;
}
.device-current-status {
    padding: 5px;
    background-color: #efefef;
    color: #000;
    display: flex;
    width: 100%;
    justify-content: flex-start;
    font-size: 10px;
    font-weight: bold;
    border-radius: 4px;
    margin-top: 10px;
}
.device-content {
}
.device-mem {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    font-size: 12px;
    margin-bottom: 5px;
    margin-top: 5px;
}
.device-mem b {
    margin-right: 5px;
    width: 60px;
    text-align: left;
    display: inline-flex;
    margin-top: 2px;
}
.device-mem-bar {
    height: 10px;
    width: 100px;
    background-color: #efefef;
    position: relative;
    display: inline-flex;
}
.device-mem-bar span {
    background-color: rosybrown;
    position: absolute;
    left: 0;
    top: 0;
    height: 10px;
}
.device-mem-wrap {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}
.device-command {
    text-align: left;
    font-size: 12px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}
.device-status {
    display: none;
    position: absolute;
    padding: 10px;
    border: 1px solid #efefef;
    background-color: #fff;
    box-shadow: 0 0 10px rgba(0,0,0,0.1);
    width: auto;
    white-space: wrap;
    margin-top: 10px;
    z-index: 999;
}
.device-status-visible {
    display: block;
}
.device-status-invisible {
    opacity: 0;
}
.device-command b {
    margin-right: 5px;
    width: 60px;
    text-align: left;
    display: inline-flex;
}
.device-temp {
    text-align: left;
    font-size: 12px;
    align-items: center;
    display: flex;
}
.device-temp b {
    margin-right: 5px;
    width: 60px;
    text-align: left;
    display: inline-flex;
}
.devices-cpu {
    margin-bottom: 15px;
    height: 30px;
    width: 60px;
    background-position: center;
    background-size: contain;
    background-repeat: no-repeat;
    background-image: url('data:image/png;base64,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');
}
.devices-card {
    margin-bottom: 15px;
    height: 30px;
    width: 60px;
    background-position: center;
    background-size: contain;
    background-repeat: no-repeat;
    background-image: url('data:image/png;base64,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');
}
.lora-group {
    margin-top: 10px;
    margin-bottom: 15px;
}
.settings-add {
    display: flex;
    width: 100%;
    justify-content: flex-end;
    margin-bottom: 20px;
}
.tab-settings-ssh-key-item {
    margin-bottom: 20px;
    border: 1px solid #ccc;
    border-radius: 8px;
    padding: 20px;
    display: flex;
    align-items: center;
}
.tab-settings-ssh-key-created {
    opacity: 0.6;
}
.tab-settings-ssh-key-name {
    display: flex;
    align-items: center;
}
.tab-settings-ssh-key-name i {
    margin-right: 5px;
}
.settings-row {
    margin-top: 30px;
}
.tab-settings-ssh-key-delete {
    margin-left: auto;
}
.tab-settings-ssh {
    padding: 10px;
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    margin: 10px 0;
}
.tab-settings-ssh code:after {
    content: close-quote;
}
.table-checkpoints tr {
}
.tab-files-process-now {
    margin-left: auto;
}
.run-pane{
    margin-top: 2rem;
}
.sources-run-button {
    font-weight: bold;
    position: relative;
}
.sources-run-button i,
.sources-run-button span {
    margin-right: 5px;
}
.sources-buttons {
    display: flex;
    align-items: center;
}
.sources-buttons .btn:not(:last-child) {
    margin-right: 5px;
}
.sources-list {
    font-size: 1.2rem;
    padding-left: 20px;
    line-height: 2;
}
.sources-list li i {
    margin-left: 10px;
}
.sources-list-active {
    font-weight: bold;
}
.source-files-table {
    align-items: stretch;
}
.sources-stats {
    margin-top: 20px;
    margin-right: auto;
}
.source-force {
    display: flex;
    flex-direction: column;
}
.upload-tab-table-type-body tr td:first-child {
    width: 20px;
}

.pane-disabled {
    pointer-events: none;
    opacity: 0.5;
    z-index: none;
}
.source-info {
    position: absolute;
    margin-top: 1px;
    display: inline-block;
    margin-left: 5px;
    cursor: pointer;
}
/* .nav-link.active {
    font-weight: bold;
    color: #0d6efd !important;
} */
.source-popup {
    display: none;
    position: absolute;
    padding: 10px;
    border: 1px solid #dee2e6;
    background-color: #fff;
    box-shadow: 0 0 2px 0 rgba(0, 0, 0, 0.2);
    text-align: left;
    z-index: 999;
    margin-top: -25px;
    margin-left: 236px;
}

.run-rename-popup {
    display: none;
    position: absolute;
    padding: 4px 10px 4px;
    border: 1px solid #dee2e6;
    background-color: #fff;
    box-shadow: 0 0 2px 0 rgba(0, 0, 0, 0.2);
    /*text-align: left;*/
    z-index: 999;
    margin-top: -25px;
    margin-left: 100px;
    align-items: center;
}
.run-rename-popup pre {
    margin: 0;
}
.run-rename {
    padding-left: 4px !important;
}

#upload-tab-table-body-files td:nth-child(3) {
    text-align: right;
    padding-right: 15px;
}
.main-tab-pane {
    position: relative;
}
.main-tab-content .main-tab-pane {
    display: none;
  }

.main-tab-content .main-tab-pane.main-active {
    display: block;
}

#nav-container .main-active {
    font-weight: bold;
    color: #0d6efd !important;
}
.chat-enabler {
    margin-bottom: 20px;
}
.chat-enabler-status {
    margin-top: 15px;
}
.fake-link {
    color: #0d6efd;
    cursor: pointer;
}
.sources-stats-db {
    display: none;
}
.sources-stats-finetune {
    display: none;
    flex-direction: column;
    margin-left: auto;
}
.sources-stats-finetune a i {
    margin-right: 5px;
}
.sources-stats-finetune h6,
.sources-stats-db h6 {
    margin-bottom: 4px;
    font-weight: bold;
}
.sources-stats-db {
    margin-top: 10px;
}
.run-pane-grid {
    display: flex;
    align-items: center;
    flex-direction: column;
    width: 100%;
}
.run-pane-col {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
}
.ftf-stats {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: flex-start;
    line-height: 1.2;
    padding: 0;
    margin: 0;
    font-size: 13px;
    margin-top: 7px;
}
.ftf-stats h6 {
    margin: 0;
    margin-right: 10px;
    font-weight: bold;
    font-size: 13px;
    padding: 0;
}
.ftf-status {
    font-weight: normal;
}
.ftf-status span {
    font-weight: bold;
    margin-left: 5px;
}
.ftf-stats div:first-of-type {
    margin-right: 10px;
}
.ftf-error {
}
.ftf-error span {
    margin-left: 5px;
}
.ftf-progress {
    width: 100%;
}
.ftf-eta {
    opacity: 0.5;
    font-size: 12px;
    font-weight: bold;
    margin-bottom: 5px;
}
.ftune-progress {
    width: 100%;
}
.ftune-eta {
    opacity: 0.5;
    font-size: 12px;
    font-weight: bold;
    margin-bottom: 5px;
}
.upload-tab-table-count-sort,
.upload-tab-table-mime-sort
 {
    cursor: pointer;
}
.finetune-settings-optional-disabled {
    opacity: 0.4;
    pointer-events: none;
}
.form-clear-default {
    position: absolute;
    right: 5px;
    margin-top: -27px;
    border: 0 !important;
    background: transparent !important;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0.6;
}
.form-clear-default:hover {
    opacity: 1;
}
.mb-3 {
    position: relative;
}
.finetune-settings-error {
    color: darkred;
    padding: 10px 0;
    font-weight: bold;
    margin-top: 15px;
}
.model-hosting-controls {
    margin-top: 5px;
    display: flex;
    justify-content: space-between;
}
.model-hosting-error {
    color: darkred;
    text-align: right;
}
.model-memory-error {
    color: darkred;
    text-align: right;
}
.disabled-group {
    opacity: 0.4;
    pointer-events: none;
}
.start-funetune__inline {
    display: grid;
    grid-template-columns: 1fr 1fr;
    grid-gap: 10px;
    margin-bottom: 10px;
}
.tab-finetune-run-now {
    font-weight: bold;
}
.tab-finetune-run-now i {
    margin-right: 5px;
}
.finetune-autorun i {
    margin-right: 5px;
}
.start-funetune-step1 {
    margin-top: 25px;
}
.start-funetune-step2 {
    margin-top: 25px;
}
.start-funetune-select-model h6,
.start-funetune-step1 h6,
.start-funetune-step2 h6 {
    font-weight: bold;
}
.callout {
    padding: 1rem;
    margin-top: 1rem;
    margin-bottom: 1rem;
    border: 1px solid #e9ecef;
    border-left-width: 0.25rem;
    border-radius: 0.25rem;
    border-left-color: #5bc0de;
}
.form-control::-webkit-input-placeholder {
    color: #dddddd !important;
}
.form-control::placeholder {
    color: #dddddd !important;
}
.form-control:-ms-input-placeholder {
    color: #dddddd !important;
}
.form-control::-ms-input-placeholder {
    color: #dddddd !important;
}
.btn-hover {
    opacity: 0 !important;
    pointer-events: none;
}
.run-table tr:hover .btn-hover {
    opacity: 1 !important;
    pointer-events: auto;
    z-index: 44;
}
.run-table-name {
    position: relative;
}
.run-table-name span {
    opacity: 0.35;
    font-size: 10px;
    display: block;
    position: absolute;
    margin-top: -6px;
    font-weight: bold;
}

.model-finetune-item {
    position: relative;
    display: flex;
    margin-bottom: 5px;
    align-items: center;
    width: 70%;
    background-color: #EEEEEEFF; /* or #f7f7f9 */
    border-radius: 5px;
    padding: 5px;
    overflow: hidden;
}

.model-finetune-item-inner {
    display: flex;
    flex-direction: column;
    width: 100%;
}

.model-finetune-item-upper-row {
    display: flex;
    flex-direction: row
}

.btn-remove-run {
    border: none;
    padding: 0;
    margin-right: 4px;
    font-size: 1rem;
}

.add-finetune-btn {
    padding: 0 5px;
}

.model-finetune-item-run {
    /*opacity: 0.35;*/
    font-size: 15px;
    display: block;
    /*margin-top: -6px;*/
    font-weight: bold;
}

.tech-msg {
    opacity: 0.35;
    font-size: 1rem;
    display: block;
    margin: 0;
    font-weight: bold;
    width: 100%;

}

.dropdown-panel-add-finetune {
    display: none;
    position: absolute;
    z-index: 1;
    background-color: #f9f9f9;
    min-width: 240px;
    box-shadow: 0 8px 16px 0 rgba(0,0,0,0.2);
    padding: 12px 16px;
    cursor: pointer;
}
.model-finetune-item-checkpoint {
    opacity: 0.35;
    font-size: 10px;
    display: block;
    margin-top: -3px;
    font-weight: bold;
    width: 100%;
}

.table-checkpoints tr:hover .btn-hover {
    opacity: 1 !important;
    pointer-events: auto;
}
/* .table-checkpoints tr td:nth-of-type(1) {
    width: 30px;
}
.table-checkpoints tr td:nth-of-type(2) {
    width: 30px;
} */
.use-model-pane {
    border: 1px solid rgba(0,0,0,0.15);
}
.animate-pane {
    animation: border_animate 1s ease-in;
}

@keyframes border_animate {
    0%{
        border: 1px solid rgba(0,0,0,0.15);
        background-color: #fff;
    }
    20%{
        border: 1px solid orangered;
        background-color: rgb(253, 255, 226);
    }
    50%{
        border: 1px solid orange;
        background-color: rgb(253, 255, 226);
    }
    100%{
        border: 1px solid rgba(0,0,0,0.15);
        background-color: #fff;
    }
}
.animate-pane .lora-row-fill {
    animation: move_animate 1s ease-in;
}
.lora-row {
    position: relative;
    overflow: hidden;
}
.lora-row-fill {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgb(253,255,226);
    background: linear-gradient(90deg, rgba(253,255,226,1) 0%, rgba(220,230,160,1) 51%, rgba(253,255,226,1) 100%);
    transform: translateX(-100%);
}

@keyframes move_animate {
    0%{
        transform: translateX(-100%);
    }
    50%{
        transform: translateX(0);
    }
    100%{
        transform: translateX(100%);
    }
}

/* DASHBOARD */
.dash-main-div {
    display: grid;
    place-items: center;
    margin-top: 70px;
    margin-bottom: 100px;
}

.dash-nav-item:active {
    color: #0d6efd;
    font-weight: bold;
}

.dash-plot-btn {
    margin-right: 5px;
    padding: 1.5px 3.5px 1.5px 3.5px;
}
.dusers-teams-table td, .dusers-teams-table th {
    padding: 5px;
}

.dash-plot-md {
    height: 400px;
    width: 1000px;
}

.dash-plot-sm {
    height: 400px;
    width: 800px;
}
.model-span {
    display: inline-flex;
    align-items: center;
}
.model-span::before {
    font-family: "bootstrap-icons";
    content: '\f4fd';
    margin-right: 5px;
    width: 14px;
    height: 14px;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 12px;
    color: #777;
}
.row-active .model-span::before {
    content: '\f2e9';
}
.modelsub-row {
    display: none;
}
.modelsub-row td {
    vertical-align: top;
}
.modelsub-name {
    display: inline-flex;
    flex-direction: column;
}
.modelsub-name div {
    display: block;
}
.modelsub-info {
    opacity: 0.7;
    display: block;
    font-size: 11px;
    margin-top: 3px;
}
.modelsub-info span {
    color: rgb(13, 110, 253);
    cursor: pointer;
}

.modelsub-row td:first-child {
    padding-left: 25px;
}

.modelsub-row.active {
    display: table-row;
}

.badge-square {
    display: inline-block;
    font-size: 9px;
    font-weight: 600;
    padding: 2px 4px;
    border: 1px solid #0b161a;
    min-width: 10px;
    line-height: 1;
    color: #0b161a;
    text-align: left;
    white-space: nowrap;
    /*justify-content: center;*/
    background-color: transparent;
}

.badge-square.solid {
    background-color: #0b161a;
    color: #fff;
}

.badge-square.danger {
    border-color: #dc3545;
    color: #dc3545;
}

.badge-square.solid.danger {
    border-color: #dc3545;
    background-color: #dc3545;
    color: #fff;
}

.badge-square.secondary {
    border-color: #6c757d;
    color: #6c757d;
}

.badge-square.solid.secondary {
    border-color: #6c757d;
    background-color: #6c757d;
    color: #fff;
}

.badge-square.success {
    border-color: #1a8754;
    color: #1a8754;
}

.badge-square.success.solid {
    border-color: #1a8754;
    background-color: #1a8754;
    color: #fff;
}

.badge-square.warning {
    border-color: #ffc107;
    color: #ffc107;
}

.badge-square.solid.warning {
    border-color: #ffc107;
    background-color: #ffc107;
    color: #fff;
}

.ft-status-pill-div {
    margin: 0;
    padding: 0;
    display: inline-flex;
    justify-content: left;
}

.run-table-row td {
    vertical-align: middle;
}

.finetune-settings-inline {
    display: grid;
    grid-template-columns: 1fr 3fr;
    align-items: center;
}
.finetune-settings-inline:not(:last-child) {
    margin-bottom: 15px;
}
.finetune-settings-options {
    border: 1px solid #5bc0de;
    display: grid;
    grid-template-columns: 3fr 1fr;
    column-gap: 20px;
    align-items: flex-start;
    padding: 15px;
    border-radius: var(--bs-border-radius);
}
.finetune-settings-options div {
    display: grid;
    grid-template-columns: 1fr 1fr;
    column-gap: 20px;
    row-gap: 10px;
    align-items: center;
}
.finetune-settings-inline-low {
    display: flex;
    align-items: center;
}
.finetune-settings-inline-low input {
    margin-right: 5px;
    margin-top: 0;
}
.finetune-settings-inline-gpus {
    margin-top: 20px;
    display: flex;
    align-items: center;
}
.finetune-settings-inline-gpus > label {
    margin-right: 10px;
}
.delete-project {
    width: 140px;
    margin-top: auto !important;
    margin-left: auto;
}
.source-files-table {
    min-height: 350px;
}
.users-table-license {
    display: flex;
    align-items: center;
}
.users-table-license button {
    margin-top: 0;
    margin-right: 10px;
}
.stop-finetune {
    margin-left: 15px;
}


/* Toolbox Tab */

.dropdown-menu li {
    padding: 0 10px;
}

.toolbox-editor {
    width: 100%;
    min-height: 600px;
    margin-bottom: 20px;
    max-height: 700px;
    overflow: auto;
    padding: 0;
}
.toolbox-editor pre code {
    font-family: Menlo, Monaco, Consolas, monospace;
}
.toolbox-editor pre {
    margin: 0;
}
.toolbox-editor pre code {
    font-family: Menlo, Monaco, Consolas, monospace;
}
.toolbox-editor pre {
    margin: 0;
}

.toolbox-error {
    border: 2px solid red;
    padding: 5px 10px;
    border-radius: 5px;
    color: red;
    margin-bottom: 10px;
    font-weight: bold;
}
.toolbox-error i {
    margin-right: 5px;
}
.error-line {
    background-color: #ff8c97;
}
.settings-tab-customization-submit {
    padding-left: 40px !important;
    padding-right: 40px !important;
}
.settings-tab-customization-badge {
    font-size: 14px;
    position: absolute;
    margin-left: 5px;
}
.finetune-new-project {
    margin-top: -15px;
    color: #0d6efd;
    font-weight: bold;
    margin-bottom: 20px;
    cursor: pointer;
}

.projects-dropdown li:last-child button {
    border-top: 1px solid #ccc;
}
.projects-dropdown li:only-child button {
    border-top: none;
}
.start-finetune-stats {
    display: flex;
    align-items: flex-start;
}
.start-finetune-wrap {
    width: calc(100% - 100px);
}
.stop-finetune {
    height: 28px;
}
#checkpoints table tbody tr td:first-child {
    width: 50px;
}
.download-lora-modal-inline {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
}
.download-lora-modal-inline .btn {
    width: 270px !important;
    min-width: 270px !important;
    display: block !important;
}
.download-lora-modal-inline div {
    opacity: 0.8;
    margin-left: 20px;
    width: 100%;
}
#download-lora-modal {
    --bs-modal-width: 600px;
}
.team-model-dropdown {
    padding-left: 0;
    padding-top: 0;
    font-size: 0.8rem;
    text-align: left;
    display: flex;
    align-items: center;
    max-width: 420px !important;
    overflow: hidden;
}
.team-model-dropdown:active {
    border-color: transparent !important;
}
.deprecated-badge {
    margin-left: 3px;
    background-color: #999 !important;
    color: #fff !important;
    opacity: 0.7;
    font-size: 9px;
    font-family: monospace;
    text-transform: uppercase;
    line-height: 9px;
    font-weight: 400;
    padding: 4px 5px 3px 5px !important;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}
.repo-badge {
    margin-left: 3px;
    color: #7877a8 !important;
    display: inline;
}
.default-context {
    /* padding: .25rem .5rem; */
    padding-left: .5rem;
    font-size: .875rem;
    /* border-radius: var(--bs-border-radius-sm); */
    display: inline-flex;
    /* background-color: #eee; */
}
.settings-info {
    opacity: 0.7;
    margin-top: 8px;
    margin-bottom: 8px;
    font-size: 13px;
    max-width: 600px;
}

/* Model Weights */
.model-weights-info {
    display: flex;
    align-items: center;
    gap: 8px;
}
.model-weights-info i {
    display: inline-flex;
    margin: 0;  
}

.model-weights-status {
    display: inline-flex;
    align-items: center;
}
.model-weights-button {
    border: 0;
    border-radius: 3px !important;
    display: flex;
    align-items: center;
}
.model-weights-button i {
    margin-right: 3px !important;
}
.weights-modal-code {
    border: 1px solid #ccc;
    border-radius: 0.375rem;
    padding: 5px;
}
.weights-modal-code  {
    position: relative;
    background: #f5f5f5;
    border-radius: 4px;
    margin: 10px 0;
}
.weights-modal-code code {
    display: block;
    margin: 0;
    padding: 0;
}
.weights-modal-code pre {
    margin: 0;
    padding: 0;
}