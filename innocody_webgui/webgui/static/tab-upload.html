<div class="row">
  <div id="upload-files-modal"></div>

  <div class="col-12">
    <div class="pane sources-pane">
      <h3>Code and Text Sources</h3>
      <table class="table table-sm align-middle upload-tab-table-files source-files-table-itself">
        <thead>
          <tr>
            <th>Filename</th>
            <th></th>
            <th></th>
            <th></th>
            <th></th>
          </tr>
        </thead>
        <tbody id="upload-tab-table-body-files">
          <tr><td>No sources added.</td><td></td><td></td><td></td><td></td></tr>
        </tbody>
      </table>
      <div class="sources-buttons">
        <button id="upload-tab-git-button" data-bs-toggle="modal" data-bs-target="#upload-tab-git-modal"
          type="button" class="btn btn-primary btn-lg">Add git repo&mldr;</button>
        <button id="open-upload-files-modal" type="button" class="btn btn-primary">Upload files</button>
        <button id="tab_files_process_now" type="button" class="btn btn-success tab-files-process-now">Run "git pull", scan files</button>
      </div>
    </div>
    <div class="pane filetypes-pane">
      <h3>File Type Filter
        <div class="mt-3 d-grid gap-2 d-md-flex">
          <button class="btn btn-outline-secondary btn-sm filetypes-checkall">Check all</button>
          <button class="btn filetypes-uncheckall btn-outline-secondary btn-sm">Uncheck all</button>
        </div>
      </h3>
      <div class="row source-files-table">
        <div class="col-7">
          <table class="table table-striped align-middle upload-tab-table-types">
            <thead>
              <tr>
                <th></th>
                <th>MIME <i class="upload-tab-table-mime-sort bi bi-caret-up-fill"></i></th>
                <th>Count <i class="upload-tab-table-count-sort bi bi-caret-up-fill"></i></th>
              </tr>
            </thead>
            <tbody class="upload-tab-table-type-body">
              <tr><td>No scanned files.</td><td></td><td></td></tr>
            </tbody>
          </table>
        </div>
        <div class="col-5 source-force">
          <div class="mb-3">
            <label for="force_exclude" class="form-label">Force exclude from finetune</label>
            <input type="text" class="form-control" id="force_exclude" placeholder="generated/*,dist/*">
          </div>
          <div class="mb-3">
            <label for="force_include" class="form-label">Force include in finetune</label>
            <input type="text" class="form-control" id="force_include" placeholder="src/strange_file.py">
          </div>
          <div class="sources-stats">
            <div class="sources-stats-finetune">
              <h6>Files for Finetune</h6>
              <div class="sources-stats-fine-accepted">Accepted: <span></span></div>
              <div class="sources-stats-fine-rejected">Rejected: <span></span></div>
            </div>
            <div class="sources-stats-db">
              <h6>Files for Vector DB</h6>
              <div class="sources-stats-db-accepted">Accepted: <span></span></div>
              <div class="sources-stats-db-rejected">Rejected: <span></span></div>
            </div>
            <button class="mt-3 btn-primary btn proceed-finetune">Proceed to Finetune</button>
          </div>
          <button class="mt-3 btn-danger btn delete-project">Delete Project</button>
        </div>
      </div>
    </div>
  </div>

  <div class="modal fade" id="upload-tab-git-modal" tabindex="-1" aria-labelledby="upload-tab-git-modal"
    aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="gitModalLabel">Add Git Repository</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <div class="mb-3 form-text ssh-info">
            For public repositories, a https link works best:
            <code>https://github.com/my_company/my_repo</code>
          </div>
          <div class="mb-3 form-text ssh-info">
            For private repositories, <span class="ssh-link main-tab-button fake-link" data-tab="settings">add SSH Key</span> and use a link like this:
            <code>**************:my_company/my_repo.git</code>
          </div>
          <div class="mb-3">
            <label class="form-label">Repository URL</label>
            <input placeholder="https://github.com/my_company/my_repo" type="url" id="tab-upload-git-input"
              class="form-control">
          </div>
          <div class="mb-3 ssh-selector d-none">
            <label class="form-label">SSH Key</label>
            <select class="form-select ssh-key-select">
            </select>
          </div>
          <div class="mb-3">
            <label class="form-label">Branch</label>
            <input placeholder="main" type="text" id="tab-upload-git-brach-input" class="form-control">
            <div class="form-text">
              Optional. If not specified, the default branch will be used.
            </div>
          </div>
          <div id="status-git" class="uploaded-status"></div>
          <button class="tab-upload-git-submit btn btn-primary">Submit</button>
        </div>
      </div>
    </div>
  </div>

  <div class="modal fade" id="delete-modal" tabindex="-1" aria-labelledby="delete-modal" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="urlModalLabel">Delete source</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          You are about to delete the source. This cannot be undone, and your sources will be scanned again.
        </div>
        <div id="status-delete" class="deleted-status"></div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
          <button type="button" class="delete-modal-submit submit btn btn-primary">Delete</button>
        </div>
      </div>
    </div>
  </div>

</div>
