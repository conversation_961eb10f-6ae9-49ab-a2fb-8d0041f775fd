<div class="cpu-pane">
</div>
<div class="gpus-pane">
  <div class="gpus-list">
  </div>
</div>
<div class="cpu-pane">
  <label for="batch-size-input" class="form-label fw-bold">Batch Size</label>
  <div class="d-flex mb-3">
    <input 
      type="number" 
      class="form-control me-2 flex-grow-1" 
      id="batch-size-input" 
      placeholder="Enter batch size..." 
      min="1"
    >
    <button 
      class="btn btn-primary" 
      id="set-batch-size-btn" 
      type="button"
    >
      Set
    </button>
  </div>
  <div id="batch-size-message" class="form-text text-muted"></div>
</div>

<div class="pane">
  <h3>Hosted Models</h3>
  <table class="table align-middle table-assigned-models">
    <thead>
      <tr>
        <th>Model</th>
        <th>Context</th>
        <th>Finetune</th>
        <th>Sharding</th>
        <th>Share GPU</th>
        <th></th>
      </tr>
    </thead>
    <tbody>
      <tr><td>No models assigned.</td></tr>
    </tbody>
  </table>
  <div class="model-hosting-controls">
    <button data-bs-toggle="modal" data-bs-target="#add-model-modal" class="btn btn-primary model-hosting-add"><i class="bi bi-plus-lg"></i> Add Model</button>
    <div>
      <div class="model-hosting-error"><i class="bi bi-exclamation-triangle-fill"></i> You have more models selected than GPUs available.</div>
      <div class="model-memory-error"><i class="bi bi-exclamation-triangle-fill"></i> Required memory exceeds the GPU's memory.</div>
    </div>
  </div>
</div>

<div class="modal fade" id="add-model-modal" tabindex="-1" aria-labelledby="add-model-modal" aria-hidden="true">
  <div class="modal-dialog modal-xl modal-dialog-centered add-model-modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="urlModalLabel">Add Model</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <div class="table-responsive">
        <table class="table align-middle table-hover table-models">
          <thead>
            <tr>
              <th>Model</th>
              <th>Completion</th>
              <th>Finetune</th>
              <th>Chat</th>
              <th>Weights</th>
            </tr>
          </thead>
          <tbody>
            <tr><td>No models found.</td></tr>
          </tbody>
        </table>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
      </div>
    </div>
  </div>
</div>

<div class="modal fade" id="upload-weights-modal" tabindex="-1" aria-labelledby="upload-weights-modal" aria-hidden="true">
  <div class="modal-dialog modal-lg modal-dialog-centered upload-weights-modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="urlModalLabel">Upload Model Weights</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <div class="weights-modal-code">
        </div>
        <div class="weights-modal-info mt-3">
        </div>
        <div class="mt-4">
          <label for="model_weights" class="form-label fw-bold">Model Weights for <span></span></label>
          <input class="form-control" type="file" accept=".tar,.tar.gz,.tgz,.zip" name="model_weights" id="model_weights">
        </div>
        <div class="weights-progress">
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
        <button type="button" class="btn btn-primary weights-modal-submit">Upload</button>
      </div>
    </div>
  </div>
</div>