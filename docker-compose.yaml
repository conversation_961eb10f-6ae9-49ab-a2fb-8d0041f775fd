volumes:
  perm_storage:

networks:
  innocody_net:
    driver: bridge
    
services:
  innocody-server:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: innocody-server
    environment:
      - INNOCODY_PERM_DIR=/perm_storage
      - INNOCODY_TMP_DIR=/tmp
      - HF_HUB_OFFLINE=false
    ports:
      - "8008:8008"
      - "80:8008"
      - "443:8008"
    volumes:
      - perm_storage:/perm_storage
    networks:
      - innocody_net
