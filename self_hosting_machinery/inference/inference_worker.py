import os
import sys
import logging
import time
import signal
import socket
import traceback

from self_hosting_machinery.inference.stream_results import infserver_session
from self_hosting_machinery.inference.stream_results import validate_description_dict
from self_hosting_machinery.inference.stream_results import UploadProxy
from self_hosting_machinery.inference.stream_results import completions_wait_batch

from self_hosting_machinery.inference import InferenceHF, InferenceEmbeddings

from innocody_utils.scripts import env

from typing import Dict, Any


quit_flag = False
log = logging.getLogger("MODEL").info


def worker_loop(model_name: str, models_db: Dict[str, Any], compile: bool):
    if model_name not in models_db:
        log(f"STATUS not found {model_name}")
        if compile:
            return
        log("will sleep for 5 minutes and then exit, to slow down service restarts")
        wake_up_ts = time.time() + 300
        while time.time() < wake_up_ts and not quit_flag:
            time.sleep(1)
        raise RuntimeError(f"unknown model \"{model_name}\"")
    log("STATUS loading model")

    model_dict = models_db[model_name]
    # Skip compile stage for cloud backend (no local kernels to compile)
    if compile and model_dict.get("backend") == "cloud":
        log(f"STATUS skipping compile for cloud backend model {model_name}")
        return

    if "embeddings" in model_dict["filter_caps"]:
        inference_model = InferenceEmbeddings(
            model_name=model_name,
            model_dict=model_dict,
        )

        dummy_call = 16 * [{
            'id': 'emb-legit-42',
            'function': 'embeddings',
            'inputs': "A"*8000,   # max size validated at 9000 chars, 16 batch size
            'created': time.time(),
            'type': 'index'
        }] + \
        16 * [{
            'id': 'emb-legit-42',
            'function': 'embeddings',
            'inputs': "A"*8000,   # max size validated at 9000 chars, 16 batch size
            'created': time.time(),
            'type': 'search'
        }] 
    else:
        inference_model = InferenceHF(
            model_name=model_name,
            model_dict=model_dict,
            model_cfg=None,
        )

        dummy_call = {
            'temperature': 0.8,
            'top_p': 0.95,
            'max_tokens': 40,
            'id': 'comp-wkCX57Le8giP-1337',
            'object': 'text_completion_req',
            'function': 'completion',
            'echo': False,
            'stop_tokens': [],
            'prompt': 'Hello world',
            'created': time.time(),
        }

    class DummyUploadProxy:
        def upload_result(*args, **kwargs):
            pass
        def check_cancelled(*args, **kwargs):
            return set()


    
    log("STATUS serving %s" % model_name)
    req_session = infserver_session()
    cuda_visible_devices = os.environ.get("CUDA_VISIBLE_DEVICES", "").replace(",", "")
    
    B = 64
    description_dict = validate_description_dict(
        f'{model_name}_{socket.getfqdn()}_{cuda_visible_devices}',
        "account_name",
        # TODO: add UI support setting batch_size
        model=model_name, B=B, max_thinking_time=60,
    )
    
    upload_proxy = UploadProxy(upload_q=None, cancelled_q=None)
    upload_proxy.start_upload_result_daemon()

    while not quit_flag:
        upload_proxy.keepalive()
        upload_proxy.cancelled_reset()
        
        if os.path.exists(env.CONFIG_BATCH_UNSET):
            with open(env.CONFIG_BATCH_UNSET, "r") as f:
                B = int(f.read().strip())
            
            log(f"DETECT BATCHSIZE CHANGE !!! UPDATE BATCHSIZE FROM {description_dict['B']} -> {B}")
            description_dict["B"] = B
            
            os.rename(env.CONFIG_BATCH_UNSET, env.CONFIG_BATCH_SET)
        
        retcode, request_batch = completions_wait_batch(
            req_session, description_dict, verbose=False)
        ts_arrived = time.time()
        if retcode == "OK":
            upload_proxy_args = {
                "description_dict": description_dict,
                "original_batch": request_batch,
                "idx_updated": list(range(len(request_batch))),
                "tokens": None,
                "ts_arrived": ts_arrived,
                "ts_batch_started": 0,
                "ts_prompt": 0,
                "ts_first_token": 0,
                "ts_batch_finished": 0,
            }
            
            try:
                # inference_model.lora_switch_according_to_request(request.get("lora_config", None))
                # inference_model.lora_switch_according_to_request(None)
                inference_model.infer(request_batch, upload_proxy, upload_proxy_args)
            except Exception as e:
                log(f"inference failed with {e}")
                log(traceback.format_exc())
        elif retcode == "WAIT":
            # Normal, no requests
            pass
        else:
            # No connectivity, connection refused, other errors go there
            time.sleep(10)

    upload_proxy.stop()
    log("inference_worker.py clean shutdown")


def catch_sigkill(signum, frame):
    sys.stderr.write("caught SIGUSR1\n")
    sys.stderr.flush()
    global quit_flag
    quit_flag = True


if __name__ == "__main__":
    from argparse import ArgumentParser
    from innocody_known_models import models_mini_db

    parser = ArgumentParser()
    parser.add_argument("--model", type=str)
    parser.add_argument("--compile", action="store_true", help="download and compile triton kernels, quit")
    args = parser.parse_args()

    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s MODEL %(message)s',
        datefmt='%Y%m%d %H:%M:%S',
        handlers=[logging.StreamHandler(stream=sys.stderr)])

    signal.signal(signal.SIGUSR1, catch_sigkill)

    worker_loop(args.model, models_mini_db, compile=args.compile)
