import os
import time
import json
import logging

import torch
import traceback
from openai import OpenAI

from typing import Dict, Any

from sentence_transformers import SentenceTransformer

from innocody_utils.scripts import env
from innocody_utils.huggingface.utils import huggingface_hub_token
from self_hosting_machinery.inference import InferenceBase
from innocody_webgui.webgui.selfhost_fastapi_completions import QueryType

def log(*args):
    logging.getLogger("MODEL").info(*args)


class InferenceEmbeddings(InferenceBase):
    def __init__(
            self,
            model_name: str,
            model_dict: Dict[str, Any],
    ):
        self._model_name = model_name
        self._model_dict = model_dict
        self._model_dir = f"models--{self._model_dict['model_path'].replace('/', '--')}"

        if model_dict.get("cpu"):
            self._device = "cpu"
        else:
            self._device = "cuda:0"
        
        backend = model_dict.get("backend", "transformers")
        self._backend = "torch" if backend == "transformers" else backend
        
        self._model_kwargs = model_dict.get("model_class_kwargs", {})
        
        self._prompt = model_dict.get("prompt", None)

        log(f"loading model {self._model_name}")
        log(f"BACKEND: {self._backend}")
        
        if self._backend == "cloud":
            endpoint_id = model_dict["endpoint_id"]
            base_url = f"https://api.runpod.ai/v2/{endpoint_id}/openai/v1"
            
            api_key = model_dict.get("key")

            # 💡 Set up OpenAI client with base URL and API key
            self._model = OpenAI(
                base_url=base_url,
                api_key=api_key
            )
        else:
            self._model = SentenceTransformer(
                self._model_dict["model_path"],
                backend=self._backend,
                device=self._device,
                cache_folder=self.cache_dir,
                use_auth_token=huggingface_hub_token(),
                model_kwargs=self._model_kwargs
            )

    @property
    def model(self) -> torch.nn.Module:
        return self._model

    @property
    def model_name(self) -> str:
        return self._model_name

    @property
    def model_dict(self) -> Dict[str, Any]:
        return self._model_dict

    @property
    def cache_dir(self) -> str:
        return env.DIR_WEIGHTS

    def infer(self, batch_request: list[Dict[str, Any]], upload_proxy: Any, upload_proxy_args: Dict):        
        
        log(f"🔍 DEBUG: Starting infer with {len(batch_request)} requests, backend={self._backend}")
        
        count_batch = 0
        mean_length = 0
        
        batch_index = []
        batch_search = []
        ids = []
        
        for idx, _request in enumerate(batch_request):
            request_id = _request["id"]
            
            if request_id in upload_proxy.check_cancelled():
                log(f"🔍 DEBUG: Request {request_id} was cancelled")
                return
            
            location = None
            if _request["type"] == QueryType.INDEX.value:
                location = (_request["type"], len(batch_index))
                batch_index.append(_request["inputs"])
            elif _request["type"] == QueryType.SEARCH.value:
                location = (_request["type"], len(batch_search))
                batch_search.append(_request["inputs"])
            
            ids.append(location)
            
            count_batch += 1
            mean_length += len(_request["inputs"])
        
        mean_length /= count_batch
        
        log("embeddings count_batch=%d mean_length=%d" % (count_batch, mean_length))        
        log(f"🔍 DEBUG: batch_index={len(batch_index)}, batch_search={len(batch_search)}, backend={self._backend}")
        
        t0 = time.time()
        
        # Initialize result variables to prevent NameError
        batch_index_results = []
        batch_search_results = []
        
        try:
            if self._backend == "cloud":  
                log(f"🔍 DEBUG: Using cloud backend, about to call RunPod API")
                
                if batch_index:
                    log(f"🔍 DEBUG: Processing {len(batch_index)} INDEX requests")
                    resp = self._model.embeddings.create(  # lowercase "embeddings"
                        model=self._model_dict["model_path"],
                        input=batch_index
                    )
                    batch_index_results = [d.embedding for d in resp.data]
                    log(f"🔍 DEBUG: Got {len(batch_index_results)} INDEX results")

                if batch_search:
                    log(f"🔍 DEBUG: Processing {len(batch_search)} SEARCH requests")
                    inputs = [self._prompt + text for text in batch_search] if self._prompt else batch_search
                    resp = self._model.embeddings.create(
                        model=self._model_dict["model_path"],
                        input=inputs
                    )
                    batch_search_results = [d.embedding for d in resp.data]
                    log(f"🔍 DEBUG: Got {len(batch_search_results)} SEARCH results")
                
            else:
                if len(batch_index) > 0:
                    batch_index_results = self._model.encode(batch_index, show_progress_bar=True)
                
                if len(batch_search) > 0:
                    batch_search_results = self._model.encode(batch_search, prompt=self._prompt, show_progress_bar=True)
        
        except Exception as e:
            log(f"❌ ERROR in cloud embedding: {e}")
            log(f"❌ ERROR traceback: {traceback.format_exc()}")
            raise  # Re-raise để inference_worker có thể catch
        
        batch_results = []
        for true_idx, (req_type, idx) in enumerate(ids):
            if req_type == QueryType.INDEX.value:
                select_list = batch_index_results
            elif req_type == QueryType.SEARCH.value:
                select_list = batch_search_results
            
            import math      
            
            # Handle both cloud (list) and local (numpy array) results
            embedding_data = select_list[idx]
            if self._backend == "cloud":
                # Cloud API already returns lists
                embedding_list = embedding_data
            else:
                # Local models return numpy arrays, need .tolist()
                embedding_list = embedding_data.tolist()
            
            for val in embedding_list:
                if not math.isfinite(val):
                    log(f"Invalid float in embedding at index")
                    
                    batch_request
                    log(batch_request[true_idx]["inputs"])
                    log(select_list[idx])
                    break
            
            batch_results.append(select_list[idx])
        
        t1 = time.time()
        log("/embeddings %0.3fs" % (t1 - t0))
        
        upload_proxy_args["ts_batch_started"] = t0
        upload_proxy_args["ts_batch_finished"] = t1
        
        files = []
        finish_reasons = []
        for result in batch_results:
            
            # Handle both cloud (list) and local (numpy array) results
            if self._backend == "cloud":
                # Cloud API already returns lists
                result_list = result
            else:
                # Local models return numpy arrays, need .tolist()
                result_list = result.tolist()
            
            files.append(
                {
                    "results": json.dumps(result_list, allow_nan=False),
                }
            )
            finish_reasons.append("DONE")
        
        upload_proxy.upload_result(
            **upload_proxy_args,
            files=files,
            finish_reason=finish_reasons,
            generated_tokens_n=[0] * count_batch,
            more_toplevel_fields=[{}] * count_batch,
            status="completed",
        )
