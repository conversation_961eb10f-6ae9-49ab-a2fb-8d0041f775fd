#
# This file is autogenerated by pip-compile with Python 3.10
# by the following command:
#
#    pip-compile setup.py
#
aiofiles==24.1.0
    # via innopilot-self-hosting (setup.py)
aiohappyeyeballs==2.6.1
    # via aiohttp
aiohttp==3.12.9
    # via
    #   litellm
    #   innopilot-self-hosting (setup.py)
aiosignal==1.3.2
    # via aiohttp
annotated-types==0.7.0
    # via pydantic
anyio==4.9.0
    # via
    #   httpx
    #   openai
    #   starlette
async-timeout==5.0.1
    # via aiohttp
attrs==25.3.0
    # via
    #   aiohttp
    #   jsonschema
    #   referencing
certifi==2025.4.26
    # via
    #   httpcore
    #   httpx
    #   requests
cffi==1.17.1
    # via cryptography
charset-normalizer==3.4.2
    # via requests
click==8.2.1
    # via
    #   litellm
    #   uvicorn
cryptography==45.0.3
    # via innopilot-self-hosting (setup.py)
distro==1.9.0
    # via openai
exceptiongroup==1.3.0
    # via anyio
fastapi==0.115.2
    # via innopilot-self-hosting (setup.py)
filelock==3.18.0
    # via huggingface-hub
frozenlist==1.6.2
    # via
    #   aiohttp
    #   aiosignal
fsspec==2025.5.1
    # via huggingface-hub
giturlparse==0.12.0
    # via innopilot-self-hosting (setup.py)
h11==0.16.0
    # via
    #   httpcore
    #   uvicorn
hf-xet==1.1.3
    # via huggingface-hub
httpcore==1.0.9
    # via httpx
httpx==0.28.1
    # via
    #   litellm
    #   openai
huggingface-hub==0.32.4
    # via tokenizers
idna==3.10
    # via
    #   anyio
    #   httpx
    #   requests
    #   yarl
importlib-metadata==8.7.0
    # via litellm
jinja2==3.1.6
    # via litellm
jiter==0.10.0
    # via openai
jsonschema==4.24.0
    # via litellm
jsonschema-specifications==2025.4.1
    # via jsonschema
litellm==1.72.1
    # via innopilot-self-hosting (setup.py)
markupsafe==3.0.2
    # via jinja2
more-itertools==10.7.0
    # via innopilot-self-hosting (setup.py)
multidict==6.4.4
    # via
    #   aiohttp
    #   yarl
numpy==2.2.6
    # via pandas
openai==1.84.0
    # via litellm
packaging==25.0
    # via huggingface-hub
pandas==2.3.0
    # via innopilot-self-hosting (setup.py)
propcache==0.3.1
    # via
    #   aiohttp
    #   yarl
pycparser==2.22
    # via cffi
pydantic==2.11.5
    # via
    #   fastapi
    #   litellm
    #   openai
    #   innopilot-self-hosting (setup.py)
pydantic-core==2.33.2
    # via pydantic
python-dateutil==2.9.0.post0
    # via pandas
python-dotenv==1.1.0
    # via litellm
python-multipart==0.0.20
    # via innopilot-self-hosting (setup.py)
pytz==2025.2
    # via pandas
pyyaml==6.0.2
    # via huggingface-hub
referencing==0.36.2
    # via
    #   jsonschema
    #   jsonschema-specifications
regex==2024.11.6
    # via tiktoken
requests==2.32.3
    # via
    #   huggingface-hub
    #   tiktoken
rpds-py==0.25.1
    # via
    #   jsonschema
    #   referencing
scyllapy==1.3.0
    # via innopilot-self-hosting (setup.py)
six==1.17.0
    # via python-dateutil
sniffio==1.3.1
    # via
    #   anyio
    #   openai
starlette==0.37.2
    # via
    #   fastapi
    #   innopilot-self-hosting (setup.py)
termcolor==3.1.0
    # via innopilot-self-hosting (setup.py)
tiktoken==0.9.0
    # via litellm
tokenizers==0.21.1
    # via litellm
tqdm==4.67.1
    # via
    #   huggingface-hub
    #   openai
typing-extensions==4.14.0
    # via
    #   anyio
    #   exceptiongroup
    #   fastapi
    #   huggingface-hub
    #   multidict
    #   openai
    #   pydantic
    #   pydantic-core
    #   referencing
    #   typing-inspection
    #   uvicorn
typing-inspection==0.4.1
    # via pydantic
tzdata==2025.2
    # via pandas
urllib3==2.4.0
    # via requests
uvicorn==0.34.3
    # via innopilot-self-hosting (setup.py)
uvloop==0.21.0
    # via innopilot-self-hosting (setup.py)
yarl==1.20.0
    # via aiohttp
zipp==3.22.0
    # via importlib-metadata
