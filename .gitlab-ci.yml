workflow:
  auto_cancel:
    on_new_commit: interruptible

default:
  interruptible: true

stages:
  - test
  - build-dev
  - build-release
  - trigger-build-extension

variables:
  CARGO_TERM_COLOR: always
  RUST_BACKTRACE: full
  CROSS_CONTAINER_ENGINE_NO_BUILDKIT: 1

.cache_definition: &cache_definition
  cache:
    key:
      prefix: "cargo-${CI_PROJECT_NAME}-${CI_COMMIT_REF_SLUG}-${CI_JOB_NAME}-${CI_RUNNER_EXECUTABLE_ARCH}"
      files:
        - Cargo.lock
    paths:
      - target/                 
      - .cargo/registry/         
      - .cargo/git/               
      - .cargo/bin/             
      - .rustup/                  

.restore_cache_to_tmp: &restore_cache_to_tmp
  - apt-get update && apt-get install -y rsync
  - mkdir -p "$CARGO_HOME" "$RUSTUP_HOME" "$CARGO_TARGET_DIR"
  - rsync -a "$CI_PROJECT_DIR/.cargo/" "$CARGO_HOME/" || true
  - rsync -a "$CI_PROJECT_DIR/.rustup/" "$RUSTUP_HOME/" || true
  - rsync -a "$CI_PROJECT_DIR/target/" "$CARGO_TARGET_DIR/" || true

.save_cache_from_tmp: &save_cache_from_tmp
  - mkdir -p "$CI_PROJECT_DIR/.cargo" "$CI_PROJECT_DIR/.rustup" "$CI_PROJECT_DIR/target"
  - rsync -a "$CARGO_HOME/" "$CI_PROJECT_DIR/.cargo/"
  - rsync -a "$RUSTUP_HOME/" "$CI_PROJECT_DIR/.rustup/"
  - rsync -a "$CARGO_TARGET_DIR/" "$CI_PROJECT_DIR/target/"

# ====================================================
# Target build
# ====================================================

.linux_x86_64: &linux_x86_64
  image:
    name: ghcr.io/cross-rs/cross:main
    pull_policy: if-not-present
  variables:
    TARGET: "x86_64-unknown-linux-gnu"
    BIN_NAME: "innocody-lsp"
    CARGO_HOME: "/tmp/.cargo/$CI_PROJECT_NAME/$CI_COMMIT_REF_SLUG/$CI_JOB_NAME"
    RUSTUP_HOME: "/tmp/.rustup/$CI_PROJECT_NAME/$CI_COMMIT_REF_SLUG/$CI_JOB_NAME"
    CARGO_TARGET_DIR: "/tmp/$CI_PROJECT_NAME/$CI_COMMIT_REF_SLUG/$CI_JOB_NAME/target"
  tags:
    - khen-linux-shared

.linux_aarch64: &linux_aarch64
  image:
    name: ghcr.io/cross-rs/cross:main
    pull_policy: if-not-present
  variables:
    TARGET: "aarch64-unknown-linux-gnu"
    BIN_NAME: "innocody-lsp"
    CARGO_HOME: "/tmp/.cargo/$CI_PROJECT_NAME/$CI_COMMIT_REF_SLUG/$CI_JOB_NAME"
    RUSTUP_HOME: "/tmp/.rustup/$CI_PROJECT_NAME/$CI_COMMIT_REF_SLUG/$CI_JOB_NAME"
    CARGO_TARGET_DIR: "/tmp/$CI_PROJECT_NAME/$CI_COMMIT_REF_SLUG/$CI_JOB_NAME/target"
  tags:
    - khen-linux-shared

.macos_aarch64: &macos_aarch64
  variables:
    TARGET: "aarch64-apple-darwin"
    BIN_NAME: "innocody-lsp"
  tags:
    - macos-aarch64

.macos_x86_64: &macos_x86_64
  variables:
    TARGET: "x86_64-apple-darwin"
    BIN_NAME: "innocody-lsp"
  tags:
    - macos-x86_64

.windows_aarch64: &windows_aarch64
  variables:
    TARGET: "aarch64-pc-windows-msvc"
    BIN_NAME: "innocody-lsp.exe"
  tags:
    - windows

.windows_x86_64: &windows_x86_64
  variables:
    TARGET: "x86_64-pc-windows-msvc"
    BIN_NAME: "innocody-lsp.exe"
  tags:
    - windows

# ====================================================
# Artifact script
# ====================================================

.save_artifact_script: &save_artifact_script
  - export SHORT_SHA=$(echo $CI_COMMIT_SHA | cut -c1-8)
  - export TS=$(date +%Y%m%d-%H%M%S)
  - export BIN_PREFIX=${BIN_PREFIX:-"release"}
  - export CARGO_TARGET_DIR=${CARGO_TARGET_DIR:-$CI_PROJECT_DIR/target}
  - export BIN_PATH=${CARGO_TARGET_DIR}/${TARGET}/${BIN_PREFIX}/${BIN_NAME}
  - cp ${BIN_PATH} ${BIN_NAME}

.save_artifact_script_windows: &save_artifact_script_windows
  - |
    $ErrorActionPreference = 'Stop'

    $SHORT_SHA = $env:CI_COMMIT_SHA.Substring(0,8)
    $TS = Get-Date -Format "yyyyMMdd-HHmmss"

    $BIN_PREFIX = if ($env:BIN_PREFIX) { $env:BIN_PREFIX } else { "release" }
    $BIN_NAME = $env:BIN_NAME
    $CARGO_TARGET_DIR = if ($env:CARGO_TARGET_DIR) { $env:CARGO_TARGET_DIR } else { Join-Path $env:CI_PROJECT_DIR "target" }

    $BIN_PATH = Join-Path -Path (Join-Path $CARGO_TARGET_DIR $TARGET) $BIN_PREFIX $BIN_NAME

    if (Test-Path $BIN_PATH) {
      Copy-Item -Path $BIN_PATH -Destination $BIN_NAME -Force
      Write-Host "Binary found at: $BIN_PATH"
    } else {
      Write-Host "Binary not found at: $BIN_PATH"
      Exit 1
    }

.artifact_definition: &artifact_definition
  artifacts:
    name: "${CI_PROJECT_NAME}-${CI_COMMIT_REF_NAME}-${CI_COMMIT_SHORT_SHA}"
    paths:
      - "*lsp*"
    expire_in: 1 month


# ====================================================
# Common before_script logic
# ====================================================

.linux_before_script: &linux_before_script
  before_script:
    - *restore_cache_to_tmp
    - |
      if ! command -v rustup >/dev/null 2>&1; then
        echo "rustup not found. Installing..."
        curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh -s -- -y --no-modify-path
        source "$CARGO_HOME/env" || source "$HOME/.cargo/env"
      else
        echo "rustup is already installed."
      fi
    - export PATH="$CARGO_HOME/bin:$PATH"
    - rustup set profile minimal
    - rustup update stable
    - rustup default stable 
    - rustup component add rust-src
    - export RUSTFLAGS="-C linker=clang -C link-arg=-fuse-ld=mold -C link-arg=--target=$TARGET"

# MacOS-specific before_script
.macos_before_script: &macos_before_script
  before_script:
    - xcode-select --install || echo "Xcode CLI tools already installed"
    - clang --version

# ====================================================
# Common test rules (only for MRs into dev)
# ====================================================
.dev_common_template: &dev_common_template
  rules:
    # 1. If this is an MR pipeline AND the target branch is dev → run tests
    - if: '$CI_PIPELINE_SOURCE == "merge_request_event" && $CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "dev"'
      when: on_success
    # 2. Otherwise → skip tests entirely
    - when: never

# ====================================================
# Common Release Logic (for main branch or tags)
# ====================================================
.build_release: &build_release
  <<: *artifact_definition
  stage: build-release
  rules:
    - if: '$CI_COMMIT_BRANCH == "main"'

.build_dev: &build_dev
  <<: *artifact_definition
  stage: build-dev
  rules:
    - if: '$CI_COMMIT_BRANCH == "dev"'

.cross_test: &cross_test
  script:
    - cross test --target ${TARGET} --profile ci
    - *save_cache_from_tmp

.cargo_test: &cargo_test
  script:
    - rustup default stable
    - rustup target add ${TARGET}
    - cargo test --target ${TARGET} --profile ci

.cross_build: &cross_build
  script:
    - cross build --release --target ${TARGET}
    - *save_cache_from_tmp
    - *save_artifact_script

.cargo_build: &cargo_build
  script:
    - rustup default stable
    - rustup target add ${TARGET}
    - cargo build --release --target ${TARGET}
    - *save_artifact_script

.cargo_build_win: &cargo_build_win
  script:
    - rustup default stable
    - rustup target add ${TARGET}
    - cargo build --release --target ${TARGET}
    - *save_artifact_script_windows

# ====================================================
# Tests
# ====================================================

test-linux-x64:
  stage: test
  <<: *linux_x86_64
  <<: [
    *dev_common_template, 
    *linux_before_script, 
  ]
  <<: *cache_definition
  <<: *cross_test

test-linux-aarch64:
  stage: test
  <<: *linux_aarch64
  <<: [
    *dev_common_template, 
    *linux_before_script, 
  ]
  <<: *cache_definition
  <<: *cross_test
  rules:
    - when: never # Temporarily disabled due to issues with cross test on aarch64 no runner available chip amd64

test-darwin-aarch64:
  stage: test
  <<: *macos_aarch64
  <<: [
    *dev_common_template, 
    *macos_before_script, 
  ]
  <<: *cache_definition
  <<: *cargo_test

test-darwin-x64:
  stage: test
  <<: *macos_x86_64
  <<: [
    *dev_common_template, 
    *macos_before_script, 
  ]
  <<: *cache_definition
  <<: *cargo_test

test-win32-x64:
  stage: test
  <<: *windows_x86_64
  <<: [
    *dev_common_template, 
  ]
  <<: *cache_definition
  <<: *cargo_test

# ====================================================
# Dev Build Targets (using cross images, with optional test dependencies)
# ====================================================

build-dev-linux-x64:
  needs:
    - job: test-linux-x64
      optional: true
    - job: test-linux-aarch64
      optional: true
  <<: *linux_x86_64
  <<: [
    *build_dev,
    *linux_before_script,
    *cross_build
  ]
  <<: *cache_definition

build-dev-darwin-x64:
  needs:
    - job: test-darwin-x64
      optional: true
    - job: test-darwin-aarch64
      optional: true
  <<: *macos_x86_64
  <<: [
    *build_dev,
    *macos_before_script,
    *cargo_build
  ]
  <<: *cache_definition

build-dev-win32-x64:
  needs:
    - job: test-win32-x64
      optional: true
  <<: *windows_x86_64
  <<: [
    *build_dev,
    *cargo_build_win
  ]
  <<: *cache_definition


# ====================================================
# Linux Build Targets (using cross images)
# ====================================================
build-release-linux-x64:
  <<: *linux_x86_64
  <<: [
    *build_release, 
    *linux_before_script, 
    *cross_build
  ]
  <<: *cache_definition

build-release-linux-arm64:
  <<: *linux_aarch64
  <<: [
    *build_release, 
    *linux_before_script, 
    *cross_build
  ]
  <<: *cache_definition
  rules:
    - when: never # Temporarily disabled due to issues with cross build on aarch64 no runner available chip amd64


# ====================================================
# MacOS Build Target
# ====================================================
build-release-darwin-arm64:
  <<: *macos_aarch64
  <<: [
    *build_release, 
    *macos_before_script, 
    *cargo_build
  ]
  <<: *cache_definition

build-release-darwin-x64:
  <<: *macos_x86_64
  <<: [
    *build_release, 
    *macos_before_script, 
    *cargo_build
  ]
  <<: *cache_definition

# ====================================================
# Windows Build Targets
# ====================================================
build-release-win32-x64:
  <<: *windows_x86_64
  <<: [
    *build_release, 
    *cargo_build_win
  ]
  <<: *cache_definition

build-release-win32-arm64:
  <<: *windows_aarch64
  <<: [
    *build_release, 
    *cargo_build_win
  ]
  <<: *cache_definition

trigger_build_extension:
  stage: trigger-build-extension
  only:
    - main
    - dev
  script:
    - echo "Triggering pipeline in innocody-vscode from branch $CI_COMMIT_REF_NAME..."
    - |
      curl -X POST --fail \
        -F token=$VS_CODE_TRIGGER_TOKEN \
        -F ref=$CI_COMMIT_REF_NAME \
        https://gitlab-v2.innotech-vn.com/api/v4/projects/5/trigger/pipeline

trigger_build_extension:
  stage: trigger-build-extension
  only:
    - main
    - dev
  script:
    - |
      PIPELINE_TITLE="Triggered: $CI_PROJECT_NAME | PIPELINE: $CI_PIPELINE_ID | SHA: $CI_COMMIT_SHORT_SHA"
      echo "Triggering with name: $PIPELINE_TITLE"
      curl -X POST --fail \
        -F token=$VS_CODE_TRIGGER_TOKEN \
        -F ref=$CI_COMMIT_REF_NAME \
        -F "variables[PIPELINE_TITLE]=$PIPELINE_TITLE" \
        -F "variables[CI_CONFIG_PATH]=.gitlab-ci-trigger.yml" \
        https://gitlab-v2.innotech-vn.com/api/v4/projects/5/trigger/pipeline
