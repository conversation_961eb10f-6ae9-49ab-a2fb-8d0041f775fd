import requests

url = "https://api.runpod.ai/v2/sotm489wf1vufe/openai/v1/embeddings"
api_key = "rpa_MM1FIE43K11REIWSMXFUVKXUVN5KCQO1M7IDLQZQ6r3vhd"

headers = {
    "Content-Type": "application/json",
    "Authorization": f"Bearer {api_key}"
}

payload = {
    "model": "BAAI/bge-code-v1",
    "input": ["A" * 8000]*32
}

response = requests.post(url, headers=headers, json=payload)

if response.ok:
    data = response.json()
    print(data)
    embeddings = [item["embedding"] for item in data.get("data", [])]
    print("Embeddings:", embeddings)
else:
    print("Error:", response.status_code, response.text)


# import requests

# ENDPOINT_ID = "a3i27vmpyshx15"
# API_KEY = "rpa_MM1FIE43K11REIWSMXFUVKXUVN5KCQO1M7IDLQZQ6r3vhd"
# url = f"https://api.runpod.ai/v2/{ENDPOINT_ID}/runsync"

# headers = {
#     "Content-Type": "application/json",
#     "Authorization": f"Bearer {API_KEY}"
# }

# payload = {
#     "input": {
#         "model": "khaangnguyeen/static-bge-code-v1-st",
#         "input": "A" * 8000
#     }
# }

# response = requests.post(url, headers=headers, json=payload)
# response.raise_for_status()

# res = response.json()
# print(res)
# embeddings = [item["embedding"] for item in res["data"]]
# print(embeddings)
