fields:
  url:
    f_type: string
    f_desc: "The URL of the MCP server, e.g., 'https://api.example.com/mcp/sse'."
  headers:
    f_type: string_to_string_map
    f_desc: "HTTP headers to include in requests to the MCP server."
    f_default:
      User-Agent: "Innocody.ai (+https://gitlab-v2.innotech-vn.com/the-ai-team/innocody)"
      Accept: text/event-stream
      Content-Type: application/json
  init_timeout:
    f_type: string_short
    f_desc: "Timeout in seconds for MCP server initialization."
    f_default: "60"
    f_extra: true
  request_timeout:
    f_type: string_short
    f_desc: "Timeout in seconds for MCP requests."
    f_default: "30"
    f_extra: true
description: |
  You can add here an MCP (Model Context Protocol) server, connecting to an SSE endpoint.
  Read more about MCP here: https://www.anthropic.com/news/model-context-protocol
available:
  on_your_laptop_possible: true
  when_isolated_possible: true
confirmation:
  ask_user_default: ["*"]
  deny_default: []
smartlinks:
  - sl_label: "Test"
    sl_chat:
      - role: "user"
        content: >
          🔧 Your job is to test %CURRENT_CONFIG%. Tools that this MCP server has created should be visible to you. Don't search anything, it should be visible as
          a tools already. Run one and express happiness. If something does wrong, or you don't see the tools, ask user if they want to fix it by rewriting the config.
    sl_enable_only_with_tool: true
