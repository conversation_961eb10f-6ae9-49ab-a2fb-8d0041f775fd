use crate::at_commands::at_commands::AtCommandsContext;
use crate::call_validation::{<PERSON>t<PERSON><PERSON><PERSON>, ChatMessage, ContextEnum, DiffChunk};
use crate::integrations::integr_abstract::IntegrationConfirmation;
use crate::privacy::{check_file_privacy, load_privacy_if_needed, FilePrivacyLevel, PrivacySettings};
use crate::tools::file_edit::auxiliary::{await_ast_indexing, convert_edit_to_diffchunks, str_replace, sync_documents_ast};
use crate::tools::tools_description::{MatchConfirmDeny, MatchConfirmDenyResult, Tool, ToolDesc, ToolParam, ToolSource, ToolSourceType};
use async_trait::async_trait;
use serde_json::{json, Value};
use std::collections::HashMap;
use std::path::PathBuf;
use std::sync::Arc;
use tokio::sync::Mutex as AMutex;
use crate::files_correction::{canonicalize_normalized_path, get_project_dirs, preprocess_path_for_normalization};
use tokio::sync::RwLock as ARwLock;
use crate::at_commands::at_file::{file_repair_candidates, return_one_candidate_or_a_good_error};
use crate::global_context::GlobalContext;

struct ToolUpdateTextDocArgs {
    path: PathBuf,
    old_str: String,
    replacement: String,
    multiple: bool,
}

pub struct ToolUpdateTextDoc {
    pub config_path: String,
}

async fn parse_args(
    gcx: Arc<ARwLock<GlobalContext>>,
    args: &HashMap<String, Value>,
    privacy_settings: Arc<PrivacySettings>
) -> Result<ToolUpdateTextDocArgs, String> {
    let path = match args.get("path") {
        Some(Value::String(s)) => {
            let raw_path = preprocess_path_for_normalization(s.trim().to_string());
            let candidates_file = file_repair_candidates(gcx.clone(), &raw_path, 3, false).await;
            let path = match return_one_candidate_or_a_good_error(gcx.clone(), &raw_path, &candidates_file, &get_project_dirs(gcx.clone()).await, false).await {
                Ok(f) => canonicalize_normalized_path(PathBuf::from(f)),
                Err(e) => return Err(e),
            };
            if check_file_privacy(privacy_settings, &path, &FilePrivacyLevel::AllowToSendAnywhere).is_err() {
                return Err(format!(
                    "Error: Cannot update the file '{:?}' due to privacy settings.",
                    s.trim()
                ));
            }
            if !path.exists() {
                return Err(format!(
                    "Error: The file '{:?}' does not exist. Please check if the path is correct and the file exists.",
                    path
                ));
            }
            path
        }
        Some(v) => return Err(format!("Error: The 'path' argument must be a string, but received: {:?}", v)),
        None => return Err("Error: The 'path' argument is required but was not provided.".to_string()),
    };
    let old_str = match args.get("old_str") {
        Some(Value::String(s)) => s.to_string(),
        Some(v) => return Err(format!("Error: The 'old_str' argument must be a string containing the text to replace, but received: {:?}", v)),
        None => return Err("Error: The 'old_str' argument is required. Please provide the text that needs to be replaced.".to_string())
    };
    let replacement = match args.get("replacement") {
        Some(Value::String(s)) => s.to_string(),
        Some(v) => return Err(format!("Error: The 'replacement' argument must be a string containing the new text, but received: {:?}", v)),
        None => return Err("Error: The 'replacement' argument is required. Please provide the new text that will replace the old text.".to_string())
    };
    let multiple = match args.get("multiple") {
        Some(Value::Bool(b)) => b.clone(),
        Some(Value::String(v)) => match v.to_lowercase().as_str() {
            "false" => false,
            "true" => true,
            _ => {
                return Err(format!("argument 'multiple' should be a boolean: {:?}", v))
            }
        },
        Some(v) => return Err(format!("Error: The 'multiple' argument must be a boolean (true/false) indicating whether to replace all occurrences, but received: {:?}", v)),
        None => false,
    };

    Ok(ToolUpdateTextDocArgs {
        path,
        old_str,
        replacement,
        multiple
    })
}

pub async fn tool_update_text_doc_exec(
    gcx: Arc<ARwLock<GlobalContext>>,
    args: &HashMap<String, Value>,
    dry: bool
) -> Result<(String, String, Vec<DiffChunk>), String> {
    let privacy_settings = load_privacy_if_needed(gcx.clone()).await;
    let args = parse_args(gcx.clone(), args, privacy_settings).await?;
    await_ast_indexing(gcx.clone()).await?;
    let (before_text, after_text) = str_replace(gcx.clone(), &args.path, &args.old_str, &args.replacement, args.multiple, dry).await?;
    sync_documents_ast(gcx.clone(), &args.path).await?;
    let diff_chunks = convert_edit_to_diffchunks(args.path.clone(), &before_text, &after_text)?;
    Ok((before_text, after_text, diff_chunks))
}

#[async_trait]
impl Tool for ToolUpdateTextDoc {
    fn as_any(&self) -> &dyn std::any::Any {
        self
    }

    async fn tool_execute(
        &mut self,
        ccx: Arc<AMutex<AtCommandsContext>>,
        tool_call_id: &String,
        args: &HashMap<String, Value>,
    ) -> Result<(bool, Vec<ContextEnum>), String> {
        let gcx = ccx.lock().await.global_context.clone();
        let (_, _, diff_chunks) = tool_update_text_doc_exec(gcx.clone(), args, false).await?;
        let results = vec![ChatMessage {
            role: "diff".to_string(),
            content: ChatContent::SimpleText(json!(diff_chunks).to_string()),
            tool_calls: None,
            tool_call_id: tool_call_id.clone(),
            usage: None,
            ..Default::default()
        }]
        .into_iter()
        .map(|x| ContextEnum::ChatMessage(x))
        .collect::<Vec<_>>();
        Ok((false, results))
    }

    async fn match_against_confirm_deny(
        &self,
        ccx: Arc<AMutex<AtCommandsContext>>,
        args: &HashMap<String, Value>,
    ) -> Result<MatchConfirmDeny, String> {
        let gcx = ccx.lock().await.global_context.clone();
        let privacy_settings = load_privacy_if_needed(gcx.clone()).await;

        async fn can_execute_tool_edit(gcx: Arc<ARwLock<GlobalContext>>, args: &HashMap<String, Value>, privacy_settings: Arc<PrivacySettings>) -> Result<(), String> {
            let _ = parse_args(gcx.clone(), args, privacy_settings).await?;
            Ok(())
        }

        let msgs_len = ccx.lock().await.messages.len();

        // workaround: if messages weren't passed by ToolsPermissionCheckPost, legacy
        if msgs_len != 0 {
            // if we cannot execute apply_edit, there's no need for confirmation
            if let Err(_) = can_execute_tool_edit(gcx.clone(), args, privacy_settings).await {
                return Ok(MatchConfirmDeny {
                    result: MatchConfirmDenyResult::PASS,
                    command: "update_textdoc".to_string(),
                    rule: "".to_string(),
                });
            }
        }
        Ok(MatchConfirmDeny {
            result: MatchConfirmDenyResult::CONFIRMATION,
            command: "update_textdoc".to_string(),
            rule: "default".to_string(),
        })
    }

    async fn command_to_match_against_confirm_deny(
        &self,
        _ccx: Arc<AMutex<AtCommandsContext>>,
        _args: &HashMap<String, Value>,
    ) -> Result<String, String> {
        Ok("update_textdoc".to_string())
    }

    fn confirm_deny_rules(&self) -> Option<IntegrationConfirmation> {
        Some(IntegrationConfirmation {
            ask_user: vec!["update_textdoc*".to_string()],
            deny: vec![],
        })
    }
    
    fn tool_description(&self) -> ToolDesc {
        ToolDesc {
            name: "update_textdoc".to_string(),
            display_name: "Update Text Document".to_string(),
            source: ToolSource { 
                source_type: ToolSourceType::Builtin, 
                config_path: self.config_path.clone(), 
            },
            agentic: false,
            experimental: false,
            description: r#"
PURPOSE: Make specific, exact text replacements in existing files

CHAIN OF THOUGHT DECISION PROCESS:

STEP 1: ANALYZE THE REQUEST
- Is the user asking to "change", "update", "modify", "fix" existing code?
- Is this about replacing specific, exact text?
- Do I know the exact text to replace?

STEP 2: INVESTIGATE CURRENT STATE
- Does the target file exist? (Use cat() to check)
- What is the exact text that needs to be changed?
- Is this a simple string replacement?

STEP 3: DECIDE TO USE update_textdoc()
Use this tool when:
Making exact string replacements in existing files
Changing function names, variable names, or specific values
Fixing typos or small text errors
Updating specific constants or configuration values
Replacing exact text patterns that you can specify precisely

AVOID when:
- Creating new files
- Completely replacing file content
- Pattern-based changes (use update_textdoc_regex instead)
- Multiple similar changes across the file
- Changes that require regex pattern matching

EXAMPLES OF REASONING:

Example 1: "Change function name from old_name to new_name"
→ Analysis: "User wants to modify existing code with exact replacement"
→ Investigation: "File exists, I can see 'def old_name(' in the code"
→ Decision: "I'll use update_textdoc() for exact string replacement"
→ Action: "I'll replace 'def old_name(' with 'def new_name('"

Example 2: "Fix the typo in the error message"
→ Analysis: "User wants to correct a specific text error"
→ Investigation: "File exists, I can see the typo 'Helllo World'"
→ Decision: "I'll use update_textdoc() to fix the exact text"
→ Action: "I'll replace 'Helllo World' with 'Hello World'"

Example 3: "Update the version number from 1.0.0 to 2.0.0"
→ Analysis: "User wants to change a specific constant value"
→ Investigation: "File exists, I can see 'version = \"1.0.0\"'"
→ Decision: "I'll use update_textdoc() for exact value replacement"
→ Action: "I'll replace 'version = \"1.0.0\"' with 'version = \"2.0.0\"'"

PARAMETERS:
- path: Absolute path to the file to modify
- old_str: The exact text to replace (must match exactly)
- replacement: The new text that will replace the old text
- multiple: If true, replace all occurrences; if false, only first occurrence

IMPORTANT: The old_str must match exactly. Use update_textdoc_regex() for pattern matching!
"#.to_string(),
            parameters: vec![
                ToolParam {
                    name: "path".to_string(),
                    description: "Absolute path to the file to change.".to_string(),
                    param_type: "string".to_string(),
                },
                ToolParam {
                    name: "old_str".to_string(),
                    description: "The exact text that needs to be updated. Use update_textdoc_regex if you need pattern matching.".to_string(),
                    param_type: "string".to_string(),
                },
                ToolParam {
                    name: "replacement".to_string(),
                    description: "The new text that will replace the old text.".to_string(),
                    param_type: "string".to_string(),
                },
                ToolParam {
                    name: "multiple".to_string(),
                    description: "If true, applies the replacement to all occurrences; if false, only the first occurrence is replaced.".to_string(),
                    param_type: "boolean".to_string(),
                }
            ],
            parameters_required: vec!["path".to_string(), "old_str".to_string(), "replacement".to_string()],
        }
    }
}
