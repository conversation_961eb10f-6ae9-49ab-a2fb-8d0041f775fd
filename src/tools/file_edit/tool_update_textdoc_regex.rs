use crate::at_commands::at_commands::AtCommandsContext;
use crate::call_validation::{<PERSON>t<PERSON><PERSON><PERSON>, ChatMessage, ContextEnum, DiffChunk};
use crate::integrations::integr_abstract::IntegrationConfirmation;
use crate::privacy::{check_file_privacy, load_privacy_if_needed, FilePrivacyLevel, PrivacySettings};
use crate::tools::file_edit::auxiliary::{await_ast_indexing, convert_edit_to_diffchunks, str_replace_regex, sync_documents_ast};
use crate::tools::tools_description::{MatchConfirmDeny, MatchConfirmDenyResult, Tool, ToolDesc, ToolParam, ToolSource, ToolSourceType};
use async_trait::async_trait;
use serde_json::{json, Value};
use std::collections::HashMap;
use std::path::PathBuf;
use std::sync::Arc;
use regex::Regex;
use tokio::sync::Mutex as AMutex;
use crate::files_correction::{canonicalize_normalized_path, get_project_dirs, preprocess_path_for_normalization};
use tokio::sync::RwLock as ARwLock;
use crate::at_commands::at_file::{file_repair_candidates, return_one_candidate_or_a_good_error};
use crate::global_context::GlobalContext;

struct ToolUpdateTextDocRegexArgs {
    path: PathBuf,
    pattern: Regex,
    replacement: String,
    multiple: bool,
}

pub struct ToolUpdateTextDocRegex {
    pub config_path: String,
}

async fn parse_args(
    gcx: Arc<ARwLock<GlobalContext>>,
    args: &HashMap<String, Value>,
    privacy_settings: Arc<PrivacySettings>
) -> Result<ToolUpdateTextDocRegexArgs, String> {
    let path = match args.get("path") {
        Some(Value::String(s)) => {
            let raw_path = preprocess_path_for_normalization(s.trim().to_string());
            let candidates_file = file_repair_candidates(gcx.clone(), &raw_path, 3, false).await;
            let path = match return_one_candidate_or_a_good_error(gcx.clone(), &raw_path, &candidates_file, &get_project_dirs(gcx.clone()).await, false).await {
                Ok(f) => canonicalize_normalized_path(PathBuf::from(f)),
                Err(e) => return Err(e),
            };
            if check_file_privacy(privacy_settings, &path, &FilePrivacyLevel::AllowToSendAnywhere).is_err() {
                return Err(format!(
                    "Error: Cannot update the file '{:?}' due to privacy settings.",
                    s.trim()
                ));
            }
            if !path.exists() {
                return Err(format!("argument 'path' doesn't exists: {:?}", path));
            }
            path
        }
        Some(v) => return Err(format!("argument 'path' should be a string: {:?}", v)),
        None => return Err("argument 'path' is required".to_string()),
    };
    let pattern = match args.get("pattern") {
        Some(Value::String(s)) => {
            match Regex::new(s) {
                Ok(r) => r,
                Err(err) => {
                    return Err(format!(
                        "Error: The provided regex pattern is invalid. Details: {}. Please check your regular expression syntax.",
                        err
                    ));
                }
            }
        },
        Some(v) => return Err(format!("Error: The 'pattern' argument must be a string containing a valid regular expression, but received: {:?}", v)),
        None => return Err("Error: The 'pattern' argument is required. Please provide a regular expression pattern to match the text that needs to be updated.".to_string())
    };
    let replacement = match args.get("replacement") {
        Some(Value::String(s)) => s.to_string(),
        Some(v) => return Err(format!("argument 'replacement' should be a string: {:?}", v)),
        None => return Err("argument 'replacement' is required".to_string())
    };
    let multiple = match args.get("multiple") {
        Some(Value::Bool(b)) => b.clone(),
        Some(Value::String(v)) => match v.to_lowercase().as_str() {
            "false" => false,
            "true" => true,
            _ => {
                return Err(format!("argument 'multiple' should be a boolean: {:?}", v))
            }
        },
        Some(v) => return Err(format!("Error: The 'multiple' argument must be a boolean (true/false) indicating whether to replace all occurrences, but received: {:?}", v)),
        None => false,
    };

    Ok(ToolUpdateTextDocRegexArgs {
        path,
        pattern,
        replacement,
        multiple
    })
}

pub async fn tool_update_text_doc_regex_exec(
    gcx: Arc<ARwLock<GlobalContext>>,
    args: &HashMap<String, Value>,
    dry: bool
) -> Result<(String, String, Vec<DiffChunk>), String> {
    let privacy_settings = load_privacy_if_needed(gcx.clone()).await;
    let args = parse_args(gcx.clone(), args, privacy_settings).await?;
    await_ast_indexing(gcx.clone()).await?;
    let (before_text, after_text) = str_replace_regex(gcx.clone(), &args.path, &args.pattern, &args.replacement, args.multiple, dry).await?;
    sync_documents_ast(gcx.clone(), &args.path).await?;
    let diff_chunks = convert_edit_to_diffchunks(args.path.clone(), &before_text, &after_text)?;
    Ok((before_text, after_text, diff_chunks))
}

#[async_trait]
impl Tool for ToolUpdateTextDocRegex {
    fn as_any(&self) -> &dyn std::any::Any {
        self
    }

    async fn tool_execute(
        &mut self,
        ccx: Arc<AMutex<AtCommandsContext>>,
        tool_call_id: &String,
        args: &HashMap<String, Value>,
    ) -> Result<(bool, Vec<ContextEnum>), String> {
        let gcx = ccx.lock().await.global_context.clone();
        let (_, _, diff_chunks) = tool_update_text_doc_regex_exec(gcx.clone(), args, false).await?;
        let results = vec![ChatMessage {
            role: "diff".to_string(),
            content: ChatContent::SimpleText(json!(diff_chunks).to_string()),
            tool_calls: None,
            tool_call_id: tool_call_id.clone(),
            usage: None,
            ..Default::default()
        }]
        .into_iter()
        .map(|x| ContextEnum::ChatMessage(x))
        .collect::<Vec<_>>();
        Ok((false, results))
    }

    async fn match_against_confirm_deny(
        &self,
        ccx: Arc<AMutex<AtCommandsContext>>,
        args: &HashMap<String, Value>,
    ) -> Result<MatchConfirmDeny, String> {
        let gcx = ccx.lock().await.global_context.clone();
        let privacy_settings = load_privacy_if_needed(gcx.clone()).await;

        async fn can_execute_tool_edit(gcx: Arc<ARwLock<GlobalContext>>, args: &HashMap<String, Value>, privacy_settings: Arc<PrivacySettings>) -> Result<(), String> {
            let _ = parse_args(gcx.clone(), args, privacy_settings).await?;
            Ok(())
        }

        let msgs_len = ccx.lock().await.messages.len();

        // workaround: if messages weren't passed by ToolsPermissionCheckPost, legacy
        if msgs_len != 0 {
            // if we cannot execute apply_edit, there's no need for confirmation
            if let Err(_) = can_execute_tool_edit(gcx.clone(), args, privacy_settings).await {
                return Ok(MatchConfirmDeny {
                    result: MatchConfirmDenyResult::PASS,
                    command: "update_textdoc_regex".to_string(),
                    rule: "".to_string(),
                });
            }
        }
        Ok(MatchConfirmDeny {
            result: MatchConfirmDenyResult::CONFIRMATION,
            command: "update_textdoc_regex".to_string(),
            rule: "default".to_string(),
        })
    }

    async fn command_to_match_against_confirm_deny(
        &self,
        _ccx: Arc<AMutex<AtCommandsContext>>,
        _args: &HashMap<String, Value>,
    ) -> Result<String, String> {
        Ok("update_textdoc_regex".to_string())
    }

    fn confirm_deny_rules(&self) -> Option<IntegrationConfirmation> {
        Some(IntegrationConfirmation {
            ask_user: vec!["update_textdoc_regex*".to_string()],
            deny: vec![],
        })
    }
    
    fn tool_description(&self) -> ToolDesc {
        ToolDesc {
            name: "update_textdoc_regex".to_string(),
            display_name: "Update Text Document with Regex".to_string(),
            source: ToolSource { 
                source_type: ToolSourceType::Builtin, 
                config_path: self.config_path.clone(), 
            },
            agentic: false,
            experimental: false,
            description: r#"
PURPOSE: Make pattern-based changes using regex in existing files

CHAIN OF THOUGHT DECISION PROCESS:

STEP 1: ANALYZE THE REQUEST
- Is the user asking for pattern-based changes?
- Do I need to match variable text patterns?
- Are there multiple similar changes to make?
- Can the change be expressed as a regex pattern?

STEP 2: INVESTIGATE CURRENT STATE
- Does the target file exist? (Use cat() to check)
- What patterns need to be matched?
- Are there multiple occurrences to change?
- Can I create a regex pattern for this?

STEP 3: DECIDE TO USE update_textdoc_regex()
Use this tool when:
Making pattern-based changes across files
Replacing text that follows a specific pattern
Making multiple similar changes at once
Changes that can be expressed as regex patterns
Updating variable text like emails, URLs, version numbers
Replacing text where the exact content varies but follows a pattern

AVOID when:
- Creating new files
- Completely replacing file content
- Simple exact string replacements (use update_textdoc instead)
- Changes that don't follow a pattern
- When you can specify the exact text to replace

EXAMPLES OF REASONING:

Example 1: "Replace all email addresses from @old.com to @new.com"
→ Analysis: "User wants pattern-based changes for email addresses"
→ Investigation: "File exists, contains multiple email addresses with @old.com"
→ Decision: "I'll use update_textdoc_regex() for pattern matching"
→ Action: "I'll use regex pattern '@old\.com' to match and replace all emails"

Example 2: "Update all version numbers from 1.x.x to 2.0.0"
→ Analysis: "User wants to change version patterns across the file"
→ Investigation: "File contains multiple version strings like '1.0.0', '1.1.0'"
→ Decision: "I'll use update_textdoc_regex() for version pattern matching"
→ Action: "I'll use regex pattern '1\.[0-9]+\.[0-9]+' to match all 1.x.x versions"

Example 3: "Change all function names from get_* to fetch_*"
→ Analysis: "User wants pattern-based function name changes"
→ Investigation: "File contains multiple functions starting with 'get_'"
→ Decision: "I'll use update_textdoc_regex() for pattern-based replacement"
→ Action: "I'll use regex pattern 'get_([a-zA-Z_]+)' to match and replace function names"

PARAMETERS:
- path: Absolute path to the file to modify
- pattern: Regex pattern to match the text that needs to be updated
- replacement: The new text that will replace the matched pattern
- multiple: If true, replace all occurrences; if false, only first occurrence

IMPORTANT: Use simple regex patterns for better performance. Test your pattern first!
"#.to_string(),
            parameters: vec![
                ToolParam {
                    name: "path".to_string(),
                    description: "Absolute path to the file to change.".to_string(),
                    param_type: "string".to_string(),
                },
                ToolParam {
                    name: "pattern".to_string(),
                    description: "A regex pattern to match the text that needs to be updated. Prefer simpler regexes for better performance.".to_string(),
                    param_type: "string".to_string(),
                },
                ToolParam {
                    name: "replacement".to_string(),
                    description: "The new text that will replace the matched pattern.".to_string(),
                    param_type: "string".to_string(),
                },
                ToolParam {
                    name: "multiple".to_string(),
                    description: "If true, applies the replacement to all occurrences; if false, only the first occurrence is replaced.".to_string(),
                    param_type: "boolean".to_string(),
                }
            ],
            parameters_required: vec!["path".to_string(), "pattern".to_string(), "replacement".to_string()],
        }
    }
}
