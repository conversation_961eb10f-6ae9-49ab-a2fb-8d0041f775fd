use std::collections::HashSet;
use std::fs;
use std::sync::Arc;
use std::path::PathBuf;
use tokio::sync::RwLock as ARwLock;

use crate::call_validation;
use crate::global_context::{try_load_caps_quickly_if_not_present, GlobalContext};
use crate::http::http_post_json;
use crate::http::routers::v1::system_prompt::{PrependSystemPromptPost, PrependSystemPromptResponse};
use crate::integrations::docker::docker_container_manager::docker_container_get_host_lsp_port_to_connect;
use crate::scratchpads::scratchpad_utils::HasRagResults;
use crate::call_validation::{ChatMessage, ChatContent, ChatMode};


pub async fn get_default_system_prompt(
    gcx: Arc<ARwLock<GlobalContext>>,
    chat_mode: ChatMode,
) -> String {
    let mut error_log = Vec::new();
    let tconfig = crate::yaml_configs::customization_loader::load_customization(gcx.clone(), true, &mut error_log).await;
    for e in error_log.iter() {
        tracing::error!("{e}");
    }
    let prompt_key = match chat_mode {
        ChatMode::NO_TOOLS => "default",
        ChatMode::EXPLORE => "exploration_tools",
        ChatMode::AGENT => "agentic_tools",
        ChatMode::CONFIGURE => "configurator",
        ChatMode::PROJECT_SUMMARY => "project_summary",
    };
    let system_prompt = tconfig.system_prompts.get(prompt_key).map_or_else(|| {
        tracing::error!("cannot find system prompt `{}`", prompt_key);
        String::new()
    }, |x| x.text.clone());
    system_prompt
}

async fn _workspace_info(
    workspace_dirs: &[String],
    active_file_path: &Option<PathBuf>,
) -> String
{
    async fn get_vcs_info(detect_vcs_at: &PathBuf) -> String {
        let mut info = String::new();
        if let Some((vcs_path, vcs_type)) = crate::files_in_workspace::detect_vcs_for_a_file_path(detect_vcs_at).await {
            info.push_str(&format!("\nThe project is under {} version control, located at:\n{}", vcs_type, vcs_path.display()));
        } else {
            info.push_str("\nThere's no version control detected, complain to user if they want to use anything git/hg/svn/etc.");
        }
        info
    }
    let mut info = String::new();
    if !workspace_dirs.is_empty() {
        info.push_str(&format!("The current IDE workspace has these project directories:\n{}", workspace_dirs.join("\n")));
    }
    let detect_vcs_at_option = active_file_path.clone().or_else(|| workspace_dirs.get(0).map(PathBuf::from));
    if let Some(detect_vcs_at) = detect_vcs_at_option {
        let vcs_info = get_vcs_info(&detect_vcs_at).await;
        if let Some(active_file) = active_file_path {
            info.push_str(&format!("\n\nThe active IDE file is:\n{}", active_file.display()));
        } else {
            info.push_str("\n\nThere is no active file currently open in the IDE.");
        }
        info.push_str(&vcs_info);
    } else {
        info.push_str("\n\nThere is no active file with version control, complain to user if they want to use anything git/hg/svn/etc and ask to open a file in IDE for you to know which project is active.");
    }
    info
}

pub async fn dig_for_project_summarization_file(gcx: Arc<ARwLock<GlobalContext>>) -> (bool, Option<String>) {
    match crate::files_correction::get_active_project_path(gcx.clone()).await {
        Some(active_project_path) => {
            let summary_path = active_project_path.join(".innocody").join("project_summary.yaml");
            if !summary_path.exists() {
                (false, Some(summary_path.to_string_lossy().to_string()))
            } else {
                (true, Some(summary_path.to_string_lossy().to_string()))
            }
        }
        None => {
            tracing::info!("No projects found, project summarization is not relevant.");
            (false, None)
        }
    }
}

async fn _read_project_summary(
    summary_path: String,
) -> Option<String> {
    match fs::read_to_string(summary_path) {
        Ok(content) => {
            if let Ok(yaml) = serde_yaml::from_str::<serde_yaml::Value>(&content) {
                if let Some(project_summary) = yaml.get("project_summary") {
                    match project_summary {
                        serde_yaml::Value::String(s) => Some(s.clone()),
                        _ => {
                            tracing::error!("'project_summary' is not a string in YAML file.");
                            None
                        }
                    }
                } else {
                    tracing::error!("Key 'project_summary' not found in YAML file.");
                    None
                }
            } else {
                tracing::error!("Failed to parse project summary YAML file.");
                None
            }
        },
        Err(e) => {
            tracing::error!("Failed to read project summary file: {}", e);
            None
        }
    }
}

pub async fn system_prompt_add_extra_instructions(
    gcx: Arc<ARwLock<GlobalContext>>,
    system_prompt: String,
    tool_names: HashSet<String>,
) -> String {
    async fn workspace_files_info(gcx: &Arc<ARwLock<GlobalContext>>) -> (Vec<String>, Option<PathBuf>) {
        let gcx_locked = gcx.read().await;
        let documents_state = &gcx_locked.documents_state;
        let dirs_locked = documents_state.workspace_folders.lock().unwrap();
        let workspace_dirs = dirs_locked.clone().into_iter().map(|x| x.to_string_lossy().to_string()).collect();
        let active_file_path = documents_state.active_file_path.clone();
        (workspace_dirs, active_file_path)
    }

    let mut system_prompt = system_prompt.clone();
    if system_prompt.contains("%WORKSPACE_INFO%") {
        let (workspace_dirs, active_file_path) = workspace_files_info(&gcx).await;
        let info = _workspace_info(&workspace_dirs, &active_file_path).await;
        system_prompt = system_prompt.replace("%WORKSPACE_INFO%", &info);
    }
    if system_prompt.contains("%KNOWLEDGE_INSTRUCTIONS%") {
        match try_load_caps_quickly_if_not_present(gcx.clone(), 0).await {
            Ok(caps) => {
                if caps.metadata.features.contains(&"knowledge".to_string()) {
                    let cfg = crate::yaml_configs::customization_loader::load_customization_compiled_in();
                    let mut knowledge_instructions = cfg.get("KNOWLEDGE_INSTRUCTIONS_META")
                        .map(|x| x.as_str().unwrap_or("").to_string()).unwrap_or("".to_string());
                    if let Some(core_memories) = crate::memories::memories_get_core(gcx.clone()).await.ok() {
                        knowledge_instructions.push_str("\nThere are some pre-existing core memories:\n");
                        for mem in core_memories {
                            knowledge_instructions.push_str(&format!("🗃️\n{}\n\n", mem.iknow_memory));
                        }
                    }
                    system_prompt = system_prompt.replace("%KNOWLEDGE_INSTRUCTIONS%", &knowledge_instructions);
                    tracing::info!("adding up extra knowledge instructions");
                } else {
                    system_prompt = system_prompt.replace("%KNOWLEDGE_INSTRUCTIONS%", "");
                }
            },
            Err(_) => {
                system_prompt = system_prompt.replace("%KNOWLEDGE_INSTRUCTIONS%", "");
            },
        };
    }
    
    if system_prompt.contains("%PROJECT_SUMMARY%") {
        let (exists, summary_path_option) = dig_for_project_summarization_file(gcx.clone()).await;
        if exists {
            if let Some(summary_path) = summary_path_option {
                if let Some(project_info) = _read_project_summary(summary_path).await {
                    system_prompt = system_prompt.replace("%PROJECT_SUMMARY%", &project_info);
                } else {
                    system_prompt = system_prompt.replace("%PROJECT_SUMMARY%", "");
                }
            }
        } else {
            system_prompt = system_prompt.replace("%PROJECT_SUMMARY%", "");
        }
    }

    if system_prompt.contains("%EXPLORE_FILE_EDIT_INSTRUCTIONS%") {
        let replacement = if tool_names.contains("create_textdoc") || tool_names.contains("update_textdoc") {
            r#"
CHAIN OF THOUGHT FOR FILE MODIFICATIONS:

When the user asks you to modify files, follow this thinking process:

STEP 1: UNDERSTAND THE REQUEST
- What exactly is the user asking for?
- Is this a creation task or modification task?
- What is the scope of the change?

STEP 2: INVESTIGATE THE CURRENT STATE
- Does the target file exist? (Use cat() to check)
- What is the current content?
- What is the file structure?

STEP 3: APPLY DECISION TREE
Think through these questions sequentially:

Q1: "Is this a NEW file creation?"
   → If YES: Use create_textdoc()
   → Reasoning: "I need to create a file that doesn't exist yet"
   → If NO: Continue to Q2

Q2: "Is this a COMPLETE file replacement?"
   → If YES: Use create_textdoc()
   → Reasoning: "I need to overwrite the entire file content"
   → If NO: Continue to Q3

Q3: "Do I need PATTERN MATCHING or MULTIPLE changes?"
   → If YES: Use update_textdoc_regex()
   → Reasoning: "I need to match patterns or make similar changes across the file"
   → If NO: Continue to Q4

Q4: "Is this a SPECIFIC, EXACT text replacement?"
   → If YES: Use update_textdoc()
   → Reasoning: "I need to replace exact text strings"
   → If NO: Reconsider approach

STEP 4: EXPLAIN YOUR REASONING
Before executing any tool, explain:
- "I analyzed the request and found [key insight]"
- "I investigated the current state and discovered [file status]"
- "I chose [tool_name] because [specific reason]"
- "I will [specific action] to achieve [goal]"

STEP 5: EXECUTE AND VERIFY
- Apply the chosen tool with confidence
- Verify the result meets the goal
- Confirm the change is correct

EXAMPLES OF REASONING:

Example 1: "Create a new Python file"
→ Analysis: "User wants a new file that doesn't exist"
→ Investigation: "File doesn't exist yet"
→ Decision: "I'll use create_textdoc() to create a new file"
→ Action: "I'll create main.py with the specified content"

Example 2: "Change function name from old_name to new_name"
→ Analysis: "User wants to modify existing code"
→ Investigation: "File exists, I can see the current function name"
→ Decision: "I'll use update_textdoc() for exact string replacement"
→ Action: "I'll replace 'def old_name(' with 'def new_name('"

Example 3: "Replace all email addresses from @old.com to @new.com"
→ Analysis: "User wants pattern-based changes across the file"
→ Investigation: "File exists, contains multiple email addresses"
→ Decision: "I'll use update_textdoc_regex() for pattern matching"
→ Action: "I'll use regex pattern to match and replace all emails"

Then use `*_textdoc()` tools to make changes.
"#
        } else {
            ""
        };

        system_prompt = system_prompt.replace("%EXPLORE_FILE_EDIT_INSTRUCTIONS%", replacement);
    }

    if system_prompt.contains("%AGENT_EXPLORATION_INSTRUCTIONS%") {
        let replacement = if tool_names.contains("locate") {
            "- Call `locate()` tool to find relevant files.\n"
        } else {
            "- Call available tools to find relevant files.\n"
        };

        system_prompt = system_prompt.replace("%AGENT_EXPLORATION_INSTRUCTIONS%", replacement);
    }

    if system_prompt.contains("%AGENT_EXECUTION_INSTRUCTIONS%") {
        let replacement = if tool_names.contains("create_textdoc") || tool_names.contains("update_textdoc") {
r#"
3. Confirm the Plan with the User — No Coding Until Approved
  - Post a concise, bullet-point summary that includes
    • the suspected root cause
    • the exact files/functions you will modify or create
    • the new or updated tests you will add
    • the expected outcome and success criteria
  - Explicitly ask "Does this align with your vision?
  - Wait for the user's approval or revisions before proceeding.

4. Implement the Fix with Chain of Thought Reasoning
  🧠 THINKING PROCESS FOR EACH FILE CHANGE:
  
  For each file you need to modify, think through this process:
  
  STEP 1: ANALYZE THE CHANGE NEEDED
  - What exactly needs to be changed?
  - Is this a new file or modification?
  - What is the scope of the change?
  
  STEP 2: INVESTIGATE CURRENT STATE
  - Does the file exist? (Use cat() to check)
  - What is the current content?
  - What is the file structure?
  
  STEP 3: CHOOSE THE RIGHT TOOL
  Think through these questions:
  
  Q1: "Am I creating a NEW file?"
     → If YES: Use create_textdoc()
     → Reasoning: "I need to create a file that doesn't exist yet"
     → If NO: Continue to Q2
  
  Q2: "Am I COMPLETELY REPLACING an existing file?"
     → If YES: Use create_textdoc()
     → Reasoning: "I need to overwrite the entire file content"
     → If NO: Continue to Q3
  
  Q3: "Do I need PATTERN MATCHING or MULTIPLE similar changes?"
     → If YES: Use update_textdoc_regex()
     → Reasoning: "I need to match patterns or make similar changes across the file"
     → If NO: Continue to Q4
  
  Q4: "Am I making a SPECIFIC, EXACT text replacement?"
     → If YES: Use update_textdoc()
     → Reasoning: "I need to replace exact text strings"
     → If NO: Reconsider my approach
  
  STEP 4: EXPLAIN YOUR REASONING
  Before executing each tool, explain:
  - "I analyzed the change needed and found [key insight]"
  - "I investigated the current state and discovered [file status]"
  - "I chose [tool_name] because [specific reason]"
  - "I will [specific action] to achieve [goal]"
  
  STEP 5: EXECUTE AND VERIFY
  - Apply the chosen tool with confidence
  - Verify the result meets the goal
  - Confirm the change is correct
  
  💡 EXAMPLES OF REASONING:
  
  Example 1: "I need to create a new test file"
  → Analysis: "This is a new file that doesn't exist yet"
  → Investigation: "No test file exists in the project"
  → Decision: "I'll use create_textdoc() to create a new test file"
  → Action: "I'll create test_main.py with comprehensive test cases"
  
  Example 2: "I need to fix a function name typo"
  → Analysis: "This is a modification to existing code"
  → Investigation: "File exists, I can see the typo in the function name"
  → Decision: "I'll use update_textdoc() for exact string replacement"
  → Action: "I'll replace 'def old_functon(' with 'def old_function('"
  
  Example 3: "I need to update all version numbers in the project"
  → Analysis: "This is a pattern-based change across multiple files"
  → Investigation: "Multiple files contain version numbers that need updating"
  → Decision: "I'll use update_textdoc_regex() for pattern matching"
  → Action: "I'll use regex to find and replace all version patterns"
  
  - Apply the approved changes directly to project files using `update_textdoc()` and `create_textdoc()` tools.

5. Validate and Improve
  - Run all available tooling to ensure the project compiles and your fix works.
  - Add or update tests that reproduce the original bug and verify they pass.
  - Execute the full test suite to guard against regressions.
  - Iterate until everything is green.
"#
        } else {
r#"
  - Propose the changes to the user
    • the suspected root cause
    • the exact files/functions to modify or create
    • the new or updated tests to add
    • the expected outcome and success criteria
"#
        };

        system_prompt = system_prompt.replace("%AGENT_EXECUTION_INSTRUCTIONS%", replacement);
    }

    system_prompt
}

pub async fn prepend_the_right_system_prompt_and_maybe_more_initial_messages(
    gcx: Arc<ARwLock<GlobalContext>>,
    mut messages: Vec<call_validation::ChatMessage>,
    chat_meta: &call_validation::ChatMeta,
    stream_back_to_user: &mut HasRagResults,
    tool_names: HashSet<String>,
) -> Vec<call_validation::ChatMessage> {
    let have_system = !messages.is_empty() && messages[0].role == "system";
    if have_system {
        return messages;
    }
    if messages.len() == 0 {
        tracing::error!("What's that? Messages list is empty");
        return messages;
    }

    let is_inside_container = gcx.read().await.cmdline.inside_container;
    if chat_meta.chat_remote && !is_inside_container {
        messages = match prepend_system_prompt_and_maybe_more_initial_messages_from_remote(gcx.clone(), &messages, chat_meta, stream_back_to_user).await {
            Ok(messages_from_remote) => messages_from_remote,
            Err(e) => {
                tracing::error!("prepend_the_right_system_prompt_and_maybe_more_initial_messages_from_remote: {}", e);
                messages
            },
        };
        return messages;
    }

    match chat_meta.chat_mode {
        ChatMode::EXPLORE | ChatMode::AGENT | ChatMode::NO_TOOLS => {
            let system_message_content = system_prompt_add_extra_instructions(
                gcx.clone(),
                get_default_system_prompt(gcx.clone(), chat_meta.chat_mode.clone()).await,
                tool_names,
            ).await;
            let msg = ChatMessage {
                role: "system".to_string(),
                content: ChatContent::SimpleText(system_message_content),
                ..Default::default()
            };
            stream_back_to_user.push_in_json(serde_json::json!(msg));
            messages.insert(0, msg);
        },
        ChatMode::CONFIGURE => {
            crate::integrations::config_chat::mix_config_messages(
                gcx.clone(),
                &chat_meta,
                &mut messages,
                stream_back_to_user,
            ).await;
        },
        ChatMode::PROJECT_SUMMARY => {
            crate::integrations::project_summary_chat::mix_project_summary_messages(
                gcx.clone(),
                &chat_meta,
                &mut messages,
                stream_back_to_user,
            ).await;
        },
    }
    tracing::info!("\n\nSYSTEM PROMPT MIXER chat_mode={:?}\n{:#?}", chat_meta.chat_mode, messages);
    messages
}

pub async fn prepend_system_prompt_and_maybe_more_initial_messages_from_remote(
    gcx: Arc<ARwLock<GlobalContext>>,
    messages: &[call_validation::ChatMessage],
    chat_meta: &call_validation::ChatMeta,
    stream_back_to_user: &mut HasRagResults,
) -> Result<Vec<call_validation::ChatMessage>, String> {
    let post = PrependSystemPromptPost {
        messages: messages.to_vec(),
        chat_meta: chat_meta.clone(),
    };

    let port = docker_container_get_host_lsp_port_to_connect(gcx.clone(), &chat_meta.chat_id).await?;
    let url = format!("http://localhost:{port}/v1/prepend-system-prompt-and-maybe-more-initial-messages");
    let response: PrependSystemPromptResponse = http_post_json(&url, &post).await?;

    for msg in response.messages_to_stream_back {
        stream_back_to_user.push_in_json(msg);
    }

    Ok(response.messages)
}
