use std::collections::VecDeque;
use std::path::PathBuf;
use tree_sitter::{Language, Node, Parser, Tree};
use tree_sitter_java::language;
use uuid::Uuid;

use crate::ast::treesitter::ast_instance_structs::{AstSymbolFields, AstSymbolInstanceArc, CommentDefinition};
use crate::ast::treesitter::ast_instance_structs::{TypeDef};
use crate::ast::treesitter::ast_instance_structs::{self};
use crate::ast::treesitter::language_id::LanguageId;
use crate::ast::treesitter::parsers::ParserError;
use crate::ast::treesitter::parsers::AstLanguageParser;
use crate::ast::treesitter::parsers::utils::{CandidateInfo, get_guid};

extern "C" { fn tree_sitter_html() -> Language; }

pub struct HtmlParser {
    language: Language,
}

impl HtmlParser {
    pub fn new() -> Result<Self, ParserError> {
        unsafe {
            Ok(HtmlParser {
                language: tree_sitter_html(),
            })
        }
    }
}

impl AstLanguageParser for HtmlParser {
    fn parse(&mut self, code: &str, path: &PathBuf) -> Vec<AstSymbolInstanceArc> {
        let mut parser = Parser::new();
        parser.set_language(&language()).unwrap();
        let tree = match parser.parse(code, None) {
            Some(tree) => tree,
            None => return vec![],
        };
        let root_node = tree.root_node();
        let mut symbols = vec![];
        let mut queue = VecDeque::new();
        let root_guid = get_guid();
        let ast_fields = AstSymbolFields {
            guid: root_guid,
            name: "<html-root>".to_string(),
            language: LanguageId::Html,
            file_path: path.clone(),
            namespace: "".to_string(),
            parent_guid: None,
            childs_guid: vec![],
            full_range: root_node.range(),
            declaration_range: root_node.range(),
            definition_range: root_node.range(),
            linked_decl_guid: None,
            linked_decl_type: None,
            caller_guid: None,
            is_error: false,
            caller_depth: None,
        };
        queue.push_back(CandidateInfo {
            ast_fields: ast_fields.clone(),
            node: root_node,
            parent_guid: root_guid,
        });

        while let Some(candidate) = queue.pop_front() {
            let node = candidate.node;
            let kind = node.kind();
            match kind {
                "element" => {
                    let tag_name = node.child_by_field_name("tag_name").map(|n| n.utf8_text(code.as_bytes()).unwrap_or("")).unwrap_or("").to_string();
                    let mut fields = candidate.ast_fields.clone();
                    fields.name = tag_name.clone();
                    fields.language = LanguageId::Html;
                    fields.full_range = node.range();
                    fields.declaration_range = node.range();
                    fields.definition_range = node.range();
                    fields.parent_guid = Some(candidate.parent_guid);
                    let symbol = ast_instance_structs::CommentDefinition { ast_fields: fields.clone() }; // Use CommentDefinition as placeholder
                    let arc = std::sync::Arc::new(parking_lot::RwLock::new(Box::new(symbol) as Box<dyn ast_instance_structs::AstSymbolInstance>));
                    symbols.push(arc.clone());
                    // Enqueue children
                    for i in 0..node.child_count() {
                        let child = node.child(i).unwrap();
                        queue.push_back(CandidateInfo {
                            ast_fields: fields.clone(),
                            node: child,
                            parent_guid: fields.guid,
                        });
                    }
                }
                "text" => {
                    let mut fields = candidate.ast_fields.clone();
                    fields.name = "#text".to_string();
                    fields.language = LanguageId::Html;
                    fields.full_range = node.range();
                    fields.declaration_range = node.range();
                    fields.definition_range = node.range();
                    fields.parent_guid = Some(candidate.parent_guid);
                    let symbol = ast_instance_structs::CommentDefinition { ast_fields: fields.clone() }; // Use CommentDefinition as placeholder
                    let arc = std::sync::Arc::new(parking_lot::RwLock::new(Box::new(symbol) as Box<dyn ast_instance_structs::AstSymbolInstance>));
                    symbols.push(arc);
                }
                "attribute" => {
                    let attr_name = node.child_by_field_name("name").map(|n| n.utf8_text(code.as_bytes()).unwrap_or("")).unwrap_or("").to_string();
                    let mut fields = candidate.ast_fields.clone();
                    fields.name = attr_name.clone();
                    fields.language = LanguageId::Html;
                    fields.full_range = node.range();
                    fields.declaration_range = node.range();
                    fields.definition_range = node.range();
                    fields.parent_guid = Some(candidate.parent_guid);
                    let symbol = ast_instance_structs::CommentDefinition { ast_fields: fields.clone() }; // Use CommentDefinition as placeholder
                    let arc = std::sync::Arc::new(parking_lot::RwLock::new(Box::new(symbol) as Box<dyn ast_instance_structs::AstSymbolInstance>));
                    symbols.push(arc);
                }
                _ => {
                    // Enqueue children for all other nodes
                    for i in 0..node.child_count() {
                        let child = node.child(i).unwrap();
                        queue.push_back(CandidateInfo {
                            ast_fields: candidate.ast_fields.clone(),
                            node: child,
                            parent_guid: candidate.parent_guid,
                        });
                    }
                }
            }
        }
        symbols
    }
}
