import java.util.ArrayList;
import java.util.*;
import com.github.pip;

interface Animal {
  public void animalSound(); // interface method (does not have a body)
  public void run(); // interface method (does not have a body)
}

interface a{}

//Java Program to illustrate how to define a class and fields  
//Defining a Student class.  
class Student<A extends X, B extends Y> extends Animal implements PI1, PI2 {  
 //defining fields  
 int id;//field or data member or instance variable  
 String name ="asd";  
 //creating main method inside the Student class  
 public static void pip(String args[]){  
  //Creating an object or instance  
  Student s1=new Student();//creating an object of Student  
  //Printing values of the object  
  System.out.println(s1.id);//accessing member through reference variable  
  System.out.println(s1.name);  
 }  
}  

void a() {
}

int as =2, sad =2 ;
Poo<s> as =2 ;
float asd = 2;
Poo qwe = 2;

public class Main {
  enum Level {
    H("Hydrogen", 1, 1.008f),
    HE("Helium", 2, 4.0026f),
    // ...
    NE("Neon", 10, 20.180f);

    private static final Map<String, Element> BY_LABEL = new HashMap<>();
    
    
    public static Element valueOfLabel(String label) {
        return BY_LABEL.get(label);
    }
  }

  public static void main(String[] args) {
  a::a = 2;
    String[] cars = {"Volvo", "BMW", "Ford", "Mazda"};
    System.out.println(cars.length);
  }
}