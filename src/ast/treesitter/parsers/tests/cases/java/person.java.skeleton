[{"line": "public class Person {\n  private String name;\n  private int age;\n  public Person(String name, int age) { ... }\n  public abstract void a();\n  public String getName() { ... }\n  public void setName(String name) { ... }\n  public int getAge() { ... }\n  public void setAge(int age) { ... }\n  @Override\n  public String toString() { ... }\n  public static void main(String[] args) { ... }\n}"}]