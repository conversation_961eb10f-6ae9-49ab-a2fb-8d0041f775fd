[{"ImportDeclaration": {"ast_fields": {"guid": "be745583-5a1d-4412-b706-a9d434208d64", "name": "foo", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "eaca833b-b93d-4fbd-a300-7a61c1c9faa4", "childs_guid": [], "full_range": {"start_byte": 0, "end_byte": 45, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 45}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}, "path_components": [".", "module.js", "foo"], "alias": "ds", "import_type": "UserModule", "filepath_ref": null}}, {"ImportDeclaration": {"ast_fields": {"guid": "0e04b5e5-7f34-436d-9655-588956d62a85", "name": "bar", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "eaca833b-b93d-4fbd-a300-7a61c1c9faa4", "childs_guid": [], "full_range": {"start_byte": 0, "end_byte": 45, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 45}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}, "path_components": [".", "module.js", "bar"], "alias": null, "import_type": "UserModule", "filepath_ref": null}}, {"ImportDeclaration": {"ast_fields": {"guid": "c56ef4f7-1ad0-4d01-8907-be087a44cebe", "name": "moduleName", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "eaca833b-b93d-4fbd-a300-7a61c1c9faa4", "childs_guid": [], "full_range": {"start_byte": 46, "end_byte": 88, "start_point": {"row": 1, "column": 0}, "end_point": {"row": 1, "column": 42}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}, "path_components": [".", "module.js"], "alias": "moduleName", "import_type": "UserModule", "filepath_ref": null}}, {"CommentDefinition": {"ast_fields": {"guid": "618380b4-dd55-4384-9d93-10f5a05355fc", "name": "", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "eaca833b-b93d-4fbd-a300-7a61c1c9faa4", "childs_guid": [], "full_range": {"start_byte": 90, "end_byte": 102, "start_point": {"row": 3, "column": 0}, "end_point": {"row": 3, "column": 12}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}}}, {"CommentDefinition": {"ast_fields": {"guid": "437ce5a1-8ba2-443f-994f-9e03d5afd4f8", "name": "", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "eaca833b-b93d-4fbd-a300-7a61c1c9faa4", "childs_guid": [], "full_range": {"start_byte": 158, "end_byte": 169, "start_point": {"row": 8, "column": 0}, "end_point": {"row": 8, "column": 11}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}}}, {"FunctionDeclaration": {"ast_fields": {"guid": "c7c264a9-601b-46e0-b2d9-1feac5907200", "name": "greet", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "eaca833b-b93d-4fbd-a300-7a61c1c9faa4", "childs_guid": ["000a17f8-59b5-46bf-837f-845a73df6da7"], "full_range": {"start_byte": 170, "end_byte": 223, "start_point": {"row": 9, "column": 0}, "end_point": {"row": 11, "column": 1}}, "declaration_range": {"start_byte": 170, "end_byte": 191, "start_point": {"row": 9, "column": 0}, "end_point": {"row": 9, "column": 21}}, "definition_range": {"start_byte": 191, "end_byte": 223, "start_point": {"row": 9, "column": 21}, "end_point": {"row": 11, "column": 1}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}, "template_types": [], "args": [{"name": "name", "type_": null}], "return_type": null}}, {"CommentDefinition": {"ast_fields": {"guid": "3255a1bf-b225-4930-9f4c-5bbedf7efe0b", "name": "", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "eaca833b-b93d-4fbd-a300-7a61c1c9faa4", "childs_guid": [], "full_range": {"start_byte": 322, "end_byte": 336, "start_point": {"row": 19, "column": 24}, "end_point": {"row": 19, "column": 38}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}}}, {"CommentDefinition": {"ast_fields": {"guid": "f4e67edb-a3ae-44d4-ad38-3945d16cd16d", "name": "", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "eaca833b-b93d-4fbd-a300-7a61c1c9faa4", "childs_guid": [], "full_range": {"start_byte": 364, "end_byte": 390, "start_point": {"row": 21, "column": 26}, "end_point": {"row": 21, "column": 52}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}}}, {"CommentDefinition": {"ast_fields": {"guid": "11d88d04-7da3-47b6-b26f-41dd5a525b82", "name": "", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "eaca833b-b93d-4fbd-a300-7a61c1c9faa4", "childs_guid": [], "full_range": {"start_byte": 392, "end_byte": 401, "start_point": {"row": 23, "column": 0}, "end_point": {"row": 23, "column": 9}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}}}, {"CommentDefinition": {"ast_fields": {"guid": "513da17d-c251-4750-ad75-64960ffded3a", "name": "", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "eaca833b-b93d-4fbd-a300-7a61c1c9faa4", "childs_guid": [], "full_range": {"start_byte": 550, "end_byte": 564, "start_point": {"row": 32, "column": 0}, "end_point": {"row": 32, "column": 14}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}}}, {"StructDeclaration": {"ast_fields": {"guid": "781f51ed-e001-460b-8c0b-92c102a16df2", "name": "Rectangle", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "eaca833b-b93d-4fbd-a300-7a61c1c9faa4", "childs_guid": ["9ec5e55c-aa64-4a16-9791-79dc7415f1db"], "full_range": {"start_byte": 565, "end_byte": 681, "start_point": {"row": 33, "column": 0}, "end_point": {"row": 38, "column": 1}}, "declaration_range": {"start_byte": 565, "end_byte": 580, "start_point": {"row": 33, "column": 0}, "end_point": {"row": 33, "column": 15}}, "definition_range": {"start_byte": 581, "end_byte": 681, "start_point": {"row": 33, "column": 16}, "end_point": {"row": 38, "column": 1}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}, "template_types": [], "inherited_types": []}}, {"CommentDefinition": {"ast_fields": {"guid": "e7b90b66-7f66-4529-9333-61a6abcc22ad", "name": "", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "eaca833b-b93d-4fbd-a300-7a61c1c9faa4", "childs_guid": [], "full_range": {"start_byte": 683, "end_byte": 747, "start_point": {"row": 40, "column": 0}, "end_point": {"row": 40, "column": 64}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}}}, {"CommentDefinition": {"ast_fields": {"guid": "8d6b258f-9d66-45dd-9931-59e603bb79a7", "name": "", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "eaca833b-b93d-4fbd-a300-7a61c1c9faa4", "childs_guid": [], "full_range": {"start_byte": 905, "end_byte": 946, "start_point": {"row": 50, "column": 0}, "end_point": {"row": 50, "column": 41}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}}}, {"CommentDefinition": {"ast_fields": {"guid": "d9589f7f-2bb1-456c-96fd-2a3b3b5e012a", "name": "", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "eaca833b-b93d-4fbd-a300-7a61c1c9faa4", "childs_guid": [], "full_range": {"start_byte": 1147, "end_byte": 1166, "start_point": {"row": 61, "column": 32}, "end_point": {"row": 61, "column": 51}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}}}, {"CommentDefinition": {"ast_fields": {"guid": "1adaacd9-2b93-4814-992c-622d9b211302", "name": "", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "eaca833b-b93d-4fbd-a300-7a61c1c9faa4", "childs_guid": [], "full_range": {"start_byte": 1186, "end_byte": 1194, "start_point": {"row": 64, "column": 0}, "end_point": {"row": 64, "column": 8}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}}}, {"CommentDefinition": {"ast_fields": {"guid": "195b2c5a-974a-418e-8359-80e91abf8f6c", "name": "", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "eaca833b-b93d-4fbd-a300-7a61c1c9faa4", "childs_guid": [], "full_range": {"start_byte": 1320, "end_byte": 1334, "start_point": {"row": 70, "column": 0}, "end_point": {"row": 70, "column": 14}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}}}, {"CommentDefinition": {"ast_fields": {"guid": "7cc2f819-c7da-43aa-a3d2-f219a616bb41", "name": "", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "eaca833b-b93d-4fbd-a300-7a61c1c9faa4", "childs_guid": [], "full_range": {"start_byte": 1438, "end_byte": 1445, "start_point": {"row": 77, "column": 0}, "end_point": {"row": 77, "column": 7}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}}}, {"CommentDefinition": {"ast_fields": {"guid": "fe9aacd4-40d6-44c4-bfb6-bc27b1d1d0f1", "name": "", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "eaca833b-b93d-4fbd-a300-7a61c1c9faa4", "childs_guid": [], "full_range": {"start_byte": 1499, "end_byte": 1507, "start_point": {"row": 82, "column": 0}, "end_point": {"row": 82, "column": 8}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}}}, {"CommentDefinition": {"ast_fields": {"guid": "15aeec64-3332-4ab7-a40c-10d60ebb9ce2", "name": "", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "eaca833b-b93d-4fbd-a300-7a61c1c9faa4", "childs_guid": [], "full_range": {"start_byte": 1618, "end_byte": 1635, "start_point": {"row": 87, "column": 0}, "end_point": {"row": 87, "column": 17}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}}}, {"StructDeclaration": {"ast_fields": {"guid": "8affd81e-87a6-44fb-b38e-11ee353897e4", "name": "Person", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "eaca833b-b93d-4fbd-a300-7a61c1c9faa4", "childs_guid": ["2c523358-8aae-4a32-ab42-b0e024e3f985", "961b6faf-f354-4be6-aee4-e4e7758f4041", "f3b3cfe9-204b-46d5-a356-7f2eca8fafa1"], "full_range": {"start_byte": 1636, "end_byte": 1858, "start_point": {"row": 88, "column": 0}, "end_point": {"row": 98, "column": 1}}, "declaration_range": {"start_byte": 1636, "end_byte": 1648, "start_point": {"row": 88, "column": 0}, "end_point": {"row": 88, "column": 12}}, "definition_range": {"start_byte": 1649, "end_byte": 1858, "start_point": {"row": 88, "column": 13}, "end_point": {"row": 98, "column": 1}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}, "template_types": [], "inherited_types": []}}, {"CommentDefinition": {"ast_fields": {"guid": "8cc25b88-1f56-4d7a-acd3-93099e23b332", "name": "", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "eaca833b-b93d-4fbd-a300-7a61c1c9faa4", "childs_guid": [], "full_range": {"start_byte": 1860, "end_byte": 1894, "start_point": {"row": 100, "column": 0}, "end_point": {"row": 100, "column": 34}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}}}, {"CommentDefinition": {"ast_fields": {"guid": "2732b05d-165f-4b05-b77c-b6ae69d4e7de", "name": "", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "eaca833b-b93d-4fbd-a300-7a61c1c9faa4", "childs_guid": [], "full_range": {"start_byte": 1964, "end_byte": 1983, "start_point": {"row": 103, "column": 30}, "end_point": {"row": 103, "column": 49}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}}}, {"CommentDefinition": {"ast_fields": {"guid": "36d47022-ad19-486a-bff7-024e4445c527", "name": "", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "eaca833b-b93d-4fbd-a300-7a61c1c9faa4", "childs_guid": [], "full_range": {"start_byte": 1985, "end_byte": 1999, "start_point": {"row": 105, "column": 0}, "end_point": {"row": 105, "column": 14}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}}}, {"StructDeclaration": {"ast_fields": {"guid": "f398f09d-b2ae-47f7-8773-1c43cdb48d24", "name": "Employee", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "eaca833b-b93d-4fbd-a300-7a61c1c9faa4", "childs_guid": ["77407ebc-eda5-4870-8846-4058b7ca9d2e", "79a533cf-1e89-4489-962d-6ae0f6057f22", "e3d799ed-fea6-4628-8e28-70a28b69257a"], "full_range": {"start_byte": 2000, "end_byte": 2292, "start_point": {"row": 106, "column": 0}, "end_point": {"row": 116, "column": 1}}, "declaration_range": {"start_byte": 2000, "end_byte": 2029, "start_point": {"row": 106, "column": 0}, "end_point": {"row": 106, "column": 29}}, "definition_range": {"start_byte": 2030, "end_byte": 2292, "start_point": {"row": 106, "column": 30}, "end_point": {"row": 116, "column": 1}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}, "template_types": [], "inherited_types": [{"name": "Person", "inference_info": null, "is_pod": false, "namespace": "", "guid": null, "nested_types": []}]}}, {"CommentDefinition": {"ast_fields": {"guid": "e6180210-908c-41df-9473-42be95b2ab4a", "name": "", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "eaca833b-b93d-4fbd-a300-7a61c1c9faa4", "childs_guid": [], "full_range": {"start_byte": 2377, "end_byte": 2406, "start_point": {"row": 120, "column": 30}, "end_point": {"row": 120, "column": 59}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}}}, {"CommentDefinition": {"ast_fields": {"guid": "1b62c053-db38-4e54-b55b-219e6ae2b332", "name": "", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "eaca833b-b93d-4fbd-a300-7a61c1c9faa4", "childs_guid": [], "full_range": {"start_byte": 2408, "end_byte": 2455, "start_point": {"row": 122, "column": 0}, "end_point": {"row": 122, "column": 47}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}}}, {"FunctionDeclaration": {"ast_fields": {"guid": "b103362c-c908-4463-93e3-8ed8719bd34d", "name": "add", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "eaca833b-b93d-4fbd-a300-7a61c1c9faa4", "childs_guid": ["1aa8bda7-cb86-4131-93a6-10df64ddf49c", "a5b320ae-a98c-4e3b-91c2-947e7f5f328d"], "full_range": {"start_byte": 2456, "end_byte": 2496, "start_point": {"row": 123, "column": 0}, "end_point": {"row": 125, "column": 1}}, "declaration_range": {"start_byte": 2456, "end_byte": 2475, "start_point": {"row": 123, "column": 0}, "end_point": {"row": 123, "column": 19}}, "definition_range": {"start_byte": 2475, "end_byte": 2496, "start_point": {"row": 123, "column": 19}, "end_point": {"row": 125, "column": 1}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}, "template_types": [], "args": [{"name": "a", "type_": null}, {"name": "b", "type_": null}], "return_type": null}}, {"CommentDefinition": {"ast_fields": {"guid": "2173b787-942f-4c87-86cb-cea82992974b", "name": "", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "eaca833b-b93d-4fbd-a300-7a61c1c9faa4", "childs_guid": [], "full_range": {"start_byte": 2531, "end_byte": 2544, "start_point": {"row": 127, "column": 24}, "end_point": {"row": 127, "column": 37}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}}}, {"CommentDefinition": {"ast_fields": {"guid": "c9db865e-371b-40e6-b222-bf154047763a", "name": "", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "eaca833b-b93d-4fbd-a300-7a61c1c9faa4", "childs_guid": [], "full_range": {"start_byte": 2546, "end_byte": 2568, "start_point": {"row": 129, "column": 0}, "end_point": {"row": 129, "column": 22}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}}}, {"CommentDefinition": {"ast_fields": {"guid": "237e7996-c8df-4269-aac8-ce66a2536e96", "name": "", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "eaca833b-b93d-4fbd-a300-7a61c1c9faa4", "childs_guid": [], "full_range": {"start_byte": 2650, "end_byte": 2663, "start_point": {"row": 133, "column": 29}, "end_point": {"row": 133, "column": 42}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}}}, {"CommentDefinition": {"ast_fields": {"guid": "5d50d992-5f60-4199-b911-3a291ac2e6a6", "name": "", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "eaca833b-b93d-4fbd-a300-7a61c1c9faa4", "childs_guid": [], "full_range": {"start_byte": 2665, "end_byte": 2682, "start_point": {"row": 135, "column": 0}, "end_point": {"row": 135, "column": 17}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}}}, {"CommentDefinition": {"ast_fields": {"guid": "d974bc08-a3f1-44a4-b888-910d23e28ccd", "name": "", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "eaca833b-b93d-4fbd-a300-7a61c1c9faa4", "childs_guid": [], "full_range": {"start_byte": 2759, "end_byte": 2772, "start_point": {"row": 139, "column": 29}, "end_point": {"row": 139, "column": 42}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}}}, {"CommentDefinition": {"ast_fields": {"guid": "1f40c9ec-b1da-4932-bfa1-29c07c4794d8", "name": "", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "eaca833b-b93d-4fbd-a300-7a61c1c9faa4", "childs_guid": [], "full_range": {"start_byte": 2774, "end_byte": 2823, "start_point": {"row": 141, "column": 0}, "end_point": {"row": 141, "column": 49}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}}}, {"CommentDefinition": {"ast_fields": {"guid": "8c958b61-7c72-455c-a218-1b92cb8026fb", "name": "", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "eaca833b-b93d-4fbd-a300-7a61c1c9faa4", "childs_guid": [], "full_range": {"start_byte": 2880, "end_byte": 2907, "start_point": {"row": 144, "column": 6}, "end_point": {"row": 144, "column": 33}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}}}, {"CommentDefinition": {"ast_fields": {"guid": "bc665d4c-be3a-4b41-81ae-7edf4f022a50", "name": "", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "eaca833b-b93d-4fbd-a300-7a61c1c9faa4", "childs_guid": [], "full_range": {"start_byte": 2909, "end_byte": 2932, "start_point": {"row": 146, "column": 0}, "end_point": {"row": 146, "column": 23}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}}}, {"FunctionDeclaration": {"ast_fields": {"guid": "a5b70c6f-dfa8-46f8-95a7-181dc7343258", "name": "Person", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "eaca833b-b93d-4fbd-a300-7a61c1c9faa4", "childs_guid": ["94e96f7c-9aad-4bef-8e12-4a04a832e226", "acffa283-bc27-418d-9f3b-94eafe0ee9ee", "a5f42d75-f60f-49d0-992e-91f39dabd48e", "d92d5735-5d67-4fed-b2ea-4ef4ca37f16d"], "full_range": {"start_byte": 2933, "end_byte": 3005, "start_point": {"row": 147, "column": 0}, "end_point": {"row": 150, "column": 1}}, "declaration_range": {"start_byte": 2933, "end_byte": 2960, "start_point": {"row": 147, "column": 0}, "end_point": {"row": 147, "column": 27}}, "definition_range": {"start_byte": 2960, "end_byte": 3005, "start_point": {"row": 147, "column": 27}, "end_point": {"row": 150, "column": 1}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}, "template_types": [], "args": [{"name": "name", "type_": null}, {"name": "age", "type_": null}], "return_type": null}}, {"CommentDefinition": {"ast_fields": {"guid": "0b713e39-3d30-4844-9fa1-fff3bb6eb981", "name": "", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "eaca833b-b93d-4fbd-a300-7a61c1c9faa4", "childs_guid": [], "full_range": {"start_byte": 3061, "end_byte": 3105, "start_point": {"row": 153, "column": 19}, "end_point": {"row": 153, "column": 63}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}}}, {"CommentDefinition": {"ast_fields": {"guid": "b15afe40-59a9-414b-87ed-11b44486dda0", "name": "", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "eaca833b-b93d-4fbd-a300-7a61c1c9faa4", "childs_guid": [], "full_range": {"start_byte": 3107, "end_byte": 3128, "start_point": {"row": 155, "column": 0}, "end_point": {"row": 155, "column": 21}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}}}, {"CommentDefinition": {"ast_fields": {"guid": "ec06c457-94a2-4bc9-ba6e-a7cb51755dc5", "name": "", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "eaca833b-b93d-4fbd-a300-7a61c1c9faa4", "childs_guid": [], "full_range": {"start_byte": 3275, "end_byte": 3288, "start_point": {"row": 164, "column": 31}, "end_point": {"row": 164, "column": 44}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}}}, {"CommentDefinition": {"ast_fields": {"guid": "c114cc93-4cae-47d8-8663-2f115aa9eae0", "name": "", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "eaca833b-b93d-4fbd-a300-7a61c1c9faa4", "childs_guid": [], "full_range": {"start_byte": 3320, "end_byte": 3333, "start_point": {"row": 165, "column": 31}, "end_point": {"row": 165, "column": 44}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}}}, {"ImportDeclaration": {"ast_fields": {"guid": "28f289c2-7531-4acc-ab2f-64f3dd11715f", "name": "React", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "eaca833b-b93d-4fbd-a300-7a61c1c9faa4", "childs_guid": [], "full_range": {"start_byte": 3335, "end_byte": 3361, "start_point": {"row": 167, "column": 0}, "end_point": {"row": 167, "column": 26}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}, "path_components": ["react", "React"], "alias": null, "import_type": "Unknown", "filepath_ref": null}}, {"StructDeclaration": {"ast_fields": {"guid": "0653923c-a62e-4e2c-9197-e6759fa9c2f2", "name": "HelloWorld", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "eaca833b-b93d-4fbd-a300-7a61c1c9faa4", "childs_guid": ["2ff80ba3-1674-40d0-95b0-ca561c4243d1"], "full_range": {"start_byte": 3363, "end_byte": 3573, "start_point": {"row": 169, "column": 0}, "end_point": {"row": 178, "column": 1}}, "declaration_range": {"start_byte": 3363, "end_byte": 3403, "start_point": {"row": 169, "column": 0}, "end_point": {"row": 169, "column": 40}}, "definition_range": {"start_byte": 3404, "end_byte": 3573, "start_point": {"row": 169, "column": 41}, "end_point": {"row": 178, "column": 1}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}, "template_types": [], "inherited_types": []}}, {"VariableDefinition": {"ast_fields": {"guid": "19d7c20a-fdf3-438c-8e4f-b70667186caf", "name": "name", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "eaca833b-b93d-4fbd-a300-7a61c1c9faa4", "childs_guid": [], "full_range": {"start_byte": 107, "end_byte": 124, "start_point": {"row": 4, "column": 4}, "end_point": {"row": 4, "column": 21}}, "declaration_range": {"start_byte": 107, "end_byte": 124, "start_point": {"row": 4, "column": 4}, "end_point": {"row": 4, "column": 21}}, "definition_range": {"start_byte": 107, "end_byte": 124, "start_point": {"row": 4, "column": 4}, "end_point": {"row": 4, "column": 21}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}, "type_": {"name": null, "inference_info": "\"<PERSON>\"", "is_pod": true, "namespace": "", "guid": null, "nested_types": []}}}, {"VariableDefinition": {"ast_fields": {"guid": "4f358ea8-5de5-4920-836d-1fd3ed678d1a", "name": "age", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "eaca833b-b93d-4fbd-a300-7a61c1c9faa4", "childs_guid": [], "full_range": {"start_byte": 130, "end_byte": 138, "start_point": {"row": 5, "column": 4}, "end_point": {"row": 5, "column": 12}}, "declaration_range": {"start_byte": 130, "end_byte": 138, "start_point": {"row": 5, "column": 4}, "end_point": {"row": 5, "column": 12}}, "definition_range": {"start_byte": 130, "end_byte": 138, "start_point": {"row": 5, "column": 4}, "end_point": {"row": 5, "column": 12}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}, "type_": {"name": null, "inference_info": "30", "is_pod": true, "namespace": "", "guid": null, "nested_types": []}}}, {"VariableDefinition": {"ast_fields": {"guid": "410d47f7-117c-4535-8c4c-ec35c1882318", "name": "pi", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "eaca833b-b93d-4fbd-a300-7a61c1c9faa4", "childs_guid": [], "full_range": {"start_byte": 146, "end_byte": 155, "start_point": {"row": 6, "column": 6}, "end_point": {"row": 6, "column": 15}}, "declaration_range": {"start_byte": 146, "end_byte": 155, "start_point": {"row": 6, "column": 6}, "end_point": {"row": 6, "column": 15}}, "definition_range": {"start_byte": 146, "end_byte": 155, "start_point": {"row": 6, "column": 6}, "end_point": {"row": 6, "column": 15}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}, "type_": {"name": null, "inference_info": "3.14", "is_pod": true, "namespace": "", "guid": null, "nested_types": []}}}, {"VariableDefinition": {"ast_fields": {"guid": "82e6abec-6988-4e43-8ee3-52c456c805e4", "name": "Color", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "eaca833b-b93d-4fbd-a300-7a61c1c9faa4", "childs_guid": [], "full_range": {"start_byte": 231, "end_byte": 295, "start_point": {"row": 13, "column": 6}, "end_point": {"row": 17, "column": 1}}, "declaration_range": {"start_byte": 231, "end_byte": 295, "start_point": {"row": 13, "column": 6}, "end_point": {"row": 17, "column": 1}}, "definition_range": {"start_byte": 231, "end_byte": 295, "start_point": {"row": 13, "column": 6}, "end_point": {"row": 17, "column": 1}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}, "type_": {"name": null, "inference_info": "{\n    RED: 'Red',\n    BLUE: 'Blue',\n    GREEN: 'Green'\n}", "is_pod": false, "namespace": "", "guid": null, "nested_types": []}}}, {"FunctionCall": {"ast_fields": {"guid": "eb67e06c-6abe-479f-8d76-2deba5afac96", "name": "log", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "eaca833b-b93d-4fbd-a300-7a61c1c9faa4", "childs_guid": [], "full_range": {"start_byte": 298, "end_byte": 320, "start_point": {"row": 19, "column": 0}, "end_point": {"row": 19, "column": 22}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": "1584d618-c850-44c8-8b0f-1f2ed854df4c", "is_error": false}, "template_types": []}}, {"FunctionCall": {"ast_fields": {"guid": "91155420-7b2a-4fcc-a6e5-677ad58022d6", "name": "log", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "eaca833b-b93d-4fbd-a300-7a61c1c9faa4", "childs_guid": [], "full_range": {"start_byte": 338, "end_byte": 362, "start_point": {"row": 21, "column": 0}, "end_point": {"row": 21, "column": 24}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": "efdbb33f-5852-4e0a-b715-ab53e0cff08f", "is_error": false}, "template_types": []}}, {"VariableDefinition": {"ast_fields": {"guid": "739003fb-a694-4898-9ca6-8ec3333b558a", "name": "person", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "eaca833b-b93d-4fbd-a300-7a61c1c9faa4", "childs_guid": [], "full_range": {"start_byte": 406, "end_byte": 548, "start_point": {"row": 24, "column": 4}, "end_point": {"row": 30, "column": 1}}, "declaration_range": {"start_byte": 406, "end_byte": 548, "start_point": {"row": 24, "column": 4}, "end_point": {"row": 30, "column": 1}}, "definition_range": {"start_byte": 406, "end_byte": 548, "start_point": {"row": 24, "column": 4}, "end_point": {"row": 30, "column": 1}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}, "type_": {"name": null, "inference_info": "{\n    firstName: \"<PERSON>\",\n    lastName: \"<PERSON><PERSON>\",\n    fullName: function() {\n        return this.firstName + \" \" + this.lastName;\n    }\n}", "is_pod": false, "namespace": "", "guid": null, "nested_types": []}}}, {"StructDeclaration": {"ast_fields": {"guid": "f9004947-4af7-4e2e-90fe-fb555dd4a017", "name": "Rectangle", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "eaca833b-b93d-4fbd-a300-7a61c1c9faa4", "childs_guid": ["33bbcfc4-e021-45c9-a96f-6b93eedf19db", "6d49e4e1-5140-43c4-aa2e-651ceb43786f", "0c8b553f-5c6a-405b-a3f8-78c4e0f6c07f"], "full_range": {"start_byte": 766, "end_byte": 902, "start_point": {"row": 41, "column": 18}, "end_point": {"row": 48, "column": 1}}, "declaration_range": {"start_byte": 766, "end_byte": 771, "start_point": {"row": 41, "column": 18}, "end_point": {"row": 41, "column": 23}}, "definition_range": {"start_byte": 772, "end_byte": 902, "start_point": {"row": 41, "column": 24}, "end_point": {"row": 48, "column": 1}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}, "template_types": [], "inherited_types": []}}, {"StructDeclaration": {"ast_fields": {"guid": "6906eff0-26f6-4636-91f5-485b9024a58f", "name": "Rectangle2", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "eaca833b-b93d-4fbd-a300-7a61c1c9faa4", "childs_guid": ["b766efe9-b431-4a6a-8344-a7576914f406", "2de4f758-7b47-4326-91e2-381fa481d2a8", "c4ca4f4a-39fc-433b-966f-f5b28ec22396"], "full_range": {"start_byte": 965, "end_byte": 1111, "start_point": {"row": 51, "column": 18}, "end_point": {"row": 58, "column": 1}}, "declaration_range": {"start_byte": 965, "end_byte": 981, "start_point": {"row": 51, "column": 18}, "end_point": {"row": 51, "column": 34}}, "definition_range": {"start_byte": 982, "end_byte": 1111, "start_point": {"row": 51, "column": 35}, "end_point": {"row": 58, "column": 1}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}, "template_types": [], "inherited_types": []}}, {"FunctionCall": {"ast_fields": {"guid": "ecd50fca-7808-4c5b-b483-8bb1c2c63cca", "name": "log", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "eaca833b-b93d-4fbd-a300-7a61c1c9faa4", "childs_guid": [], "full_range": {"start_byte": 1115, "end_byte": 1145, "start_point": {"row": 61, "column": 0}, "end_point": {"row": 61, "column": 30}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": "a35712a2-ecc9-4d60-8cde-4ea6aabc3bd8", "is_error": false}, "template_types": []}}, {"VariableDefinition": {"ast_fields": {"guid": "8018d9fe-03f1-4ac9-b9a4-8438d4dc3354", "name": "asd", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "eaca833b-b93d-4fbd-a300-7a61c1c9faa4", "childs_guid": [], "full_range": {"start_byte": 1171, "end_byte": 1183, "start_point": {"row": 62, "column": 4}, "end_point": {"row": 62, "column": 16}}, "declaration_range": {"start_byte": 1171, "end_byte": 1183, "start_point": {"row": 62, "column": 4}, "end_point": {"row": 62, "column": 16}}, "definition_range": {"start_byte": 1171, "end_byte": 1183, "start_point": {"row": 62, "column": 4}, "end_point": {"row": 62, "column": 16}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}, "type_": {"name": null, "inference_info": "person", "is_pod": false, "namespace": "", "guid": null, "nested_types": []}}}, {"VariableDefinition": {"ast_fields": {"guid": "8bfae283-8b94-492e-bd69-3c4a7b94f206", "name": "fruits", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "eaca833b-b93d-4fbd-a300-7a61c1c9faa4", "childs_guid": [], "full_range": {"start_byte": 1199, "end_byte": 1237, "start_point": {"row": 65, "column": 4}, "end_point": {"row": 65, "column": 42}}, "declaration_range": {"start_byte": 1199, "end_byte": 1237, "start_point": {"row": 65, "column": 4}, "end_point": {"row": 65, "column": 42}}, "definition_range": {"start_byte": 1199, "end_byte": 1237, "start_point": {"row": 65, "column": 4}, "end_point": {"row": 65, "column": 42}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}, "type_": {"name": null, "inference_info": "[\"apple\", \"banana\", \"cherry\"]", "is_pod": false, "namespace": "", "guid": null, "nested_types": []}}}, {"FunctionCall": {"ast_fields": {"guid": "c0503ab9-32a5-4512-8b94-06f4b8df42d1", "name": "for<PERSON>ach", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "eaca833b-b93d-4fbd-a300-7a61c1c9faa4", "childs_guid": [], "full_range": {"start_byte": 1239, "end_byte": 1317, "start_point": {"row": 66, "column": 0}, "end_point": {"row": 68, "column": 2}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": "6af7f7c3-6031-4e72-8b11-57a7bfd5cd1a", "is_error": false}, "template_types": []}}, {"FunctionCall": {"ast_fields": {"guid": "816bccc9-770a-4cbb-a1ea-9e47dc70b23e", "name": "addEventListener", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "eaca833b-b93d-4fbd-a300-7a61c1c9faa4", "childs_guid": [], "full_range": {"start_byte": 1508, "end_byte": 1615, "start_point": {"row": 83, "column": 0}, "end_point": {"row": 85, "column": 2}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": "74826ff2-d68d-4a34-bb76-09f0cbf16b85", "is_error": false}, "template_types": []}}, {"VariableDefinition": {"ast_fields": {"guid": "b9bafe57-2d62-4f54-88d1-27dd1892040f", "name": "john", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "eaca833b-b93d-4fbd-a300-7a61c1c9faa4", "childs_guid": [], "full_range": {"start_byte": 1899, "end_byte": 1931, "start_point": {"row": 101, "column": 4}, "end_point": {"row": 101, "column": 36}}, "declaration_range": {"start_byte": 1899, "end_byte": 1931, "start_point": {"row": 101, "column": 4}, "end_point": {"row": 101, "column": 36}}, "definition_range": {"start_byte": 1899, "end_byte": 1931, "start_point": {"row": 101, "column": 4}, "end_point": {"row": 101, "column": 36}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}, "type_": {"name": null, "inference_info": "new Person(\"<PERSON>\", \"<PERSON><PERSON>\")", "is_pod": false, "namespace": "", "guid": null, "nested_types": []}}}, {"FunctionCall": {"ast_fields": {"guid": "0d9497be-6153-43f3-b8d7-8caf68d864ac", "name": "log", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "eaca833b-b93d-4fbd-a300-7a61c1c9faa4", "childs_guid": [], "full_range": {"start_byte": 1934, "end_byte": 1962, "start_point": {"row": 103, "column": 0}, "end_point": {"row": 103, "column": 28}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": "f97e4c0e-08f7-436f-8aef-e6e4948a4987", "is_error": false}, "template_types": []}}, {"VariableDefinition": {"ast_fields": {"guid": "84738bbd-732b-4430-ba96-d578ecd357d6", "name": "jane", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "eaca833b-b93d-4fbd-a300-7a61c1c9faa4", "childs_guid": [], "full_range": {"start_byte": 2298, "end_byte": 2344, "start_point": {"row": 118, "column": 4}, "end_point": {"row": 118, "column": 50}}, "declaration_range": {"start_byte": 2298, "end_byte": 2344, "start_point": {"row": 118, "column": 4}, "end_point": {"row": 118, "column": 50}}, "definition_range": {"start_byte": 2298, "end_byte": 2344, "start_point": {"row": 118, "column": 4}, "end_point": {"row": 118, "column": 50}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}, "type_": {"name": null, "inference_info": "new Employee(\"<PERSON>\", \"<PERSON><PERSON>\", \"<PERSON>\")", "is_pod": false, "namespace": "", "guid": null, "nested_types": []}}}, {"FunctionCall": {"ast_fields": {"guid": "a37d7a34-1ea7-465d-80a5-81a478b796f1", "name": "log", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "eaca833b-b93d-4fbd-a300-7a61c1c9faa4", "childs_guid": [], "full_range": {"start_byte": 2347, "end_byte": 2375, "start_point": {"row": 120, "column": 0}, "end_point": {"row": 120, "column": 28}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": "82278dec-d03b-4da4-a28a-50dbdeede071", "is_error": false}, "template_types": []}}, {"FunctionCall": {"ast_fields": {"guid": "69c89dbc-6822-44d3-aaef-f5aa4bccf033", "name": "add", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "eaca833b-b93d-4fbd-a300-7a61c1c9faa4", "childs_guid": [], "full_range": {"start_byte": 2497, "end_byte": 2505, "start_point": {"row": 126, "column": 0}, "end_point": {"row": 126, "column": 8}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": "9f5a522b-c88e-4671-900a-5e0558b8d0fb", "is_error": false}, "template_types": []}}, {"FunctionCall": {"ast_fields": {"guid": "89750eca-1828-4af9-8509-d84c9ca17a47", "name": "log", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "eaca833b-b93d-4fbd-a300-7a61c1c9faa4", "childs_guid": [], "full_range": {"start_byte": 2507, "end_byte": 2529, "start_point": {"row": 127, "column": 0}, "end_point": {"row": 127, "column": 22}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": "c4bdf92c-c87c-4946-b834-e550510ae14a", "is_error": false}, "template_types": []}}, {"FunctionDeclaration": {"ast_fields": {"guid": "b5290c9d-c5da-47cb-b8b0-ab7bef613052", "name": "multiply", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "eaca833b-b93d-4fbd-a300-7a61c1c9faa4", "childs_guid": ["1632b189-05f6-4c1d-a8d0-f9d459343905", "9e23b32f-41e4-46cc-9541-1d4ef3443e0c"], "full_range": {"start_byte": 2584, "end_byte": 2620, "start_point": {"row": 130, "column": 15}, "end_point": {"row": 132, "column": 1}}, "declaration_range": {"start_byte": 2584, "end_byte": 2599, "start_point": {"row": 130, "column": 15}, "end_point": {"row": 130, "column": 30}}, "definition_range": {"start_byte": 2599, "end_byte": 2620, "start_point": {"row": 130, "column": 30}, "end_point": {"row": 132, "column": 1}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}, "template_types": [], "args": [{"name": "a", "type_": null}, {"name": "b", "type_": null}], "return_type": null}}, {"FunctionCall": {"ast_fields": {"guid": "bf3fd603-8776-4f20-b45d-b167045405a0", "name": "log", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "eaca833b-b93d-4fbd-a300-7a61c1c9faa4", "childs_guid": [], "full_range": {"start_byte": 2621, "end_byte": 2648, "start_point": {"row": 133, "column": 0}, "end_point": {"row": 133, "column": 27}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": "51d48f50-aff1-487b-bd94-f6acca029f92", "is_error": false}, "template_types": []}}, {"FunctionDeclaration": {"ast_fields": {"guid": "ffb10970-5d7e-41cc-af5b-1f0bca4a347d", "name": "subtract", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "eaca833b-b93d-4fbd-a300-7a61c1c9faa4", "childs_guid": ["07253e75-d668-4e95-b893-4dab9a3cf61c", "f4ccb55a-c40b-4944-beb1-30ec68da2c43"], "full_range": {"start_byte": 2698, "end_byte": 2729, "start_point": {"row": 136, "column": 15}, "end_point": {"row": 138, "column": 1}}, "declaration_range": {"start_byte": 2698, "end_byte": 2708, "start_point": {"row": 136, "column": 15}, "end_point": {"row": 136, "column": 25}}, "definition_range": {"start_byte": 2708, "end_byte": 2729, "start_point": {"row": 136, "column": 25}, "end_point": {"row": 138, "column": 1}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}, "template_types": [], "args": [{"name": "a", "type_": null}, {"name": "b", "type_": null}], "return_type": null}}, {"FunctionCall": {"ast_fields": {"guid": "293af1fa-7dfe-46ae-9b39-7164d826fcc7", "name": "log", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "eaca833b-b93d-4fbd-a300-7a61c1c9faa4", "childs_guid": [], "full_range": {"start_byte": 2730, "end_byte": 2757, "start_point": {"row": 139, "column": 0}, "end_point": {"row": 139, "column": 27}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": "02ef85aa-4cec-4a9c-a32a-e66b96a309c7", "is_error": false}, "template_types": []}}, {"FunctionCall": {"ast_fields": {"guid": "07b6b0d1-bcff-484f-a317-9976f857bed0", "name": "", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "eaca833b-b93d-4fbd-a300-7a61c1c9faa4", "childs_guid": [], "full_range": {"start_byte": 2824, "end_byte": 2878, "start_point": {"row": 142, "column": 0}, "end_point": {"row": 144, "column": 4}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": "7a8bbf57-96e9-4f13-86e6-a87ef7f650d8", "is_error": false}, "template_types": []}}, {"VariableDefinition": {"ast_fields": {"guid": "f2902110-ddb3-4aca-89ab-f027fdba78ad", "name": "john", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "eaca833b-b93d-4fbd-a300-7a61c1c9faa4", "childs_guid": [], "full_range": {"start_byte": 3011, "end_byte": 3040, "start_point": {"row": 152, "column": 4}, "end_point": {"row": 152, "column": 33}}, "declaration_range": {"start_byte": 3011, "end_byte": 3040, "start_point": {"row": 152, "column": 4}, "end_point": {"row": 152, "column": 33}}, "definition_range": {"start_byte": 3011, "end_byte": 3040, "start_point": {"row": 152, "column": 4}, "end_point": {"row": 152, "column": 33}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}, "type_": {"name": null, "inference_info": "new Person('<PERSON>', 30)", "is_pod": false, "namespace": "", "guid": null, "nested_types": []}}}, {"FunctionCall": {"ast_fields": {"guid": "17846b9f-354f-4a00-99cc-2093500b8895", "name": "log", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "eaca833b-b93d-4fbd-a300-7a61c1c9faa4", "childs_guid": [], "full_range": {"start_byte": 3042, "end_byte": 3059, "start_point": {"row": 153, "column": 0}, "end_point": {"row": 153, "column": 17}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": "0e19719e-5ee7-4077-9a11-212244efd686", "is_error": false}, "template_types": []}}, {"VariableUsage": {"ast_fields": {"guid": "51461f50-3e91-4e35-aa1e-6bc2c3c23519", "name": "idGenerator", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "eaca833b-b93d-4fbd-a300-7a61c1c9faa4", "childs_guid": [], "full_range": {"start_byte": 3139, "end_byte": 3150, "start_point": {"row": 156, "column": 10}, "end_point": {"row": 156, "column": 21}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}}}, {"VariableDefinition": {"ast_fields": {"guid": "ff2f48fb-b3d9-441a-868a-f2917c24feaa", "name": "gen", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "eaca833b-b93d-4fbd-a300-7a61c1c9faa4", "childs_guid": [], "full_range": {"start_byte": 3223, "end_byte": 3242, "start_point": {"row": 163, "column": 4}, "end_point": {"row": 163, "column": 23}}, "declaration_range": {"start_byte": 3223, "end_byte": 3242, "start_point": {"row": 163, "column": 4}, "end_point": {"row": 163, "column": 23}}, "definition_range": {"start_byte": 3223, "end_byte": 3242, "start_point": {"row": 163, "column": 4}, "end_point": {"row": 163, "column": 23}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}, "type_": {"name": null, "inference_info": "idGenerator()", "is_pod": false, "namespace": "", "guid": null, "nested_types": []}}}, {"FunctionCall": {"ast_fields": {"guid": "0fbcbe04-ff30-4fae-9d73-461d53dc4ede", "name": "log", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "eaca833b-b93d-4fbd-a300-7a61c1c9faa4", "childs_guid": [], "full_range": {"start_byte": 3244, "end_byte": 3273, "start_point": {"row": 164, "column": 0}, "end_point": {"row": 164, "column": 29}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": "83a74e39-e5d7-4599-92c0-709faaf421f8", "is_error": false}, "template_types": []}}, {"FunctionCall": {"ast_fields": {"guid": "e666eb9c-7aff-4a93-8c54-9f644c3d1470", "name": "log", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "eaca833b-b93d-4fbd-a300-7a61c1c9faa4", "childs_guid": [], "full_range": {"start_byte": 3289, "end_byte": 3318, "start_point": {"row": 165, "column": 0}, "end_point": {"row": 165, "column": 29}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": "bce79918-ffac-4885-b7c7-ef652d742414", "is_error": false}, "template_types": []}}, {"VariableUsage": {"ast_fields": {"guid": "e7b94596-5feb-42d3-9c17-e5b56c7e49d2", "name": "HelloWorld", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "eaca833b-b93d-4fbd-a300-7a61c1c9faa4", "childs_guid": [], "full_range": {"start_byte": 3590, "end_byte": 3600, "start_point": {"row": 180, "column": 15}, "end_point": {"row": 180, "column": 25}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}}}, {"StructDeclaration": {"ast_fields": {"guid": "8a14f9d2-06da-46e9-85dc-794b8634c016", "name": "anon-8a14f9d2-06da-46e9-85dc-794b8634c016", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "eaca833b-b93d-4fbd-a300-7a61c1c9faa4", "childs_guid": ["ec915a5a-f2a7-4a0d-8e46-b1fbe95c866f", "f5358d0d-2e4a-47c4-a370-ae93082ccb98", "113adfb3-69a4-4643-ba56-0bccc2344a63"], "full_range": {"start_byte": 239, "end_byte": 295, "start_point": {"row": 13, "column": 14}, "end_point": {"row": 17, "column": 1}}, "declaration_range": {"start_byte": 239, "end_byte": 295, "start_point": {"row": 13, "column": 14}, "end_point": {"row": 17, "column": 1}}, "definition_range": {"start_byte": 239, "end_byte": 295, "start_point": {"row": 13, "column": 14}, "end_point": {"row": 17, "column": 1}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}, "template_types": [], "inherited_types": []}}, {"VariableUsage": {"ast_fields": {"guid": "1584d618-c850-44c8-8b0f-1f2ed854df4c", "name": "console", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "eaca833b-b93d-4fbd-a300-7a61c1c9faa4", "childs_guid": [], "full_range": {"start_byte": 298, "end_byte": 305, "start_point": {"row": 19, "column": 0}, "end_point": {"row": 19, "column": 7}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}}}, {"VariableUsage": {"ast_fields": {"guid": "6849dd60-370d-457d-b82a-72628bdd5040", "name": "RED", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "eaca833b-b93d-4fbd-a300-7a61c1c9faa4", "childs_guid": [], "full_range": {"start_byte": 310, "end_byte": 319, "start_point": {"row": 19, "column": 12}, "end_point": {"row": 19, "column": 21}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": "03c738de-cd7e-4291-a22d-4ab4282f9b4f", "is_error": false}}}, {"VariableUsage": {"ast_fields": {"guid": "efdbb33f-5852-4e0a-b715-ab53e0cff08f", "name": "console", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "eaca833b-b93d-4fbd-a300-7a61c1c9faa4", "childs_guid": [], "full_range": {"start_byte": 338, "end_byte": 345, "start_point": {"row": 21, "column": 0}, "end_point": {"row": 21, "column": 7}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}}}, {"FunctionCall": {"ast_fields": {"guid": "3f8a0c77-81ec-49cb-9f0e-cafedf1c23b6", "name": "greet", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "eaca833b-b93d-4fbd-a300-7a61c1c9faa4", "childs_guid": [], "full_range": {"start_byte": 350, "end_byte": 361, "start_point": {"row": 21, "column": 12}, "end_point": {"row": 21, "column": 23}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": "92fb7737-78bc-42e3-9bb2-a069ebe53af7", "is_error": false}, "template_types": []}}, {"StructDeclaration": {"ast_fields": {"guid": "de7d0455-0473-4b38-b0e2-aec0c4fb0d16", "name": "anon-de7d0455-0473-4b38-b0e2-aec0c4fb0d16", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "eaca833b-b93d-4fbd-a300-7a61c1c9faa4", "childs_guid": ["17334571-66e6-46b1-a590-5e4a3da71b90", "dc875d74-be44-4d65-af6a-21082d75aa14", "643997a5-c7b6-4523-84a8-4e94b500cf74"], "full_range": {"start_byte": 415, "end_byte": 548, "start_point": {"row": 24, "column": 13}, "end_point": {"row": 30, "column": 1}}, "declaration_range": {"start_byte": 415, "end_byte": 548, "start_point": {"row": 24, "column": 13}, "end_point": {"row": 30, "column": 1}}, "definition_range": {"start_byte": 415, "end_byte": 548, "start_point": {"row": 24, "column": 13}, "end_point": {"row": 30, "column": 1}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}, "template_types": [], "inherited_types": []}}, {"FunctionDeclaration": {"ast_fields": {"guid": "9ec5e55c-aa64-4a16-9791-79dc7415f1db", "name": "constructor", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "781f51ed-e001-460b-8c0b-92c102a16df2", "childs_guid": ["8579e584-2a46-4ab4-8b34-ad951d0e1ff9", "b63ed477-233f-4889-86ab-f320be88e521", "c5f8791a-3903-41b9-8723-42f0cd23377f", "53491bad-40e8-4eb9-b376-740458131b6d"], "full_range": {"start_byte": 587, "end_byte": 679, "start_point": {"row": 34, "column": 4}, "end_point": {"row": 37, "column": 5}}, "declaration_range": {"start_byte": 587, "end_byte": 614, "start_point": {"row": 34, "column": 4}, "end_point": {"row": 34, "column": 31}}, "definition_range": {"start_byte": 614, "end_byte": 679, "start_point": {"row": 34, "column": 31}, "end_point": {"row": 37, "column": 5}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}, "template_types": [], "args": [{"name": "height", "type_": null}, {"name": "width", "type_": null}], "return_type": null}}, {"VariableUsage": {"ast_fields": {"guid": "a35712a2-ecc9-4d60-8cde-4ea6aabc3bd8", "name": "console", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "eaca833b-b93d-4fbd-a300-7a61c1c9faa4", "childs_guid": [], "full_range": {"start_byte": 1115, "end_byte": 1122, "start_point": {"row": 61, "column": 0}, "end_point": {"row": 61, "column": 7}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}}}, {"FunctionCall": {"ast_fields": {"guid": "d81b4089-1cf1-401f-a775-d292e28bb962", "name": "fullName", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "eaca833b-b93d-4fbd-a300-7a61c1c9faa4", "childs_guid": [], "full_range": {"start_byte": 1127, "end_byte": 1144, "start_point": {"row": 61, "column": 12}, "end_point": {"row": 61, "column": 29}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": "c746cc7f-93f5-4d3b-9847-d27594960954", "is_error": false}, "template_types": []}}, {"VariableUsage": {"ast_fields": {"guid": "e3c6ca92-b639-40cd-9f1f-be7b87b5ee1f", "name": "person", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "eaca833b-b93d-4fbd-a300-7a61c1c9faa4", "childs_guid": [], "full_range": {"start_byte": 1177, "end_byte": 1183, "start_point": {"row": 62, "column": 10}, "end_point": {"row": 62, "column": 16}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}}}, {"VariableUsage": {"ast_fields": {"guid": "6af7f7c3-6031-4e72-8b11-57a7bfd5cd1a", "name": "fruits", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "eaca833b-b93d-4fbd-a300-7a61c1c9faa4", "childs_guid": [], "full_range": {"start_byte": 1239, "end_byte": 1245, "start_point": {"row": 66, "column": 0}, "end_point": {"row": 66, "column": 6}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}}}, {"VariableDefinition": {"ast_fields": {"guid": "9945f105-0d1a-4320-b715-839815adbb06", "name": "i", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "eaca833b-b93d-4fbd-a300-7a61c1c9faa4", "childs_guid": [], "full_range": {"start_byte": 1455, "end_byte": 1460, "start_point": {"row": 78, "column": 9}, "end_point": {"row": 78, "column": 14}}, "declaration_range": {"start_byte": 1455, "end_byte": 1460, "start_point": {"row": 78, "column": 9}, "end_point": {"row": 78, "column": 14}}, "definition_range": {"start_byte": 1455, "end_byte": 1460, "start_point": {"row": 78, "column": 9}, "end_point": {"row": 78, "column": 14}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}, "type_": {"name": null, "inference_info": "0", "is_pod": true, "namespace": "", "guid": null, "nested_types": []}}}, {"VariableUsage": {"ast_fields": {"guid": "64056b4f-70c5-4351-8875-d817816d8ea0", "name": "i", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "eaca833b-b93d-4fbd-a300-7a61c1c9faa4", "childs_guid": [], "full_range": {"start_byte": 1469, "end_byte": 1470, "start_point": {"row": 78, "column": 23}, "end_point": {"row": 78, "column": 24}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}}}, {"FunctionCall": {"ast_fields": {"guid": "74826ff2-d68d-4a34-bb76-09f0cbf16b85", "name": "getElementById", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "eaca833b-b93d-4fbd-a300-7a61c1c9faa4", "childs_guid": [], "full_range": {"start_byte": 1508, "end_byte": 1543, "start_point": {"row": 83, "column": 0}, "end_point": {"row": 83, "column": 35}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": "000c36f7-ae08-43de-afea-041abfcabd55", "is_error": false}, "template_types": []}}, {"FunctionDeclaration": {"ast_fields": {"guid": "2c523358-8aae-4a32-ab42-b0e024e3f985", "name": "constructor", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "8affd81e-87a6-44fb-b38e-11ee353897e4", "childs_guid": ["c4886ded-cbba-4bc0-92de-2e33f51d72d4", "a79c7993-1260-4fc4-becf-08ccfe30b104", "9600771b-ea9c-48e6-85b2-381a32427af0", "6b91db5d-911d-479f-95bb-d9fbba63e354"], "full_range": {"start_byte": 1655, "end_byte": 1765, "start_point": {"row": 89, "column": 4}, "end_point": {"row": 92, "column": 5}}, "declaration_range": {"start_byte": 1655, "end_byte": 1688, "start_point": {"row": 89, "column": 4}, "end_point": {"row": 89, "column": 37}}, "definition_range": {"start_byte": 1688, "end_byte": 1765, "start_point": {"row": 89, "column": 37}, "end_point": {"row": 92, "column": 5}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}, "template_types": [], "args": [{"name": "firstName", "type_": null}, {"name": "lastName", "type_": null}], "return_type": null}}, {"CommentDefinition": {"ast_fields": {"guid": "961b6faf-f354-4be6-aee4-e4e7758f4041", "name": "", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "8affd81e-87a6-44fb-b38e-11ee353897e4", "childs_guid": [], "full_range": {"start_byte": 1771, "end_byte": 1780, "start_point": {"row": 94, "column": 4}, "end_point": {"row": 94, "column": 13}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}}}, {"FunctionDeclaration": {"ast_fields": {"guid": "f3b3cfe9-204b-46d5-a356-7f2eca8fafa1", "name": "fullName", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "8affd81e-87a6-44fb-b38e-11ee353897e4", "childs_guid": ["4922affb-1612-4da9-9a7e-90dac6f98ce3", "f6080628-df01-45e9-b9e4-9caf9c7f4a91"], "full_range": {"start_byte": 1785, "end_byte": 1856, "start_point": {"row": 95, "column": 4}, "end_point": {"row": 97, "column": 5}}, "declaration_range": {"start_byte": 1785, "end_byte": 1796, "start_point": {"row": 95, "column": 4}, "end_point": {"row": 95, "column": 15}}, "definition_range": {"start_byte": 1796, "end_byte": 1856, "start_point": {"row": 95, "column": 15}, "end_point": {"row": 97, "column": 5}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}, "template_types": [], "args": [], "return_type": null}}, {"VariableUsage": {"ast_fields": {"guid": "f97e4c0e-08f7-436f-8aef-e6e4948a4987", "name": "console", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "eaca833b-b93d-4fbd-a300-7a61c1c9faa4", "childs_guid": [], "full_range": {"start_byte": 1934, "end_byte": 1941, "start_point": {"row": 103, "column": 0}, "end_point": {"row": 103, "column": 7}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}}}, {"FunctionCall": {"ast_fields": {"guid": "a6bce28e-07c8-4a24-bfb8-7d666e75b0cd", "name": "fullName", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "eaca833b-b93d-4fbd-a300-7a61c1c9faa4", "childs_guid": [], "full_range": {"start_byte": 1946, "end_byte": 1961, "start_point": {"row": 103, "column": 12}, "end_point": {"row": 103, "column": 27}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": "29708307-588a-4ca5-b6ad-c9d925710ede", "is_error": false}, "template_types": []}}, {"FunctionDeclaration": {"ast_fields": {"guid": "77407ebc-eda5-4870-8846-4058b7ca9d2e", "name": "constructor", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "f398f09d-b2ae-47f7-8773-1c43cdb48d24", "childs_guid": ["e6ffbc3d-0315-4918-97ad-76d7a6e432c5", "612f727a-64a6-4c0e-84d8-62aa118f5a5d", "27db3598-f53f-4d55-a1b0-429c912c074c", "866b0c4b-5181-4379-b959-793c73d1e842", "6a9ad822-9cc5-4c57-b2b3-114a51262f1b", "687e673a-7d69-4683-8597-130c56751a98"], "full_range": {"start_byte": 2036, "end_byte": 2187, "start_point": {"row": 107, "column": 4}, "end_point": {"row": 110, "column": 5}}, "declaration_range": {"start_byte": 2036, "end_byte": 2079, "start_point": {"row": 107, "column": 4}, "end_point": {"row": 107, "column": 47}}, "definition_range": {"start_byte": 2079, "end_byte": 2187, "start_point": {"row": 107, "column": 47}, "end_point": {"row": 110, "column": 5}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}, "template_types": [], "args": [{"name": "firstName", "type_": null}, {"name": "lastName", "type_": null}, {"name": "position", "type_": null}], "return_type": null}}, {"CommentDefinition": {"ast_fields": {"guid": "79a533cf-1e89-4489-962d-6ae0f6057f22", "name": "", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "f398f09d-b2ae-47f7-8773-1c43cdb48d24", "childs_guid": [], "full_range": {"start_byte": 2193, "end_byte": 2211, "start_point": {"row": 112, "column": 4}, "end_point": {"row": 112, "column": 22}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}}}, {"FunctionDeclaration": {"ast_fields": {"guid": "e3d799ed-fea6-4628-8e28-70a28b69257a", "name": "fullName", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "f398f09d-b2ae-47f7-8773-1c43cdb48d24", "childs_guid": ["0dc36c8c-928d-4f4c-901c-1e6154ffe01d", "18fa874f-1649-4569-ad40-634990de79de"], "full_range": {"start_byte": 2216, "end_byte": 2290, "start_point": {"row": 113, "column": 4}, "end_point": {"row": 115, "column": 5}}, "declaration_range": {"start_byte": 2216, "end_byte": 2227, "start_point": {"row": 113, "column": 4}, "end_point": {"row": 113, "column": 15}}, "definition_range": {"start_byte": 2227, "end_byte": 2290, "start_point": {"row": 113, "column": 15}, "end_point": {"row": 115, "column": 5}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}, "template_types": [], "args": [], "return_type": null}}, {"VariableUsage": {"ast_fields": {"guid": "82278dec-d03b-4da4-a28a-50dbdeede071", "name": "console", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "eaca833b-b93d-4fbd-a300-7a61c1c9faa4", "childs_guid": [], "full_range": {"start_byte": 2347, "end_byte": 2354, "start_point": {"row": 120, "column": 0}, "end_point": {"row": 120, "column": 7}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}}}, {"FunctionCall": {"ast_fields": {"guid": "c8fbcf21-aceb-44c3-bfe8-9f9f9c465abb", "name": "fullName", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "eaca833b-b93d-4fbd-a300-7a61c1c9faa4", "childs_guid": [], "full_range": {"start_byte": 2359, "end_byte": 2374, "start_point": {"row": 120, "column": 12}, "end_point": {"row": 120, "column": 27}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": "409a2ea6-8ecf-41b2-9f56-55997bae1828", "is_error": false}, "template_types": []}}, {"VariableUsage": {"ast_fields": {"guid": "c4bdf92c-c87c-4946-b834-e550510ae14a", "name": "console", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "eaca833b-b93d-4fbd-a300-7a61c1c9faa4", "childs_guid": [], "full_range": {"start_byte": 2507, "end_byte": 2514, "start_point": {"row": 127, "column": 0}, "end_point": {"row": 127, "column": 7}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}}}, {"FunctionCall": {"ast_fields": {"guid": "ff2f9b18-ed59-452e-a91b-9d4fdfaeffe6", "name": "add", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "eaca833b-b93d-4fbd-a300-7a61c1c9faa4", "childs_guid": [], "full_range": {"start_byte": 2519, "end_byte": 2528, "start_point": {"row": 127, "column": 12}, "end_point": {"row": 127, "column": 21}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": "86b273c0-46f5-4a23-b665-4ae935b7c623", "is_error": false}, "template_types": []}}, {"VariableUsage": {"ast_fields": {"guid": "51d48f50-aff1-487b-bd94-f6acca029f92", "name": "console", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "eaca833b-b93d-4fbd-a300-7a61c1c9faa4", "childs_guid": [], "full_range": {"start_byte": 2621, "end_byte": 2628, "start_point": {"row": 133, "column": 0}, "end_point": {"row": 133, "column": 7}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}}}, {"FunctionCall": {"ast_fields": {"guid": "fb3b534f-107e-4eb1-ac4f-1163f4fb2e6d", "name": "multiply", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "eaca833b-b93d-4fbd-a300-7a61c1c9faa4", "childs_guid": [], "full_range": {"start_byte": 2633, "end_byte": 2647, "start_point": {"row": 133, "column": 12}, "end_point": {"row": 133, "column": 26}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": "d8593b0d-1a52-4a3e-bb76-e7bb472<PERSON>da", "is_error": false}, "template_types": []}}, {"VariableUsage": {"ast_fields": {"guid": "02ef85aa-4cec-4a9c-a32a-e66b96a309c7", "name": "console", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "eaca833b-b93d-4fbd-a300-7a61c1c9faa4", "childs_guid": [], "full_range": {"start_byte": 2730, "end_byte": 2737, "start_point": {"row": 139, "column": 0}, "end_point": {"row": 139, "column": 7}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}}}, {"FunctionCall": {"ast_fields": {"guid": "0757ee14-0850-47da-a48e-029aa6cdee34", "name": "subtract", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "eaca833b-b93d-4fbd-a300-7a61c1c9faa4", "childs_guid": [], "full_range": {"start_byte": 2742, "end_byte": 2756, "start_point": {"row": 139, "column": 12}, "end_point": {"row": 139, "column": 26}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": "1b0e8ea0-ff44-4d8d-a269-5435fb87aa4f", "is_error": false}, "template_types": []}}, {"VariableUsage": {"ast_fields": {"guid": "0e19719e-5ee7-4077-9a11-212244efd686", "name": "console", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "eaca833b-b93d-4fbd-a300-7a61c1c9faa4", "childs_guid": [], "full_range": {"start_byte": 3042, "end_byte": 3049, "start_point": {"row": 153, "column": 0}, "end_point": {"row": 153, "column": 7}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}}}, {"VariableUsage": {"ast_fields": {"guid": "a58f3ae5-e211-4138-a879-2c5d35b24adb", "name": "john", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "eaca833b-b93d-4fbd-a300-7a61c1c9faa4", "childs_guid": [], "full_range": {"start_byte": 3054, "end_byte": 3058, "start_point": {"row": 153, "column": 12}, "end_point": {"row": 153, "column": 16}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}}}, {"FunctionCall": {"ast_fields": {"guid": "85c83e27-8acb-4ccc-b5de-f9ab65f26f83", "name": "idGenerator", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "eaca833b-b93d-4fbd-a300-7a61c1c9faa4", "childs_guid": [], "full_range": {"start_byte": 3229, "end_byte": 3242, "start_point": {"row": 163, "column": 10}, "end_point": {"row": 163, "column": 23}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": "f20ea88d-14f4-46d8-a7fe-934266c94978", "is_error": false}, "template_types": []}}, {"VariableUsage": {"ast_fields": {"guid": "83a74e39-e5d7-4599-92c0-709faaf421f8", "name": "console", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "eaca833b-b93d-4fbd-a300-7a61c1c9faa4", "childs_guid": [], "full_range": {"start_byte": 3244, "end_byte": 3251, "start_point": {"row": 164, "column": 0}, "end_point": {"row": 164, "column": 7}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}}}, {"VariableUsage": {"ast_fields": {"guid": "f6a0bf9f-c090-4021-a487-aab6b3c33e24", "name": "value", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "eaca833b-b93d-4fbd-a300-7a61c1c9faa4", "childs_guid": [], "full_range": {"start_byte": 3256, "end_byte": 3272, "start_point": {"row": 164, "column": 12}, "end_point": {"row": 164, "column": 28}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": "534a63ae-4fa9-4cac-be1f-************", "is_error": false}}}, {"VariableUsage": {"ast_fields": {"guid": "bce79918-ffac-4885-b7c7-ef652d742414", "name": "console", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "eaca833b-b93d-4fbd-a300-7a61c1c9faa4", "childs_guid": [], "full_range": {"start_byte": 3289, "end_byte": 3296, "start_point": {"row": 165, "column": 0}, "end_point": {"row": 165, "column": 7}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}}}, {"VariableUsage": {"ast_fields": {"guid": "3b5fde90-002d-433e-90b0-949010aa515f", "name": "value", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "eaca833b-b93d-4fbd-a300-7a61c1c9faa4", "childs_guid": [], "full_range": {"start_byte": 3301, "end_byte": 3317, "start_point": {"row": 165, "column": 12}, "end_point": {"row": 165, "column": 28}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": "8e35d372-8f1a-4bc9-be4a-e675f5061b8b", "is_error": false}}}, {"FunctionDeclaration": {"ast_fields": {"guid": "2ff80ba3-1674-40d0-95b0-ca561c4243d1", "name": "render", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "0653923c-a62e-4e2c-9197-e6759fa9c2f2", "childs_guid": ["316ec5a4-1bf8-4543-aa49-046fa283f37b", "4e948e7f-e1bd-4f7a-8b7e-85251ad1a515", "afd8062e-c9b3-486a-9217-b1e218933619", "5c01939f-7ce2-4203-8d46-b447222ac458", "33412bc0-0f08-4809-a45e-31906cda21ae", "7c8b7cd7-6148-4b4a-8117-d939389059bc"], "full_range": {"start_byte": 3410, "end_byte": 3571, "start_point": {"row": 170, "column": 4}, "end_point": {"row": 177, "column": 5}}, "declaration_range": {"start_byte": 3410, "end_byte": 3419, "start_point": {"row": 170, "column": 4}, "end_point": {"row": 170, "column": 13}}, "definition_range": {"start_byte": 3419, "end_byte": 3571, "start_point": {"row": 170, "column": 13}, "end_point": {"row": 177, "column": 5}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}, "template_types": [], "args": [], "return_type": null}}, {"ClassFieldDeclaration": {"ast_fields": {"guid": "ec915a5a-f2a7-4a0d-8e46-b1fbe95c866f", "name": "RED", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "8a14f9d2-06da-46e9-85dc-794b8634c016", "childs_guid": [], "full_range": {"start_byte": 245, "end_byte": 255, "start_point": {"row": 14, "column": 4}, "end_point": {"row": 14, "column": 14}}, "declaration_range": {"start_byte": 245, "end_byte": 255, "start_point": {"row": 14, "column": 4}, "end_point": {"row": 14, "column": 14}}, "definition_range": {"start_byte": 245, "end_byte": 255, "start_point": {"row": 14, "column": 4}, "end_point": {"row": 14, "column": 14}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}, "type_": {"name": null, "inference_info": "'Red'", "is_pod": true, "namespace": "", "guid": null, "nested_types": []}}}, {"ClassFieldDeclaration": {"ast_fields": {"guid": "f5358d0d-2e4a-47c4-a370-ae93082ccb98", "name": "BLUE", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "8a14f9d2-06da-46e9-85dc-794b8634c016", "childs_guid": [], "full_range": {"start_byte": 261, "end_byte": 273, "start_point": {"row": 15, "column": 4}, "end_point": {"row": 15, "column": 16}}, "declaration_range": {"start_byte": 261, "end_byte": 273, "start_point": {"row": 15, "column": 4}, "end_point": {"row": 15, "column": 16}}, "definition_range": {"start_byte": 261, "end_byte": 273, "start_point": {"row": 15, "column": 4}, "end_point": {"row": 15, "column": 16}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}, "type_": {"name": null, "inference_info": "'Blue'", "is_pod": true, "namespace": "", "guid": null, "nested_types": []}}}, {"ClassFieldDeclaration": {"ast_fields": {"guid": "113adfb3-69a4-4643-ba56-0bccc2344a63", "name": "GREEN", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "8a14f9d2-06da-46e9-85dc-794b8634c016", "childs_guid": [], "full_range": {"start_byte": 279, "end_byte": 293, "start_point": {"row": 16, "column": 4}, "end_point": {"row": 16, "column": 18}}, "declaration_range": {"start_byte": 279, "end_byte": 293, "start_point": {"row": 16, "column": 4}, "end_point": {"row": 16, "column": 18}}, "definition_range": {"start_byte": 279, "end_byte": 293, "start_point": {"row": 16, "column": 4}, "end_point": {"row": 16, "column": 18}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}, "type_": {"name": null, "inference_info": "'Green'", "is_pod": true, "namespace": "", "guid": null, "nested_types": []}}}, {"VariableUsage": {"ast_fields": {"guid": "03c738de-cd7e-4291-a22d-4ab4282f9b4f", "name": "Color", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "eaca833b-b93d-4fbd-a300-7a61c1c9faa4", "childs_guid": [], "full_range": {"start_byte": 310, "end_byte": 315, "start_point": {"row": 19, "column": 12}, "end_point": {"row": 19, "column": 17}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}}}, {"VariableUsage": {"ast_fields": {"guid": "ed916058-b2d4-4de2-a07a-7a6387f3ecf2", "name": "name", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "eaca833b-b93d-4fbd-a300-7a61c1c9faa4", "childs_guid": [], "full_range": {"start_byte": 356, "end_byte": 360, "start_point": {"row": 21, "column": 18}, "end_point": {"row": 21, "column": 22}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}}}, {"ClassFieldDeclaration": {"ast_fields": {"guid": "17334571-66e6-46b1-a590-5e4a3da71b90", "name": "firstName", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "de7d0455-0473-4b38-b0e2-aec0c4fb0d16", "childs_guid": [], "full_range": {"start_byte": 421, "end_byte": 438, "start_point": {"row": 25, "column": 4}, "end_point": {"row": 25, "column": 21}}, "declaration_range": {"start_byte": 421, "end_byte": 438, "start_point": {"row": 25, "column": 4}, "end_point": {"row": 25, "column": 21}}, "definition_range": {"start_byte": 421, "end_byte": 438, "start_point": {"row": 25, "column": 4}, "end_point": {"row": 25, "column": 21}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}, "type_": {"name": null, "inference_info": "\"John\"", "is_pod": true, "namespace": "", "guid": null, "nested_types": []}}}, {"ClassFieldDeclaration": {"ast_fields": {"guid": "dc875d74-be44-4d65-af6a-21082d75aa14", "name": "lastName", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "de7d0455-0473-4b38-b0e2-aec0c4fb0d16", "childs_guid": [], "full_range": {"start_byte": 444, "end_byte": 459, "start_point": {"row": 26, "column": 4}, "end_point": {"row": 26, "column": 19}}, "declaration_range": {"start_byte": 444, "end_byte": 459, "start_point": {"row": 26, "column": 4}, "end_point": {"row": 26, "column": 19}}, "definition_range": {"start_byte": 444, "end_byte": 459, "start_point": {"row": 26, "column": 4}, "end_point": {"row": 26, "column": 19}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}, "type_": {"name": null, "inference_info": "\"Doe\"", "is_pod": true, "namespace": "", "guid": null, "nested_types": []}}}, {"FunctionDeclaration": {"ast_fields": {"guid": "643997a5-c7b6-4523-84a8-4e94b500cf74", "name": "fullName", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "de7d0455-0473-4b38-b0e2-aec0c4fb0d16", "childs_guid": ["c79c6503-08b1-456f-ab3e-95bd2fa99781", "0f023451-3b6a-455c-b3ac-bb512b70209c"], "full_range": {"start_byte": 475, "end_byte": 546, "start_point": {"row": 27, "column": 14}, "end_point": {"row": 29, "column": 5}}, "declaration_range": {"start_byte": 475, "end_byte": 486, "start_point": {"row": 27, "column": 14}, "end_point": {"row": 27, "column": 25}}, "definition_range": {"start_byte": 486, "end_byte": 546, "start_point": {"row": 27, "column": 25}, "end_point": {"row": 29, "column": 5}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}, "template_types": [], "args": [], "return_type": null}}, {"ClassFieldDeclaration": {"ast_fields": {"guid": "33bbcfc4-e021-45c9-a96f-6b93eedf19db", "name": "#height", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "f9004947-4af7-4e2e-90fe-fb555dd4a017", "childs_guid": [], "full_range": {"start_byte": 778, "end_byte": 785, "start_point": {"row": 42, "column": 4}, "end_point": {"row": 42, "column": 11}}, "declaration_range": {"start_byte": 778, "end_byte": 785, "start_point": {"row": 42, "column": 4}, "end_point": {"row": 42, "column": 11}}, "definition_range": {"start_byte": 778, "end_byte": 785, "start_point": {"row": 42, "column": 4}, "end_point": {"row": 42, "column": 11}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}, "type_": {"name": null, "inference_info": null, "is_pod": false, "namespace": "", "guid": null, "nested_types": []}}}, {"ClassFieldDeclaration": {"ast_fields": {"guid": "6d49e4e1-5140-43c4-aa2e-651ceb43786f", "name": "#width", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "f9004947-4af7-4e2e-90fe-fb555dd4a017", "childs_guid": [], "full_range": {"start_byte": 794, "end_byte": 800, "start_point": {"row": 43, "column": 4}, "end_point": {"row": 43, "column": 10}}, "declaration_range": {"start_byte": 794, "end_byte": 800, "start_point": {"row": 43, "column": 4}, "end_point": {"row": 43, "column": 10}}, "definition_range": {"start_byte": 794, "end_byte": 800, "start_point": {"row": 43, "column": 4}, "end_point": {"row": 43, "column": 10}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}, "type_": {"name": null, "inference_info": null, "is_pod": false, "namespace": "", "guid": null, "nested_types": []}}}, {"FunctionDeclaration": {"ast_fields": {"guid": "0c8b553f-5c6a-405b-a3f8-78c4e0f6c07f", "name": "constructor", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "f9004947-4af7-4e2e-90fe-fb555dd4a017", "childs_guid": ["e629aaeb-d61e-4168-b176-820f4ed96262", "f552ef18-b987-40cd-beaf-48a5cd610d6c", "44c00998-0c7f-4364-b111-ace56a33861c", "0a11f993-6671-482c-9444-2a4f3c61ca11"], "full_range": {"start_byte": 806, "end_byte": 900, "start_point": {"row": 44, "column": 4}, "end_point": {"row": 47, "column": 5}}, "declaration_range": {"start_byte": 806, "end_byte": 833, "start_point": {"row": 44, "column": 4}, "end_point": {"row": 44, "column": 31}}, "definition_range": {"start_byte": 833, "end_byte": 900, "start_point": {"row": 44, "column": 31}, "end_point": {"row": 47, "column": 5}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}, "template_types": [], "args": [{"name": "height", "type_": null}, {"name": "width", "type_": null}], "return_type": null}}, {"ClassFieldDeclaration": {"ast_fields": {"guid": "b766efe9-b431-4a6a-8344-a7576914f406", "name": "height", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "6906eff0-26f6-4636-91f5-485b9024a58f", "childs_guid": [], "full_range": {"start_byte": 988, "end_byte": 994, "start_point": {"row": 52, "column": 4}, "end_point": {"row": 52, "column": 10}}, "declaration_range": {"start_byte": 988, "end_byte": 994, "start_point": {"row": 52, "column": 4}, "end_point": {"row": 52, "column": 10}}, "definition_range": {"start_byte": 988, "end_byte": 994, "start_point": {"row": 52, "column": 4}, "end_point": {"row": 52, "column": 10}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}, "type_": {"name": null, "inference_info": null, "is_pod": false, "namespace": "", "guid": null, "nested_types": []}}}, {"ClassFieldDeclaration": {"ast_fields": {"guid": "2de4f758-7b47-4326-91e2-381fa481d2a8", "name": "width", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "6906eff0-26f6-4636-91f5-485b9024a58f", "childs_guid": [], "full_range": {"start_byte": 1003, "end_byte": 1008, "start_point": {"row": 53, "column": 4}, "end_point": {"row": 53, "column": 9}}, "declaration_range": {"start_byte": 1003, "end_byte": 1008, "start_point": {"row": 53, "column": 4}, "end_point": {"row": 53, "column": 9}}, "definition_range": {"start_byte": 1003, "end_byte": 1008, "start_point": {"row": 53, "column": 4}, "end_point": {"row": 53, "column": 9}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}, "type_": {"name": null, "inference_info": null, "is_pod": false, "namespace": "", "guid": null, "nested_types": []}}}, {"FunctionDeclaration": {"ast_fields": {"guid": "c4ca4f4a-39fc-433b-966f-f5b28ec22396", "name": "constructor", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "6906eff0-26f6-4636-91f5-485b9024a58f", "childs_guid": ["d0d2990e-1622-4c67-be3f-53aa71871d82", "*************-43ad-97c7-c427e1e9662c", "5a122d9b-825c-4c03-8c3f-3071b035ba63", "b87d2dd5-2b48-4d66-a412-f6be784dc676"], "full_range": {"start_byte": 1017, "end_byte": 1109, "start_point": {"row": 54, "column": 4}, "end_point": {"row": 57, "column": 5}}, "declaration_range": {"start_byte": 1017, "end_byte": 1044, "start_point": {"row": 54, "column": 4}, "end_point": {"row": 54, "column": 31}}, "definition_range": {"start_byte": 1044, "end_byte": 1109, "start_point": {"row": 54, "column": 31}, "end_point": {"row": 57, "column": 5}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}, "template_types": [], "args": [{"name": "height", "type_": null}, {"name": "width", "type_": null}], "return_type": null}}, {"VariableUsage": {"ast_fields": {"guid": "c746cc7f-93f5-4d3b-9847-d27594960954", "name": "person", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "eaca833b-b93d-4fbd-a300-7a61c1c9faa4", "childs_guid": [], "full_range": {"start_byte": 1127, "end_byte": 1133, "start_point": {"row": 61, "column": 12}, "end_point": {"row": 61, "column": 18}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}}}, {"VariableUsage": {"ast_fields": {"guid": "8325fc35-f3d9-4a14-8c52-c9351d617203", "name": "age", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "eaca833b-b93d-4fbd-a300-7a61c1c9faa4", "childs_guid": [], "full_range": {"start_byte": 1339, "end_byte": 1342, "start_point": {"row": 71, "column": 4}, "end_point": {"row": 71, "column": 7}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}}}, {"FunctionCall": {"ast_fields": {"guid": "cabfcca7-af4d-4446-8526-60480d2c591e", "name": "log", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "eaca833b-b93d-4fbd-a300-7a61c1c9faa4", "childs_guid": [], "full_range": {"start_byte": 1355, "end_byte": 1387, "start_point": {"row": 72, "column": 4}, "end_point": {"row": 72, "column": 36}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": "1f022b3d-297e-4415-9f04-396ee8fe46ea", "is_error": false}, "template_types": []}}, {"VariableUsage": {"ast_fields": {"guid": "75aad241-3632-4b90-92f8-aeb6ab008bc8", "name": "i", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "eaca833b-b93d-4fbd-a300-7a61c1c9faa4", "childs_guid": [], "full_range": {"start_byte": 1462, "end_byte": 1463, "start_point": {"row": 78, "column": 16}, "end_point": {"row": 78, "column": 17}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}}}, {"FunctionCall": {"ast_fields": {"guid": "09619314-24c4-4e9a-9bcb-479cbfb94949", "name": "log", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "eaca833b-b93d-4fbd-a300-7a61c1c9faa4", "childs_guid": [], "full_range": {"start_byte": 1480, "end_byte": 1494, "start_point": {"row": 79, "column": 4}, "end_point": {"row": 79, "column": 18}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": "1b5108c6-7ad8-499a-9073-26415250c4f7", "is_error": false}, "template_types": []}}, {"VariableUsage": {"ast_fields": {"guid": "000c36f7-ae08-43de-afea-041abfcabd55", "name": "document", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "eaca833b-b93d-4fbd-a300-7a61c1c9faa4", "childs_guid": [], "full_range": {"start_byte": 1508, "end_byte": 1516, "start_point": {"row": 83, "column": 0}, "end_point": {"row": 83, "column": 8}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}}}, {"VariableUsage": {"ast_fields": {"guid": "acc8a9dc-6778-4ee0-a8f2-b0e3402f734f", "name": "Person", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "eaca833b-b93d-4fbd-a300-7a61c1c9faa4", "childs_guid": [], "full_range": {"start_byte": 1910, "end_byte": 1916, "start_point": {"row": 101, "column": 15}, "end_point": {"row": 101, "column": 21}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}}}, {"VariableUsage": {"ast_fields": {"guid": "29708307-588a-4ca5-b6ad-c9d925710ede", "name": "john", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "eaca833b-b93d-4fbd-a300-7a61c1c9faa4", "childs_guid": [], "full_range": {"start_byte": 1946, "end_byte": 1950, "start_point": {"row": 103, "column": 12}, "end_point": {"row": 103, "column": 16}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}}}, {"VariableUsage": {"ast_fields": {"guid": "9a6c6b20-8310-427b-9cec-69d8526e39d5", "name": "Employee", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "eaca833b-b93d-4fbd-a300-7a61c1c9faa4", "childs_guid": [], "full_range": {"start_byte": 2309, "end_byte": 2317, "start_point": {"row": 118, "column": 15}, "end_point": {"row": 118, "column": 23}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}}}, {"VariableUsage": {"ast_fields": {"guid": "409a2ea6-8ecf-41b2-9f56-55997bae1828", "name": "jane", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "eaca833b-b93d-4fbd-a300-7a61c1c9faa4", "childs_guid": [], "full_range": {"start_byte": 2359, "end_byte": 2363, "start_point": {"row": 120, "column": 12}, "end_point": {"row": 120, "column": 16}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}}}, {"VariableUsage": {"ast_fields": {"guid": "4d704e92-c28f-4764-a76d-587b95824bbb", "name": "Person", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "eaca833b-b93d-4fbd-a300-7a61c1c9faa4", "childs_guid": [], "full_range": {"start_byte": 3022, "end_byte": 3028, "start_point": {"row": 152, "column": 15}, "end_point": {"row": 152, "column": 21}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}}}, {"VariableDefinition": {"ast_fields": {"guid": "038dfeff-fed2-4ea0-b17f-706c89503f8d", "name": "id", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "eaca833b-b93d-4fbd-a300-7a61c1c9faa4", "childs_guid": [], "full_range": {"start_byte": 3163, "end_byte": 3169, "start_point": {"row": 157, "column": 8}, "end_point": {"row": 157, "column": 14}}, "declaration_range": {"start_byte": 3163, "end_byte": 3169, "start_point": {"row": 157, "column": 8}, "end_point": {"row": 157, "column": 14}}, "definition_range": {"start_byte": 3163, "end_byte": 3169, "start_point": {"row": 157, "column": 8}, "end_point": {"row": 157, "column": 14}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}, "type_": {"name": null, "inference_info": "0", "is_pod": true, "namespace": "", "guid": null, "nested_types": []}}}, {"FunctionCall": {"ast_fields": {"guid": "534a63ae-4fa9-4cac-be1f-************", "name": "next", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "eaca833b-b93d-4fbd-a300-7a61c1c9faa4", "childs_guid": [], "full_range": {"start_byte": 3256, "end_byte": 3266, "start_point": {"row": 164, "column": 12}, "end_point": {"row": 164, "column": 22}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": "2e5fb68e-46f6-4114-b686-cc8ca80075c0", "is_error": false}, "template_types": []}}, {"FunctionCall": {"ast_fields": {"guid": "8e35d372-8f1a-4bc9-be4a-e675f5061b8b", "name": "next", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "eaca833b-b93d-4fbd-a300-7a61c1c9faa4", "childs_guid": [], "full_range": {"start_byte": 3301, "end_byte": 3311, "start_point": {"row": 165, "column": 12}, "end_point": {"row": 165, "column": 22}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": "e1bf219a-5d35-477c-a167-75cf3392c469", "is_error": false}, "template_types": []}}, {"VariableUsage": {"ast_fields": {"guid": "000a17f8-59b5-46bf-837f-845a73df6da7", "name": "name", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "c7c264a9-601b-46e0-b2d9-1feac5907200", "childs_guid": [], "full_range": {"start_byte": 216, "end_byte": 220, "start_point": {"row": 10, "column": 23}, "end_point": {"row": 10, "column": 27}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}}}, {"VariableUsage": {"ast_fields": {"guid": "8f415a3c-a17b-45b3-be9b-2787791e75b4", "name": "item", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "eaca833b-b93d-4fbd-a300-7a61c1c9faa4", "childs_guid": [], "full_range": {"start_byte": 1263, "end_byte": 1267, "start_point": {"row": 66, "column": 24}, "end_point": {"row": 66, "column": 28}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}}}, {"VariableUsage": {"ast_fields": {"guid": "29884bc2-e3d6-4ecb-8fa1-0270d7d76f8c", "name": "index", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "eaca833b-b93d-4fbd-a300-7a61c1c9faa4", "childs_guid": [], "full_range": {"start_byte": 1269, "end_byte": 1274, "start_point": {"row": 66, "column": 30}, "end_point": {"row": 66, "column": 35}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}}}, {"VariableUsage": {"ast_fields": {"guid": "058ef75f-089c-4ac2-af59-7f538ed0bfad", "name": "array", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "eaca833b-b93d-4fbd-a300-7a61c1c9faa4", "childs_guid": [], "full_range": {"start_byte": 1276, "end_byte": 1281, "start_point": {"row": 66, "column": 37}, "end_point": {"row": 66, "column": 42}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}}}, {"VariableUsage": {"ast_fields": {"guid": "1f022b3d-297e-4415-9f04-396ee8fe46ea", "name": "console", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "eaca833b-b93d-4fbd-a300-7a61c1c9faa4", "childs_guid": [], "full_range": {"start_byte": 1355, "end_byte": 1362, "start_point": {"row": 72, "column": 4}, "end_point": {"row": 72, "column": 11}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}}}, {"FunctionCall": {"ast_fields": {"guid": "be28a555-e748-4eb3-93a7-53c374a81a90", "name": "log", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "eaca833b-b93d-4fbd-a300-7a61c1c9faa4", "childs_guid": [], "full_range": {"start_byte": 1402, "end_byte": 1433, "start_point": {"row": 74, "column": 4}, "end_point": {"row": 74, "column": 35}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": "6fe1cb2d-37e2-4741-927f-d2b3fe6e1542", "is_error": false}, "template_types": []}}, {"VariableUsage": {"ast_fields": {"guid": "1b5108c6-7ad8-499a-9073-26415250c4f7", "name": "console", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "eaca833b-b93d-4fbd-a300-7a61c1c9faa4", "childs_guid": [], "full_range": {"start_byte": 1480, "end_byte": 1487, "start_point": {"row": 79, "column": 4}, "end_point": {"row": 79, "column": 11}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}}}, {"VariableUsage": {"ast_fields": {"guid": "1ca5b8f7-40b9-423f-92d1-8c665d92dcf9", "name": "i", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "eaca833b-b93d-4fbd-a300-7a61c1c9faa4", "childs_guid": [], "full_range": {"start_byte": 1492, "end_byte": 1493, "start_point": {"row": 79, "column": 16}, "end_point": {"row": 79, "column": 17}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}}}, {"CommentDefinition": {"ast_fields": {"guid": "866b0c4b-5181-4379-b959-793c73d1e842", "name": "", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "77407ebc-eda5-4870-8846-4058b7ca9d2e", "childs_guid": [], "full_range": {"start_byte": 2117, "end_byte": 2147, "start_point": {"row": 108, "column": 36}, "end_point": {"row": 108, "column": 66}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}}}, {"VariableUsage": {"ast_fields": {"guid": "1aa8bda7-cb86-4131-93a6-10df64ddf49c", "name": "a", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "b103362c-c908-4463-93e3-8ed8719bd34d", "childs_guid": [], "full_range": {"start_byte": 2488, "end_byte": 2489, "start_point": {"row": 124, "column": 11}, "end_point": {"row": 124, "column": 12}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}}}, {"VariableUsage": {"ast_fields": {"guid": "a5b320ae-a98c-4e3b-91c2-947e7f5f328d", "name": "b", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "b103362c-c908-4463-93e3-8ed8719bd34d", "childs_guid": [], "full_range": {"start_byte": 2492, "end_byte": 2493, "start_point": {"row": 124, "column": 15}, "end_point": {"row": 124, "column": 16}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}}}, {"VariableUsage": {"ast_fields": {"guid": "94e96f7c-9aad-4bef-8e12-4a04a832e226", "name": "name", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "a5b70c6f-dfa8-46f8-95a7-181dc7343258", "childs_guid": [], "full_range": {"start_byte": 2966, "end_byte": 2975, "start_point": {"row": 148, "column": 4}, "end_point": {"row": 148, "column": 13}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": "736f999e-2fa5-4047-b03e-df141c86381e", "is_error": false}}}, {"VariableUsage": {"ast_fields": {"guid": "acffa283-bc27-418d-9f3b-94eafe0ee9ee", "name": "name", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "a5b70c6f-dfa8-46f8-95a7-181dc7343258", "childs_guid": [], "full_range": {"start_byte": 2978, "end_byte": 2982, "start_point": {"row": 148, "column": 16}, "end_point": {"row": 148, "column": 20}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}}}, {"VariableUsage": {"ast_fields": {"guid": "a5f42d75-f60f-49d0-992e-91f39dabd48e", "name": "age", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "a5b70c6f-dfa8-46f8-95a7-181dc7343258", "childs_guid": [], "full_range": {"start_byte": 2988, "end_byte": 2996, "start_point": {"row": 149, "column": 4}, "end_point": {"row": 149, "column": 12}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": "1c70715f-a105-41c5-ae32-b3a704f98f60", "is_error": false}}}, {"VariableUsage": {"ast_fields": {"guid": "d92d5735-5d67-4fed-b2ea-4ef4ca37f16d", "name": "age", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "a5b70c6f-dfa8-46f8-95a7-181dc7343258", "childs_guid": [], "full_range": {"start_byte": 2999, "end_byte": 3002, "start_point": {"row": 149, "column": 15}, "end_point": {"row": 149, "column": 18}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}}}, {"VariableUsage": {"ast_fields": {"guid": "2e5fb68e-46f6-4114-b686-cc8ca80075c0", "name": "gen", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "eaca833b-b93d-4fbd-a300-7a61c1c9faa4", "childs_guid": [], "full_range": {"start_byte": 3256, "end_byte": 3259, "start_point": {"row": 164, "column": 12}, "end_point": {"row": 164, "column": 15}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}}}, {"VariableUsage": {"ast_fields": {"guid": "e1bf219a-5d35-477c-a167-75cf3392c469", "name": "gen", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "eaca833b-b93d-4fbd-a300-7a61c1c9faa4", "childs_guid": [], "full_range": {"start_byte": 3301, "end_byte": 3304, "start_point": {"row": 165, "column": 12}, "end_point": {"row": 165, "column": 15}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}}}, {"FunctionCall": {"ast_fields": {"guid": "b77ceacd-b893-4be8-a2a2-7e2760ac0770", "name": "log", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "eaca833b-b93d-4fbd-a300-7a61c1c9faa4", "childs_guid": [], "full_range": {"start_byte": 1289, "end_byte": 1313, "start_point": {"row": 67, "column": 4}, "end_point": {"row": 67, "column": 28}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": "9932327c-d698-4c7d-b430-e230e9b64866", "is_error": false}, "template_types": []}}, {"VariableUsage": {"ast_fields": {"guid": "6fe1cb2d-37e2-4741-927f-d2b3fe6e1542", "name": "console", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "eaca833b-b93d-4fbd-a300-7a61c1c9faa4", "childs_guid": [], "full_range": {"start_byte": 1402, "end_byte": 1409, "start_point": {"row": 74, "column": 4}, "end_point": {"row": 74, "column": 11}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}}}, {"FunctionCall": {"ast_fields": {"guid": "759e75d1-bfaa-4eaa-a4cb-0fd15e2f5ebc", "name": "alert", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "eaca833b-b93d-4fbd-a300-7a61c1c9faa4", "childs_guid": [], "full_range": {"start_byte": 1587, "end_byte": 1611, "start_point": {"row": 84, "column": 4}, "end_point": {"row": 84, "column": 28}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": "88c41d46-e0fb-46ac-b60a-ebd5c2db9af1", "is_error": false}, "template_types": []}}, {"FunctionCall": {"ast_fields": {"guid": "e6ffbc3d-0315-4918-97ad-76d7a6e432c5", "name": "", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "77407ebc-eda5-4870-8846-4058b7ca9d2e", "childs_guid": [], "full_range": {"start_byte": 2089, "end_byte": 2115, "start_point": {"row": 108, "column": 8}, "end_point": {"row": 108, "column": 34}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": "8a75fd5d-92a2-47ce-93b2-befe6ca013da", "is_error": false}, "template_types": []}}, {"VariableUsage": {"ast_fields": {"guid": "1632b189-05f6-4c1d-a8d0-f9d459343905", "name": "a", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "b5290c9d-c5da-47cb-b8b0-ab7bef613052", "childs_guid": [], "full_range": {"start_byte": 2612, "end_byte": 2613, "start_point": {"row": 131, "column": 11}, "end_point": {"row": 131, "column": 12}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}}}, {"VariableUsage": {"ast_fields": {"guid": "9e23b32f-41e4-46cc-9541-1d4ef3443e0c", "name": "b", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "b5290c9d-c5da-47cb-b8b0-ab7bef613052", "childs_guid": [], "full_range": {"start_byte": 2616, "end_byte": 2617, "start_point": {"row": 131, "column": 15}, "end_point": {"row": 131, "column": 16}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}}}, {"VariableUsage": {"ast_fields": {"guid": "07253e75-d668-4e95-b893-4dab9a3cf61c", "name": "a", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "ffb10970-5d7e-41cc-af5b-1f0bca4a347d", "childs_guid": [], "full_range": {"start_byte": 2721, "end_byte": 2722, "start_point": {"row": 137, "column": 11}, "end_point": {"row": 137, "column": 12}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}}}, {"VariableUsage": {"ast_fields": {"guid": "f4ccb55a-c40b-4944-beb1-30ec68da2c43", "name": "b", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "ffb10970-5d7e-41cc-af5b-1f0bca4a347d", "childs_guid": [], "full_range": {"start_byte": 2725, "end_byte": 2726, "start_point": {"row": 137, "column": 15}, "end_point": {"row": 137, "column": 16}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}}}, {"VariableUsage": {"ast_fields": {"guid": "8579e584-2a46-4ab4-8b34-ad951d0e1ff9", "name": "height", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "9ec5e55c-aa64-4a16-9791-79dc7415f1db", "childs_guid": [], "full_range": {"start_byte": 624, "end_byte": 635, "start_point": {"row": 35, "column": 8}, "end_point": {"row": 35, "column": 19}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": "3533d06b-7fe6-4ba0-81aa-17676e913a30", "is_error": false}}}, {"VariableUsage": {"ast_fields": {"guid": "b63ed477-233f-4889-86ab-f320be88e521", "name": "height", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "9ec5e55c-aa64-4a16-9791-79dc7415f1db", "childs_guid": [], "full_range": {"start_byte": 638, "end_byte": 644, "start_point": {"row": 35, "column": 22}, "end_point": {"row": 35, "column": 28}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}}}, {"VariableUsage": {"ast_fields": {"guid": "c5f8791a-3903-41b9-8723-42f0cd23377f", "name": "width", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "9ec5e55c-aa64-4a16-9791-79dc7415f1db", "childs_guid": [], "full_range": {"start_byte": 654, "end_byte": 664, "start_point": {"row": 36, "column": 8}, "end_point": {"row": 36, "column": 18}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": "4b21d07c-6e24-414c-86c5-4237c6760620", "is_error": false}}}, {"VariableUsage": {"ast_fields": {"guid": "53491bad-40e8-4eb9-b376-740458131b6d", "name": "width", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "9ec5e55c-aa64-4a16-9791-79dc7415f1db", "childs_guid": [], "full_range": {"start_byte": 667, "end_byte": 672, "start_point": {"row": 36, "column": 21}, "end_point": {"row": 36, "column": 26}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}}}, {"VariableUsage": {"ast_fields": {"guid": "9932327c-d698-4c7d-b430-e230e9b64866", "name": "console", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "eaca833b-b93d-4fbd-a300-7a61c1c9faa4", "childs_guid": [], "full_range": {"start_byte": 1289, "end_byte": 1296, "start_point": {"row": 67, "column": 4}, "end_point": {"row": 67, "column": 11}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}}}, {"VariableUsage": {"ast_fields": {"guid": "8f2b5f07-d593-4d48-a224-97c40de0d7cb", "name": "item", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "eaca833b-b93d-4fbd-a300-7a61c1c9faa4", "childs_guid": [], "full_range": {"start_byte": 1301, "end_byte": 1305, "start_point": {"row": 67, "column": 16}, "end_point": {"row": 67, "column": 20}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}}}, {"VariableUsage": {"ast_fields": {"guid": "0cc98ef3-df44-4598-96f2-353b9e3aeec8", "name": "index", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "eaca833b-b93d-4fbd-a300-7a61c1c9faa4", "childs_guid": [], "full_range": {"start_byte": 1307, "end_byte": 1312, "start_point": {"row": 67, "column": 22}, "end_point": {"row": 67, "column": 27}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}}}, {"VariableUsage": {"ast_fields": {"guid": "c4886ded-cbba-4bc0-92de-2e33f51d72d4", "name": "firstName", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "2c523358-8aae-4a32-ab42-b0e024e3f985", "childs_guid": [], "full_range": {"start_byte": 1698, "end_byte": 1712, "start_point": {"row": 90, "column": 8}, "end_point": {"row": 90, "column": 22}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": "089b2c1d-52af-4ab1-bd07-b8fac3c9e079", "is_error": false}}}, {"VariableUsage": {"ast_fields": {"guid": "a79c7993-1260-4fc4-becf-08ccfe30b104", "name": "firstName", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "2c523358-8aae-4a32-ab42-b0e024e3f985", "childs_guid": [], "full_range": {"start_byte": 1715, "end_byte": 1724, "start_point": {"row": 90, "column": 25}, "end_point": {"row": 90, "column": 34}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}}}, {"VariableUsage": {"ast_fields": {"guid": "9600771b-ea9c-48e6-85b2-381a32427af0", "name": "lastName", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "2c523358-8aae-4a32-ab42-b0e024e3f985", "childs_guid": [], "full_range": {"start_byte": 1734, "end_byte": 1747, "start_point": {"row": 91, "column": 8}, "end_point": {"row": 91, "column": 21}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": "fa0bb242-6f5a-4360-a841-d2a4b7d81aa4", "is_error": false}}}, {"VariableUsage": {"ast_fields": {"guid": "6b91db5d-911d-479f-95bb-d9fbba63e354", "name": "lastName", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "2c523358-8aae-4a32-ab42-b0e024e3f985", "childs_guid": [], "full_range": {"start_byte": 1750, "end_byte": 1758, "start_point": {"row": 91, "column": 24}, "end_point": {"row": 91, "column": 32}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}}}, {"VariableUsage": {"ast_fields": {"guid": "f6080628-df01-45e9-b9e4-9caf9c7f4a91", "name": "lastName", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "f3b3cfe9-204b-46d5-a356-7f2eca8fafa1", "childs_guid": [], "full_range": {"start_byte": 1836, "end_byte": 1849, "start_point": {"row": 96, "column": 38}, "end_point": {"row": 96, "column": 51}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": "f128a899-e807-48f3-9c08-c9fa04c69929", "is_error": false}}}, {"VariableUsage": {"ast_fields": {"guid": "612f727a-64a6-4c0e-84d8-62aa118f5a5d", "name": "firstName", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "77407ebc-eda5-4870-8846-4058b7ca9d2e", "childs_guid": [], "full_range": {"start_byte": 2095, "end_byte": 2104, "start_point": {"row": 108, "column": 14}, "end_point": {"row": 108, "column": 23}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}}}, {"VariableUsage": {"ast_fields": {"guid": "27db3598-f53f-4d55-a1b0-429c912c074c", "name": "lastName", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "77407ebc-eda5-4870-8846-4058b7ca9d2e", "childs_guid": [], "full_range": {"start_byte": 2106, "end_byte": 2114, "start_point": {"row": 108, "column": 25}, "end_point": {"row": 108, "column": 33}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}}}, {"VariableUsage": {"ast_fields": {"guid": "6a9ad822-9cc5-4c57-b2b3-114a51262f1b", "name": "position", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "77407ebc-eda5-4870-8846-4058b7ca9d2e", "childs_guid": [], "full_range": {"start_byte": 2156, "end_byte": 2169, "start_point": {"row": 109, "column": 8}, "end_point": {"row": 109, "column": 21}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": "8f7127bb-2f77-4abb-8983-d3c35c3ee450", "is_error": false}}}, {"VariableUsage": {"ast_fields": {"guid": "687e673a-7d69-4683-8597-130c56751a98", "name": "position", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "77407ebc-eda5-4870-8846-4058b7ca9d2e", "childs_guid": [], "full_range": {"start_byte": 2172, "end_byte": 2180, "start_point": {"row": 109, "column": 24}, "end_point": {"row": 109, "column": 32}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}}}, {"VariableUsage": {"ast_fields": {"guid": "18fa874f-1649-4569-ad40-634990de79de", "name": "position", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "e3d799ed-fea6-4628-8e28-70a28b69257a", "childs_guid": [], "full_range": {"start_byte": 2270, "end_byte": 2283, "start_point": {"row": 114, "column": 41}, "end_point": {"row": 114, "column": 54}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": "229a95a5-8d2c-425a-9f06-9b39aad9cbf4", "is_error": false}}}, {"FunctionCall": {"ast_fields": {"guid": "7a8bbf57-96e9-4f13-86e6-a87ef7f650d8", "name": "log", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "eaca833b-b93d-4fbd-a300-7a61c1c9faa4", "childs_guid": [], "full_range": {"start_byte": 2842, "end_byte": 2872, "start_point": {"row": 143, "column": 4}, "end_point": {"row": 143, "column": 34}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": "830cc828-c149-49ef-a853-047cd1f8ae82", "is_error": false}, "template_types": []}}, {"VariableUsage": {"ast_fields": {"guid": "0f023451-3b6a-455c-b3ac-bb512b70209c", "name": "lastName", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "643997a5-c7b6-4523-84a8-4e94b500cf74", "childs_guid": [], "full_range": {"start_byte": 526, "end_byte": 539, "start_point": {"row": 28, "column": 38}, "end_point": {"row": 28, "column": 51}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": "000a3d09-92f2-4db2-8bc6-adb83dddf778", "is_error": false}}}, {"VariableUsage": {"ast_fields": {"guid": "e629aaeb-d61e-4168-b176-820f4ed96262", "name": "#height", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "0c8b553f-5c6a-405b-a3f8-78c4e0f6c07f", "childs_guid": [], "full_range": {"start_byte": 843, "end_byte": 855, "start_point": {"row": 45, "column": 8}, "end_point": {"row": 45, "column": 20}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": "4d133e14-9900-4cb1-853e-74919bd065c0", "is_error": false}}}, {"VariableUsage": {"ast_fields": {"guid": "f552ef18-b987-40cd-beaf-48a5cd610d6c", "name": "height", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "0c8b553f-5c6a-405b-a3f8-78c4e0f6c07f", "childs_guid": [], "full_range": {"start_byte": 858, "end_byte": 864, "start_point": {"row": 45, "column": 23}, "end_point": {"row": 45, "column": 29}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}}}, {"VariableUsage": {"ast_fields": {"guid": "44c00998-0c7f-4364-b111-ace56a33861c", "name": "#width", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "0c8b553f-5c6a-405b-a3f8-78c4e0f6c07f", "childs_guid": [], "full_range": {"start_byte": 874, "end_byte": 885, "start_point": {"row": 46, "column": 8}, "end_point": {"row": 46, "column": 19}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": "c38db3c6-4659-44c3-85d9-33bc0e9abe4b", "is_error": false}}}, {"VariableUsage": {"ast_fields": {"guid": "0a11f993-6671-482c-9444-2a4f3c61ca11", "name": "width", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "0c8b553f-5c6a-405b-a3f8-78c4e0f6c07f", "childs_guid": [], "full_range": {"start_byte": 888, "end_byte": 893, "start_point": {"row": 46, "column": 22}, "end_point": {"row": 46, "column": 27}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}}}, {"VariableUsage": {"ast_fields": {"guid": "d0d2990e-1622-4c67-be3f-53aa71871d82", "name": "height", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "c4ca4f4a-39fc-433b-966f-f5b28ec22396", "childs_guid": [], "full_range": {"start_byte": 1054, "end_byte": 1065, "start_point": {"row": 55, "column": 8}, "end_point": {"row": 55, "column": 19}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": "9f8eaab6-8ed7-491d-be23-6cb9636c7431", "is_error": false}}}, {"VariableUsage": {"ast_fields": {"guid": "*************-43ad-97c7-c427e1e9662c", "name": "height", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "c4ca4f4a-39fc-433b-966f-f5b28ec22396", "childs_guid": [], "full_range": {"start_byte": 1068, "end_byte": 1074, "start_point": {"row": 55, "column": 22}, "end_point": {"row": 55, "column": 28}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}}}, {"VariableUsage": {"ast_fields": {"guid": "5a122d9b-825c-4c03-8c3f-3071b035ba63", "name": "width", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "c4ca4f4a-39fc-433b-966f-f5b28ec22396", "childs_guid": [], "full_range": {"start_byte": 1084, "end_byte": 1094, "start_point": {"row": 56, "column": 8}, "end_point": {"row": 56, "column": 18}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": "a80fe3c9-ec8d-4bcd-a2a9-be2b5f7268ba", "is_error": false}}}, {"VariableUsage": {"ast_fields": {"guid": "b87d2dd5-2b48-4d66-a412-f6be784dc676", "name": "width", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "c4ca4f4a-39fc-433b-966f-f5b28ec22396", "childs_guid": [], "full_range": {"start_byte": 1097, "end_byte": 1102, "start_point": {"row": 56, "column": 21}, "end_point": {"row": 56, "column": 26}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}}}, {"VariableUsage": {"ast_fields": {"guid": "4922affb-1612-4da9-9a7e-90dac6f98ce3", "name": "firstName", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "f3b3cfe9-204b-46d5-a356-7f2eca8fafa1", "childs_guid": [], "full_range": {"start_byte": 1813, "end_byte": 1827, "start_point": {"row": 96, "column": 15}, "end_point": {"row": 96, "column": 29}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": "2bf59185-bb32-4d95-a1fd-747e037a9bd6", "is_error": false}}}, {"FunctionCall": {"ast_fields": {"guid": "0dc36c8c-928d-4f4c-901c-1e6154ffe01d", "name": "fullName", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "e3d799ed-fea6-4628-8e28-70a28b69257a", "childs_guid": [], "full_range": {"start_byte": 2244, "end_byte": 2260, "start_point": {"row": 114, "column": 15}, "end_point": {"row": 114, "column": 31}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": "8aa931d2-a43a-4a5c-92da-07e8d525eafc", "is_error": false}, "template_types": []}}, {"VariableUsage": {"ast_fields": {"guid": "830cc828-c149-49ef-a853-047cd1f8ae82", "name": "console", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "eaca833b-b93d-4fbd-a300-7a61c1c9faa4", "childs_guid": [], "full_range": {"start_byte": 2842, "end_byte": 2849, "start_point": {"row": 143, "column": 4}, "end_point": {"row": 143, "column": 11}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}}}, {"VariableUsage": {"ast_fields": {"guid": "2ee4c56d-dba9-45da-b3fb-0793a5ec932f", "name": "id", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "eaca833b-b93d-4fbd-a300-7a61c1c9faa4", "childs_guid": [], "full_range": {"start_byte": 3204, "end_byte": 3206, "start_point": {"row": 159, "column": 14}, "end_point": {"row": 159, "column": 16}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}}}, {"VariableUsage": {"ast_fields": {"guid": "c79c6503-08b1-456f-ab3e-95bd2fa99781", "name": "firstName", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "643997a5-c7b6-4523-84a8-4e94b500cf74", "childs_guid": [], "full_range": {"start_byte": 503, "end_byte": 517, "start_point": {"row": 28, "column": 15}, "end_point": {"row": 28, "column": 29}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": "331ec3aa-7ec2-4574-8e28-bf24474a45fe", "is_error": false}}}, {"VariableUsage": {"ast_fields": {"guid": "316ec5a4-1bf8-4543-aa49-046fa283f37b", "name": "div", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "2ff80ba3-1674-40d0-95b0-ca561c4243d1", "childs_guid": [], "full_range": {"start_byte": 3451, "end_byte": 3454, "start_point": {"row": 172, "column": 13}, "end_point": {"row": 172, "column": 16}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}}}, {"VariableUsage": {"ast_fields": {"guid": "7c8b7cd7-6148-4b4a-8117-d939389059bc", "name": "div", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "2ff80ba3-1674-40d0-95b0-ca561c4243d1", "childs_guid": [], "full_range": {"start_byte": 3550, "end_byte": 3553, "start_point": {"row": 175, "column": 14}, "end_point": {"row": 175, "column": 17}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}}}, {"VariableUsage": {"ast_fields": {"guid": "4e948e7f-e1bd-4f7a-8b7e-85251ad1a515", "name": "h1", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "2ff80ba3-1674-40d0-95b0-ca561c4243d1", "childs_guid": [], "full_range": {"start_byte": 3473, "end_byte": 3475, "start_point": {"row": 173, "column": 17}, "end_point": {"row": 173, "column": 19}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}}}, {"VariableUsage": {"ast_fields": {"guid": "afd8062e-c9b3-486a-9217-b1e218933619", "name": "h1", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "2ff80ba3-1674-40d0-95b0-ca561c4243d1", "childs_guid": [], "full_range": {"start_byte": 3491, "end_byte": 3493, "start_point": {"row": 173, "column": 35}, "end_point": {"row": 173, "column": 37}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}}}, {"VariableUsage": {"ast_fields": {"guid": "5c01939f-7ce2-4203-8d46-b447222ac458", "name": "p", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "2ff80ba3-1674-40d0-95b0-ca561c4243d1", "childs_guid": [], "full_range": {"start_byte": 3512, "end_byte": 3513, "start_point": {"row": 174, "column": 17}, "end_point": {"row": 174, "column": 18}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}}}, {"VariableUsage": {"ast_fields": {"guid": "33412bc0-0f08-4809-a45e-31906cda21ae", "name": "p", "language": "JavaScript", "file_path": "file:///main.js", "namespace": "", "parent_guid": "2ff80ba3-1674-40d0-95b0-ca561c4243d1", "childs_guid": [], "full_range": {"start_byte": 3533, "end_byte": 3534, "start_point": {"row": 174, "column": 38}, "end_point": {"row": 174, "column": 39}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}}}]