[{"ImportDeclaration": {"ast_fields": {"guid": "bf36bb8d-adb9-42b5-8b17-75564765add0", "name": "SomeExport", "language": "TypeScript", "file_path": "file:///main.ts", "namespace": "", "parent_guid": "b9edf75c-e6cc-40f0-8304-15bac6bb6406", "childs_guid": [], "full_range": {"start_byte": 0, "end_byte": 57, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 57}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}, "path_components": [".", "SomeModule", "SomeExport"], "alias": null, "import_type": "UserModule", "filepath_ref": null}}, {"ImportDeclaration": {"ast_fields": {"guid": "68837184-523f-4af5-813c-fcd4267a1284", "name": "AnotherExport", "language": "TypeScript", "file_path": "file:///main.ts", "namespace": "", "parent_guid": "b9edf75c-e6cc-40f0-8304-15bac6bb6406", "childs_guid": [], "full_range": {"start_byte": 0, "end_byte": 57, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 57}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}, "path_components": [".", "SomeModule", "AnotherExport"], "alias": null, "import_type": "UserModule", "filepath_ref": null}}, {"ImportDeclaration": {"ast_fields": {"guid": "da4bab23-153c-46cc-955c-1549c87c15ea", "name": "", "language": "TypeScript", "file_path": "file:///main.ts", "namespace": "", "parent_guid": "b9edf75c-e6cc-40f0-8304-15bac6bb6406", "childs_guid": [], "full_range": {"start_byte": 58, "end_byte": 80, "start_point": {"row": 1, "column": 0}, "end_point": {"row": 1, "column": 22}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}, "path_components": [".", "SomeModule"], "alias": null, "import_type": "UserModule", "filepath_ref": null}}, {"ImportDeclaration": {"ast_fields": {"guid": "a42c15a7-fcb5-42e6-9644-8f3d40e78d91", "name": "SomeExport", "language": "TypeScript", "file_path": "file:///main.ts", "namespace": "", "parent_guid": "b9edf75c-e6cc-40f0-8304-15bac6bb6406", "childs_guid": [], "full_range": {"start_byte": 81, "end_byte": 132, "start_point": {"row": 2, "column": 0}, "end_point": {"row": 2, "column": 51}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}, "path_components": [".", "SomeModule", "SomeExport"], "alias": "<PERSON><PERSON>", "import_type": "UserModule", "filepath_ref": null}}, {"ImportDeclaration": {"ast_fields": {"guid": "c2ebcbce-99be-4463-a237-3871d31a2f2e", "name": "SomeModule", "language": "TypeScript", "file_path": "file:///main.ts", "namespace": "", "parent_guid": "b9edf75c-e6cc-40f0-8304-15bac6bb6406", "childs_guid": [], "full_range": {"start_byte": 133, "end_byte": 176, "start_point": {"row": 3, "column": 0}, "end_point": {"row": 3, "column": 43}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}, "path_components": [".", "SomeModule"], "alias": "SomeModule", "import_type": "UserModule", "filepath_ref": null}}, {"ImportDeclaration": {"ast_fields": {"guid": "8a9d90bf-55ce-4b9c-b1b6-fa502ef95529", "name": "SomeDefaultExport", "language": "TypeScript", "file_path": "file:///main.ts", "namespace": "", "parent_guid": "b9edf75c-e6cc-40f0-8304-15bac6bb6406", "childs_guid": [], "full_range": {"start_byte": 177, "end_byte": 222, "start_point": {"row": 4, "column": 0}, "end_point": {"row": 4, "column": 45}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}, "path_components": [".", "SomeModule", "SomeDefaultExport"], "alias": null, "import_type": "UserModule", "filepath_ref": null}}, {"CommentDefinition": {"ast_fields": {"guid": "def289ae-84bc-4e3d-b41e-aeae24df1bc3", "name": "", "language": "TypeScript", "file_path": "file:///main.ts", "namespace": "", "parent_guid": "b9edf75c-e6cc-40f0-8304-15bac6bb6406", "childs_guid": [], "full_range": {"start_byte": 224, "end_byte": 238, "start_point": {"row": 6, "column": 0}, "end_point": {"row": 6, "column": 14}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}}}, {"StructDeclaration": {"ast_fields": {"guid": "4f9bf64f-708c-400f-a988-bf80dd6ff5e1", "name": "InOrOut", "language": "TypeScript", "file_path": "file:///main.ts", "namespace": "", "parent_guid": "b9edf75c-e6cc-40f0-8304-15bac6bb6406", "childs_guid": [], "full_range": {"start_byte": 350, "end_byte": 407, "start_point": {"row": 11, "column": 0}, "end_point": {"row": 11, "column": 57}}, "declaration_range": {"start_byte": 350, "end_byte": 407, "start_point": {"row": 11, "column": 0}, "end_point": {"row": 11, "column": 57}}, "definition_range": {"start_byte": 368, "end_byte": 406, "start_point": {"row": 11, "column": 18}, "end_point": {"row": 11, "column": 56}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}, "template_types": [{"name": "T", "inference_info": null, "is_pod": false, "namespace": "", "guid": null, "nested_types": []}], "inherited_types": []}}, {"CommentDefinition": {"ast_fields": {"guid": "3f7c1a46-d848-4d78-ae2e-b3f8cf9a671a", "name": "", "language": "TypeScript", "file_path": "file:///main.ts", "namespace": "", "parent_guid": "b9edf75c-e6cc-40f0-8304-15bac6bb6406", "childs_guid": [], "full_range": {"start_byte": 632, "end_byte": 640, "start_point": {"row": 23, "column": 0}, "end_point": {"row": 23, "column": 8}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}}}, {"CommentDefinition": {"ast_fields": {"guid": "818533ef-76e2-47d9-bd7c-88dc2c677072", "name": "", "language": "TypeScript", "file_path": "file:///main.ts", "namespace": "", "parent_guid": "b9edf75c-e6cc-40f0-8304-15bac6bb6406", "childs_guid": [], "full_range": {"start_byte": 701, "end_byte": 715, "start_point": {"row": 26, "column": 0}, "end_point": {"row": 26, "column": 14}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}}}, {"CommentDefinition": {"ast_fields": {"guid": "7053900a-dbea-4de6-9dbf-31a74fcef370", "name": "", "language": "TypeScript", "file_path": "file:///main.ts", "namespace": "", "parent_guid": "b9edf75c-e6cc-40f0-8304-15bac6bb6406", "childs_guid": [], "full_range": {"start_byte": 807, "end_byte": 815, "start_point": {"row": 33, "column": 0}, "end_point": {"row": 33, "column": 8}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}}}, {"CommentDefinition": {"ast_fields": {"guid": "bd3b004b-da6b-4ed7-aca9-c499fc0e185a", "name": "", "language": "TypeScript", "file_path": "file:///main.ts", "namespace": "", "parent_guid": "b9edf75c-e6cc-40f0-8304-15bac6bb6406", "childs_guid": [], "full_range": {"start_byte": 847, "end_byte": 854, "start_point": {"row": 35, "column": 0}, "end_point": {"row": 35, "column": 7}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}}}, {"ClassFieldDeclaration": {"ast_fields": {"guid": "ca9ebc1e-4e5e-49a0-8e81-1daac904e9d8", "name": "Up", "language": "TypeScript", "file_path": "file:///main.ts", "namespace": "", "parent_guid": "e9ca627b-17d8-4930-ba4e-854a0fdde033", "childs_guid": [], "full_range": {"start_byte": 877, "end_byte": 879, "start_point": {"row": 37, "column": 4}, "end_point": {"row": 37, "column": 6}}, "declaration_range": {"start_byte": 877, "end_byte": 879, "start_point": {"row": 37, "column": 4}, "end_point": {"row": 37, "column": 6}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}, "type_": {"name": null, "inference_info": null, "is_pod": false, "namespace": "", "guid": null, "nested_types": []}}}, {"ClassFieldDeclaration": {"ast_fields": {"guid": "42cceed2-007b-4953-abac-b0be4cdfff0d", "name": "Down", "language": "TypeScript", "file_path": "file:///main.ts", "namespace": "", "parent_guid": "e9ca627b-17d8-4930-ba4e-854a0fdde033", "childs_guid": [], "full_range": {"start_byte": 885, "end_byte": 889, "start_point": {"row": 38, "column": 4}, "end_point": {"row": 38, "column": 8}}, "declaration_range": {"start_byte": 885, "end_byte": 889, "start_point": {"row": 38, "column": 4}, "end_point": {"row": 38, "column": 8}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}, "type_": {"name": null, "inference_info": null, "is_pod": false, "namespace": "", "guid": null, "nested_types": []}}}, {"ClassFieldDeclaration": {"ast_fields": {"guid": "5b3f5673-a538-436e-965a-4941a333ca52", "name": "Left", "language": "TypeScript", "file_path": "file:///main.ts", "namespace": "", "parent_guid": "e9ca627b-17d8-4930-ba4e-854a0fdde033", "childs_guid": [], "full_range": {"start_byte": 895, "end_byte": 899, "start_point": {"row": 39, "column": 4}, "end_point": {"row": 39, "column": 8}}, "declaration_range": {"start_byte": 895, "end_byte": 899, "start_point": {"row": 39, "column": 4}, "end_point": {"row": 39, "column": 8}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}, "type_": {"name": null, "inference_info": null, "is_pod": false, "namespace": "", "guid": null, "nested_types": []}}}, {"ClassFieldDeclaration": {"ast_fields": {"guid": "7f59b20c-3b09-4560-842b-6ac71e1d87ed", "name": "Right", "language": "TypeScript", "file_path": "file:///main.ts", "namespace": "", "parent_guid": "e9ca627b-17d8-4930-ba4e-854a0fdde033", "childs_guid": [], "full_range": {"start_byte": 905, "end_byte": 910, "start_point": {"row": 40, "column": 4}, "end_point": {"row": 40, "column": 9}}, "declaration_range": {"start_byte": 905, "end_byte": 910, "start_point": {"row": 40, "column": 4}, "end_point": {"row": 40, "column": 9}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}, "type_": {"name": null, "inference_info": null, "is_pod": false, "namespace": "", "guid": null, "nested_types": []}}}, {"StructDeclaration": {"ast_fields": {"guid": "e9ca627b-17d8-4930-ba4e-854a0fdde033", "name": "Direction1", "language": "TypeScript", "file_path": "file:///main.ts", "namespace": "", "parent_guid": "b9edf75c-e6cc-40f0-8304-15bac6bb6406", "childs_guid": ["ca9ebc1e-4e5e-49a0-8e81-1daac904e9d8", "42cceed2-007b-4953-abac-b0be4cdfff0d", "5b3f5673-a538-436e-965a-4941a333ca52", "7f59b20c-3b09-4560-842b-6ac71e1d87ed"], "full_range": {"start_byte": 855, "end_byte": 913, "start_point": {"row": 36, "column": 0}, "end_point": {"row": 41, "column": 1}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}, "template_types": [], "inherited_types": []}}, {"CommentDefinition": {"ast_fields": {"guid": "d78923ea-8fa6-4fbb-ad80-5498904bb764", "name": "", "language": "TypeScript", "file_path": "file:///main.ts", "namespace": "", "parent_guid": "b9edf75c-e6cc-40f0-8304-15bac6bb6406", "childs_guid": [], "full_range": {"start_byte": 915, "end_byte": 925, "start_point": {"row": 43, "column": 0}, "end_point": {"row": 43, "column": 10}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}}}, {"StructDeclaration": {"ast_fields": {"guid": "5a9e441a-4499-436c-a34f-b943a903ca5d", "name": "User", "language": "TypeScript", "file_path": "file:///main.ts", "namespace": "", "parent_guid": "b9edf75c-e6cc-40f0-8304-15bac6bb6406", "childs_guid": ["fd08fe81-7f6f-40db-98d9-87f69b03dcec", "c660a91e-6b3c-4950-a686-ee817e0e5e57"], "full_range": {"start_byte": 926, "end_byte": 976, "start_point": {"row": 44, "column": 0}, "end_point": {"row": 47, "column": 2}}, "declaration_range": {"start_byte": 926, "end_byte": 976, "start_point": {"row": 44, "column": 0}, "end_point": {"row": 47, "column": 2}}, "definition_range": {"start_byte": 938, "end_byte": 975, "start_point": {"row": 44, "column": 12}, "end_point": {"row": 47, "column": 1}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}, "template_types": [], "inherited_types": []}}, {"CommentDefinition": {"ast_fields": {"guid": "7851bab5-38d6-4c26-8415-e3b7d7506dee", "name": "", "language": "TypeScript", "file_path": "file:///main.ts", "namespace": "", "parent_guid": "b9edf75c-e6cc-40f0-8304-15bac6bb6406", "childs_guid": [], "full_range": {"start_byte": 1032, "end_byte": 1049, "start_point": {"row": 54, "column": 0}, "end_point": {"row": 54, "column": 17}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}}}, {"CommentDefinition": {"ast_fields": {"guid": "90328a46-55d9-4f4e-a1cf-************", "name": "", "language": "TypeScript", "file_path": "file:///main.ts", "namespace": "", "parent_guid": "b9edf75c-e6cc-40f0-8304-15bac6bb6406", "childs_guid": [], "full_range": {"start_byte": 1099, "end_byte": 1111, "start_point": {"row": 58, "column": 0}, "end_point": {"row": 58, "column": 12}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}}}, {"FunctionDeclaration": {"ast_fields": {"guid": "654f08a7-f558-47f3-be49-21696d5a12b0", "name": "addNum", "language": "TypeScript", "file_path": "file:///main.ts", "namespace": "", "parent_guid": "b9edf75c-e6cc-40f0-8304-15bac6bb6406", "childs_guid": ["429abdda-c235-4cc3-b662-21b1b90f701c", "f24a36df-fe7d-4dc3-b622-76e641d8250f", "57eb983a-f46e-46c9-9205-f7efda679e88"], "full_range": {"start_byte": 1112, "end_byte": 1194, "start_point": {"row": 59, "column": 0}, "end_point": {"row": 62, "column": 1}}, "declaration_range": {"start_byte": 1112, "end_byte": 1158, "start_point": {"row": 59, "column": 0}, "end_point": {"row": 59, "column": 46}}, "definition_range": {"start_byte": 1158, "end_byte": 1194, "start_point": {"row": 59, "column": 46}, "end_point": {"row": 62, "column": 1}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}, "template_types": [], "args": [{"name": "x", "type_": {"name": "number", "inference_info": null, "is_pod": true, "namespace": "", "guid": null, "nested_types": []}}, {"name": "y", "type_": {"name": "number", "inference_info": null, "is_pod": true, "namespace": "", "guid": null, "nested_types": []}}], "return_type": {"name": "number", "inference_info": null, "is_pod": true, "namespace": "", "guid": null, "nested_types": []}}}, {"StructDeclaration": {"ast_fields": {"guid": "01208911-dd2a-4735-927b-f50a74405b05", "name": "Point", "language": "TypeScript", "file_path": "file:///main.ts", "namespace": "", "parent_guid": "b9edf75c-e6cc-40f0-8304-15bac6bb6406", "childs_guid": ["383258f6-0ed6-4018-9956-da765329483c", "6b94e540-6bf1-4aa7-bb75-e495d61810aa"], "full_range": {"start_byte": 1196, "end_byte": 1434, "start_point": {"row": 64, "column": 0}, "end_point": {"row": 72, "column": 1}}, "declaration_range": {"start_byte": 1196, "end_byte": 1207, "start_point": {"row": 64, "column": 0}, "end_point": {"row": 64, "column": 11}}, "definition_range": {"start_byte": 1208, "end_byte": 1434, "start_point": {"row": 64, "column": 12}, "end_point": {"row": 72, "column": 1}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}, "template_types": [], "inherited_types": []}}, {"CommentDefinition": {"ast_fields": {"guid": "269b6285-4646-45c9-aa8e-fe1ac489b348", "name": "", "language": "TypeScript", "file_path": "file:///main.ts", "namespace": "", "parent_guid": "b9edf75c-e6cc-40f0-8304-15bac6bb6406", "childs_guid": [], "full_range": {"start_byte": 1436, "end_byte": 1449, "start_point": {"row": 74, "column": 0}, "end_point": {"row": 74, "column": 13}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}}}, {"StructDeclaration": {"ast_fields": {"guid": "ce7738ea-df82-45bd-b802-9ba645f1331f", "name": "UserInterface", "language": "TypeScript", "file_path": "file:///main.ts", "namespace": "", "parent_guid": "b9edf75c-e6cc-40f0-8304-15bac6bb6406", "childs_guid": ["81f2c567-09e5-4479-88bf-6e541e8e8e4c", "095dd73f-d6cb-4ce2-a6b7-c4b9d24af292", "80160bb8-b6b6-4303-80e0-1041a16fc90c"], "full_range": {"start_byte": 1450, "end_byte": 1538, "start_point": {"row": 75, "column": 0}, "end_point": {"row": 79, "column": 1}}, "declaration_range": {"start_byte": 1450, "end_byte": 1473, "start_point": {"row": 75, "column": 0}, "end_point": {"row": 75, "column": 23}}, "definition_range": {"start_byte": 1474, "end_byte": 1538, "start_point": {"row": 75, "column": 24}, "end_point": {"row": 79, "column": 1}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}, "template_types": [], "inherited_types": []}}, {"CommentDefinition": {"ast_fields": {"guid": "b1d908fc-1c99-4450-a13a-0ad1928978a0", "name": "", "language": "TypeScript", "file_path": "file:///main.ts", "namespace": "", "parent_guid": "b9edf75c-e6cc-40f0-8304-15bac6bb6406", "childs_guid": [], "full_range": {"start_byte": 1604, "end_byte": 1614, "start_point": {"row": 86, "column": 0}, "end_point": {"row": 86, "column": 10}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}}}, {"StructDeclaration": {"ast_fields": {"guid": "1e343c61-cc36-42fd-9000-a7f2bf22684c", "name": "Person", "language": "TypeScript", "file_path": "file:///main.ts", "namespace": "", "parent_guid": "b9edf75c-e6cc-40f0-8304-15bac6bb6406", "childs_guid": ["f220583b-6059-4222-a210-77e75830947b", "f0533f88-6c34-4107-b119-efa31781ae82", "ff3cb01f-9bf7-42a0-a4e1-e3b472f85a4a"], "full_range": {"start_byte": 1615, "end_byte": 1764, "start_point": {"row": 87, "column": 0}, "end_point": {"row": 95, "column": 1}}, "declaration_range": {"start_byte": 1615, "end_byte": 1627, "start_point": {"row": 87, "column": 0}, "end_point": {"row": 87, "column": 12}}, "definition_range": {"start_byte": 1628, "end_byte": 1764, "start_point": {"row": 87, "column": 13}, "end_point": {"row": 95, "column": 1}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}, "template_types": [], "inherited_types": []}}, {"StructDeclaration": {"ast_fields": {"guid": "3ae48c44-88ec-482a-a956-93f293f47cbf", "name": "GenericNumber", "language": "TypeScript", "file_path": "file:///main.ts", "namespace": "", "parent_guid": "b9edf75c-e6cc-40f0-8304-15bac6bb6406", "childs_guid": ["845c1b30-1854-4acf-b89f-5ccfb854852b", "fbda431a-6f3a-41f5-8aa4-77bf7483dc3a"], "full_range": {"start_byte": 1803, "end_byte": 1875, "start_point": {"row": 99, "column": 0}, "end_point": {"row": 102, "column": 1}}, "declaration_range": {"start_byte": 1803, "end_byte": 1825, "start_point": {"row": 99, "column": 0}, "end_point": {"row": 99, "column": 22}}, "definition_range": {"start_byte": 1826, "end_byte": 1875, "start_point": {"row": 99, "column": 23}, "end_point": {"row": 102, "column": 1}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}, "template_types": [{"name": "T", "inference_info": null, "is_pod": false, "namespace": "", "guid": null, "nested_types": []}], "inherited_types": []}}, {"CommentDefinition": {"ast_fields": {"guid": "e60bbe05-44b7-4ba9-8e80-ce071a6bc888", "name": "", "language": "TypeScript", "file_path": "file:///main.ts", "namespace": "", "parent_guid": "b9edf75c-e6cc-40f0-8304-15bac6bb6406", "childs_guid": [], "full_range": {"start_byte": 2056, "end_byte": 2069, "start_point": {"row": 108, "column": 40}, "end_point": {"row": 108, "column": 53}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}}}, {"CommentDefinition": {"ast_fields": {"guid": "701421dc-fe47-425e-8515-c9e3be199fb9", "name": "", "language": "TypeScript", "file_path": "file:///main.ts", "namespace": "", "parent_guid": "b9edf75c-e6cc-40f0-8304-15bac6bb6406", "childs_guid": [], "full_range": {"start_byte": 2262, "end_byte": 2278, "start_point": {"row": 114, "column": 65}, "end_point": {"row": 114, "column": 81}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}}}, {"CommentDefinition": {"ast_fields": {"guid": "b2877ffb-4a65-4ff5-9377-5d643567dc2a", "name": "", "language": "TypeScript", "file_path": "file:///main.ts", "namespace": "", "parent_guid": "b9edf75c-e6cc-40f0-8304-15bac6bb6406", "childs_guid": [], "full_range": {"start_byte": 2280, "end_byte": 2291, "start_point": {"row": 116, "column": 0}, "end_point": {"row": 116, "column": 11}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}}}, {"FunctionDeclaration": {"ast_fields": {"guid": "b8a4ec2a-83de-428b-9aee-8a30662e88a2", "name": "getArray", "language": "TypeScript", "file_path": "file:///main.ts", "namespace": "", "parent_guid": "b9edf75c-e6cc-40f0-8304-15bac6bb6406", "childs_guid": ["448bbb26-9bed-4b8a-9177-ca47afba4ae5", "32ee4527-aea8-4c03-9812-87cb1889638b", "14b1155c-1072-4956-aad2-4b0bb6650cbf"], "full_range": {"start_byte": 2292, "end_byte": 2377, "start_point": {"row": 117, "column": 0}, "end_point": {"row": 119, "column": 1}}, "declaration_range": {"start_byte": 2292, "end_byte": 2333, "start_point": {"row": 117, "column": 0}, "end_point": {"row": 117, "column": 41}}, "definition_range": {"start_byte": 2333, "end_byte": 2377, "start_point": {"row": 117, "column": 41}, "end_point": {"row": 119, "column": 1}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}, "template_types": [{"name": "T", "inference_info": null, "is_pod": false, "namespace": "", "guid": null, "nested_types": []}], "args": [{"name": "items", "type_": {"name": "array", "inference_info": null, "is_pod": false, "namespace": "", "guid": null, "nested_types": [{"name": "T", "inference_info": null, "is_pod": false, "namespace": "", "guid": null, "nested_types": []}]}}], "return_type": {"name": "array", "inference_info": null, "is_pod": false, "namespace": "", "guid": null, "nested_types": [{"name": "T", "inference_info": null, "is_pod": false, "namespace": "", "guid": null, "nested_types": []}]}}}, {"CommentDefinition": {"ast_fields": {"guid": "534c858e-d666-4461-a373-2626a111a8cf", "name": "", "language": "TypeScript", "file_path": "file:///main.ts", "namespace": "", "parent_guid": "b9edf75c-e6cc-40f0-8304-15bac6bb6406", "childs_guid": [], "full_range": {"start_byte": 2540, "end_byte": 2567, "start_point": {"row": 127, "column": 0}, "end_point": {"row": 127, "column": 27}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}}}, {"StructDeclaration": {"ast_fields": {"guid": "037b3d16-b01d-4008-9c63-85f70d90df00", "name": "Lengthy", "language": "TypeScript", "file_path": "file:///main.ts", "namespace": "", "parent_guid": "b9edf75c-e6cc-40f0-8304-15bac6bb6406", "childs_guid": ["1b995663-ff61-490a-ac19-4f2b7b20dca6"], "full_range": {"start_byte": 2568, "end_byte": 2609, "start_point": {"row": 128, "column": 0}, "end_point": {"row": 130, "column": 1}}, "declaration_range": {"start_byte": 2568, "end_byte": 2585, "start_point": {"row": 128, "column": 0}, "end_point": {"row": 128, "column": 17}}, "definition_range": {"start_byte": 2586, "end_byte": 2609, "start_point": {"row": 128, "column": 18}, "end_point": {"row": 130, "column": 1}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}, "template_types": [], "inherited_types": []}}, {"FunctionDeclaration": {"ast_fields": {"guid": "de64c75b-2e61-43c7-92b3-c5aa27910fa7", "name": "countAndDescribe", "language": "TypeScript", "file_path": "file:///main.ts", "namespace": "", "parent_guid": "b9edf75c-e6cc-40f0-8304-15bac6bb6406", "childs_guid": ["806da964-b623-4687-a64a-9cb9e0bcffc4", "a3ea76cf-0759-40bd-853d-4f30a86ef8da", "5dd581c9-42fb-471c-8c2e-84ffdf96f2de", "c2fa459c-a3ee-4422-95b8-9a8f6a18e68d", "530b5b2a-2387-41d7-aaff-0e8e9ab1a1dd", "63266d38-7f37-4ec5-9639-bcd0537aaf13", "901f6b9c-3f30-4ecc-b887-e4de092f5330", "a08f15ea-57fb-4fd7-9967-8a6d24852c54", "92b94910-2c81-492e-a683-d037b782851d", "ed1fc537-3309-4fce-ba17-c1b6bc440f4d", "38cd32b6-985b-4e49-8484-228ddb854388"], "full_range": {"start_byte": 2611, "end_byte": 2947, "start_point": {"row": 132, "column": 0}, "end_point": {"row": 140, "column": 1}}, "declaration_range": {"start_byte": 2611, "end_byte": 2681, "start_point": {"row": 132, "column": 0}, "end_point": {"row": 132, "column": 70}}, "definition_range": {"start_byte": 2681, "end_byte": 2947, "start_point": {"row": 132, "column": 70}, "end_point": {"row": 140, "column": 1}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}, "template_types": [{"name": "T", "inference_info": null, "is_pod": false, "namespace": "", "guid": null, "nested_types": []}], "args": [{"name": "element", "type_": {"name": "T", "inference_info": null, "is_pod": false, "namespace": "", "guid": null, "nested_types": []}}], "return_type": {"name": "tuple", "inference_info": null, "is_pod": false, "namespace": "", "guid": null, "nested_types": [{"name": "T", "inference_info": null, "is_pod": false, "namespace": "", "guid": null, "nested_types": []}, {"name": "string", "inference_info": null, "is_pod": true, "namespace": "", "guid": null, "nested_types": []}]}}}, {"VariableDefinition": {"ast_fields": {"guid": "56902b6c-9fc1-4f70-82f8-4e259281395a", "name": "id", "language": "TypeScript", "file_path": "file:///main.ts", "namespace": "", "parent_guid": "b9edf75c-e6cc-40f0-8304-15bac6bb6406", "childs_guid": [], "full_range": {"start_byte": 243, "end_byte": 257, "start_point": {"row": 7, "column": 4}, "end_point": {"row": 7, "column": 18}}, "declaration_range": {"start_byte": 243, "end_byte": 257, "start_point": {"row": 7, "column": 4}, "end_point": {"row": 7, "column": 18}}, "definition_range": {"start_byte": 243, "end_byte": 257, "start_point": {"row": 7, "column": 4}, "end_point": {"row": 7, "column": 18}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}, "type_": {"name": "number", "inference_info": "1", "is_pod": true, "namespace": "", "guid": null, "nested_types": []}}}, {"VariableDefinition": {"ast_fields": {"guid": "9a78bc6d-b1a8-4212-a1a2-20172dda583d", "name": "company", "language": "TypeScript", "file_path": "file:///main.ts", "namespace": "", "parent_guid": "b9edf75c-e6cc-40f0-8304-15bac6bb6406", "childs_guid": [], "full_range": {"start_byte": 263, "end_byte": 293, "start_point": {"row": 8, "column": 4}, "end_point": {"row": 8, "column": 34}}, "declaration_range": {"start_byte": 263, "end_byte": 293, "start_point": {"row": 8, "column": 4}, "end_point": {"row": 8, "column": 34}}, "definition_range": {"start_byte": 263, "end_byte": 293, "start_point": {"row": 8, "column": 4}, "end_point": {"row": 8, "column": 34}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}, "type_": {"name": "string", "inference_info": "'My Company'", "is_pod": true, "namespace": "", "guid": null, "nested_types": []}}}, {"VariableDefinition": {"ast_fields": {"guid": "0c4e872a-0d8f-49d3-bdaf-f19ea3612eaa", "name": "isPublished", "language": "TypeScript", "file_path": "file:///main.ts", "namespace": "", "parent_guid": "b9edf75c-e6cc-40f0-8304-15bac6bb6406", "childs_guid": [], "full_range": {"start_byte": 299, "end_byte": 326, "start_point": {"row": 9, "column": 4}, "end_point": {"row": 9, "column": 31}}, "declaration_range": {"start_byte": 299, "end_byte": 326, "start_point": {"row": 9, "column": 4}, "end_point": {"row": 9, "column": 31}}, "definition_range": {"start_byte": 299, "end_byte": 326, "start_point": {"row": 9, "column": 4}, "end_point": {"row": 9, "column": 31}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}, "type_": {"name": "boolean", "inference_info": "true", "is_pod": true, "namespace": "", "guid": null, "nested_types": []}}}, {"VariableDefinition": {"ast_fields": {"guid": "d48edca4-3dac-4ba4-9ffa-b805c9642195", "name": "x", "language": "TypeScript", "file_path": "file:///main.ts", "namespace": "", "parent_guid": "b9edf75c-e6cc-40f0-8304-15bac6bb6406", "childs_guid": [], "full_range": {"start_byte": 332, "end_byte": 348, "start_point": {"row": 10, "column": 4}, "end_point": {"row": 10, "column": 20}}, "declaration_range": {"start_byte": 332, "end_byte": 348, "start_point": {"row": 10, "column": 4}, "end_point": {"row": 10, "column": 20}}, "definition_range": {"start_byte": 332, "end_byte": 348, "start_point": {"row": 10, "column": 4}, "end_point": {"row": 10, "column": 20}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}, "type_": {"name": "any", "inference_info": "\"Hello\"", "is_pod": true, "namespace": "", "guid": null, "nested_types": []}}}, {"VariableDefinition": {"ast_fields": {"guid": "76388b77-3ed1-4db9-ae66-0eb2227541dd", "name": "ids", "language": "TypeScript", "file_path": "file:///main.ts", "namespace": "", "parent_guid": "b9edf75c-e6cc-40f0-8304-15bac6bb6406", "childs_guid": [], "full_range": {"start_byte": 412, "end_byte": 437, "start_point": {"row": 12, "column": 4}, "end_point": {"row": 12, "column": 29}}, "declaration_range": {"start_byte": 412, "end_byte": 437, "start_point": {"row": 12, "column": 4}, "end_point": {"row": 12, "column": 29}}, "definition_range": {"start_byte": 412, "end_byte": 437, "start_point": {"row": 12, "column": 4}, "end_point": {"row": 12, "column": 29}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}, "type_": {"name": "array", "inference_info": "[1, 2, 3]", "is_pod": false, "namespace": "", "guid": null, "nested_types": [{"name": "number", "inference_info": null, "is_pod": true, "namespace": "", "guid": null, "nested_types": []}]}}}, {"VariableDefinition": {"ast_fields": {"guid": "1c63c70b-23a5-4b22-8211-04ec3bb5eabb", "name": "arr", "language": "TypeScript", "file_path": "file:///main.ts", "namespace": "", "parent_guid": "b9edf75c-e6cc-40f0-8304-15bac6bb6406", "childs_guid": [], "full_range": {"start_byte": 443, "end_byte": 474, "start_point": {"row": 13, "column": 4}, "end_point": {"row": 13, "column": 35}}, "declaration_range": {"start_byte": 443, "end_byte": 474, "start_point": {"row": 13, "column": 4}, "end_point": {"row": 13, "column": 35}}, "definition_range": {"start_byte": 443, "end_byte": 474, "start_point": {"row": 13, "column": 4}, "end_point": {"row": 13, "column": 35}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}, "type_": {"name": "array", "inference_info": "[1, true, 'Hello']", "is_pod": false, "namespace": "", "guid": null, "nested_types": [{"name": "any", "inference_info": null, "is_pod": true, "namespace": "", "guid": null, "nested_types": []}]}}}, {"VariableDefinition": {"ast_fields": {"guid": "9115bf0b-957b-4214-9f0d-8666160880d6", "name": "PI", "language": "TypeScript", "file_path": "file:///main.ts", "namespace": "", "parent_guid": "b9edf75c-e6cc-40f0-8304-15bac6bb6406", "childs_guid": [], "full_range": {"start_byte": 482, "end_byte": 499, "start_point": {"row": 14, "column": 6}, "end_point": {"row": 14, "column": 23}}, "declaration_range": {"start_byte": 482, "end_byte": 499, "start_point": {"row": 14, "column": 6}, "end_point": {"row": 14, "column": 23}}, "definition_range": {"start_byte": 482, "end_byte": 499, "start_point": {"row": 14, "column": 6}, "end_point": {"row": 14, "column": 23}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}, "type_": {"name": "number", "inference_info": "3.14", "is_pod": true, "namespace": "", "guid": null, "nested_types": []}}}, {"VariableDefinition": {"ast_fields": {"guid": "2c99fe27-0cfb-4ece-8fed-beeb5630a5b5", "name": "asd", "language": "TypeScript", "file_path": "file:///main.ts", "namespace": "", "parent_guid": "b9edf75c-e6cc-40f0-8304-15bac6bb6406", "childs_guid": [], "full_range": {"start_byte": 505, "end_byte": 523, "start_point": {"row": 15, "column": 4}, "end_point": {"row": 15, "column": 22}}, "declaration_range": {"start_byte": 505, "end_byte": 523, "start_point": {"row": 15, "column": 4}, "end_point": {"row": 15, "column": 22}}, "definition_range": {"start_byte": 505, "end_byte": 523, "start_point": {"row": 15, "column": 4}, "end_point": {"row": 15, "column": 22}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}, "type_": {"name": "wqe", "inference_info": "12", "is_pod": false, "namespace": "", "guid": null, "nested_types": [{"name": "dfg", "inference_info": null, "is_pod": false, "namespace": "", "guid": null, "nested_types": []}]}}}, {"VariableDefinition": {"ast_fields": {"guid": "4c228098-669f-4dca-baef-2a4fdf1481eb", "name": "person", "language": "TypeScript", "file_path": "file:///main.ts", "namespace": "", "parent_guid": "b9edf75c-e6cc-40f0-8304-15bac6bb6406", "childs_guid": [], "full_range": {"start_byte": 645, "end_byte": 698, "start_point": {"row": 24, "column": 4}, "end_point": {"row": 24, "column": 57}}, "declaration_range": {"start_byte": 645, "end_byte": 698, "start_point": {"row": 24, "column": 4}, "end_point": {"row": 24, "column": 57}}, "definition_range": {"start_byte": 645, "end_byte": 698, "start_point": {"row": 24, "column": 4}, "end_point": {"row": 24, "column": 57}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}, "type_": {"name": "tuple", "inference_info": "[1, '<PERSON>', true]", "is_pod": false, "namespace": "", "guid": null, "nested_types": [{"name": "number", "inference_info": null, "is_pod": true, "namespace": "", "guid": null, "nested_types": []}, {"name": "string", "inference_info": null, "is_pod": true, "namespace": "", "guid": null, "nested_types": []}, {"name": "boolean", "inference_info": null, "is_pod": true, "namespace": "", "guid": null, "nested_types": []}]}}}, {"VariableDefinition": {"ast_fields": {"guid": "2309d774-2c38-4389-824b-3edd2620d79b", "name": "employee", "language": "TypeScript", "file_path": "file:///main.ts", "namespace": "", "parent_guid": "b9edf75c-e6cc-40f0-8304-15bac6bb6406", "childs_guid": [], "full_range": {"start_byte": 720, "end_byte": 804, "start_point": {"row": 27, "column": 4}, "end_point": {"row": 31, "column": 1}}, "declaration_range": {"start_byte": 720, "end_byte": 804, "start_point": {"row": 27, "column": 4}, "end_point": {"row": 31, "column": 1}}, "definition_range": {"start_byte": 720, "end_byte": 804, "start_point": {"row": 27, "column": 4}, "end_point": {"row": 31, "column": 1}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}, "type_": {"name": "array", "inference_info": "[\n    [1, '<PERSON>'],\n    [2, '<PERSON>'],\n    [3, '<PERSON>'],\n]", "is_pod": false, "namespace": "", "guid": null, "nested_types": [{"name": "tuple", "inference_info": null, "is_pod": false, "namespace": "", "guid": null, "nested_types": [{"name": "number", "inference_info": null, "is_pod": true, "namespace": "", "guid": null, "nested_types": []}, {"name": "string", "inference_info": null, "is_pod": true, "namespace": "", "guid": null, "nested_types": []}]}]}}}, {"VariableDefinition": {"ast_fields": {"guid": "24e0ace6-910c-4aff-b0d5-49ffe28dee1b", "name": "pid", "language": "TypeScript", "file_path": "file:///main.ts", "namespace": "", "parent_guid": "b9edf75c-e6cc-40f0-8304-15bac6bb6406", "childs_guid": [], "full_range": {"start_byte": 820, "end_byte": 845, "start_point": {"row": 34, "column": 4}, "end_point": {"row": 34, "column": 29}}, "declaration_range": {"start_byte": 820, "end_byte": 845, "start_point": {"row": 34, "column": 4}, "end_point": {"row": 34, "column": 29}}, "definition_range": {"start_byte": 820, "end_byte": 845, "start_point": {"row": 34, "column": 4}, "end_point": {"row": 34, "column": 29}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}, "type_": {"name": "union", "inference_info": "22", "is_pod": false, "namespace": "", "guid": null, "nested_types": [{"name": "string", "inference_info": null, "is_pod": true, "namespace": "", "guid": null, "nested_types": []}, {"name": "number", "inference_info": null, "is_pod": true, "namespace": "", "guid": null, "nested_types": []}]}}}, {"VariableDefinition": {"ast_fields": {"guid": "802c49ea-5eac-4856-9eb1-0cd8cd7dcae5", "name": "user", "language": "TypeScript", "file_path": "file:///main.ts", "namespace": "", "parent_guid": "b9edf75c-e6cc-40f0-8304-15bac6bb6406", "childs_guid": [], "full_range": {"start_byte": 984, "end_byte": 1029, "start_point": {"row": 49, "column": 6}, "end_point": {"row": 52, "column": 1}}, "declaration_range": {"start_byte": 984, "end_byte": 1029, "start_point": {"row": 49, "column": 6}, "end_point": {"row": 52, "column": 1}}, "definition_range": {"start_byte": 984, "end_byte": 1029, "start_point": {"row": 49, "column": 6}, "end_point": {"row": 52, "column": 1}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}, "type_": {"name": "User", "inference_info": "{\n    id: 1,\n    name: '<PERSON>',\n}", "is_pod": false, "namespace": "", "guid": null, "nested_types": []}}}, {"VariableDefinition": {"ast_fields": {"guid": "6bfb36e3-84ce-490d-9568-a60f9e172ded", "name": "cid", "language": "TypeScript", "file_path": "file:///main.ts", "namespace": "", "parent_guid": "b9edf75c-e6cc-40f0-8304-15bac6bb6406", "childs_guid": [], "full_range": {"start_byte": 1054, "end_byte": 1066, "start_point": {"row": 55, "column": 4}, "end_point": {"row": 55, "column": 16}}, "declaration_range": {"start_byte": 1054, "end_byte": 1066, "start_point": {"row": 55, "column": 4}, "end_point": {"row": 55, "column": 16}}, "definition_range": {"start_byte": 1054, "end_byte": 1066, "start_point": {"row": 55, "column": 4}, "end_point": {"row": 55, "column": 16}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}, "type_": {"name": "any", "inference_info": "1", "is_pod": true, "namespace": "", "guid": null, "nested_types": []}}}, {"VariableDefinition": {"ast_fields": {"guid": "06f6aea7-5d20-47d8-8060-921c59242502", "name": "customerId", "language": "TypeScript", "file_path": "file:///main.ts", "namespace": "", "parent_guid": "b9edf75c-e6cc-40f0-8304-15bac6bb6406", "childs_guid": [], "full_range": {"start_byte": 1072, "end_byte": 1096, "start_point": {"row": 56, "column": 4}, "end_point": {"row": 56, "column": 28}}, "declaration_range": {"start_byte": 1072, "end_byte": 1096, "start_point": {"row": 56, "column": 4}, "end_point": {"row": 56, "column": 28}}, "definition_range": {"start_byte": 1072, "end_byte": 1096, "start_point": {"row": 56, "column": 4}, "end_point": {"row": 56, "column": 28}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}, "type_": {"name": null, "inference_info": "<number>cid", "is_pod": false, "namespace": "", "guid": null, "nested_types": []}}}, {"VariableDefinition": {"ast_fields": {"guid": "1d018464-b4f0-402b-acce-6a6b10067e36", "name": "user1", "language": "TypeScript", "file_path": "file:///main.ts", "namespace": "", "parent_guid": "b9edf75c-e6cc-40f0-8304-15bac6bb6406", "childs_guid": [], "full_range": {"start_byte": 1546, "end_byte": 1601, "start_point": {"row": 81, "column": 6}, "end_point": {"row": 84, "column": 1}}, "declaration_range": {"start_byte": 1546, "end_byte": 1601, "start_point": {"row": 81, "column": 6}, "end_point": {"row": 84, "column": 1}}, "definition_range": {"start_byte": 1546, "end_byte": 1601, "start_point": {"row": 81, "column": 6}, "end_point": {"row": 84, "column": 1}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}, "type_": {"name": "UserInterface", "inference_info": "{\n    id: 1,\n    name: '<PERSON>',\n}", "is_pod": false, "namespace": "", "guid": null, "nested_types": []}}}, {"VariableDefinition": {"ast_fields": {"guid": "88908e28-4a34-4eb4-8ebd-725f846c3bdd", "name": "john", "language": "TypeScript", "file_path": "file:///main.ts", "namespace": "", "parent_guid": "b9edf75c-e6cc-40f0-8304-15bac6bb6406", "childs_guid": [], "full_range": {"start_byte": 1772, "end_byte": 1800, "start_point": {"row": 97, "column": 6}, "end_point": {"row": 97, "column": 34}}, "declaration_range": {"start_byte": 1772, "end_byte": 1800, "start_point": {"row": 97, "column": 6}, "end_point": {"row": 97, "column": 34}}, "definition_range": {"start_byte": 1772, "end_byte": 1800, "start_point": {"row": 97, "column": 6}, "end_point": {"row": 97, "column": 34}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}, "type_": {"name": null, "inference_info": "new Person(1, '<PERSON>')", "is_pod": false, "namespace": "", "guid": null, "nested_types": []}}}, {"VariableDefinition": {"ast_fields": {"guid": "1f07eaf1-f573-4e95-8878-5c4d655f345d", "name": "myGenericNumber", "language": "TypeScript", "file_path": "file:///main.ts", "namespace": "", "parent_guid": "b9edf75c-e6cc-40f0-8304-15bac6bb6406", "childs_guid": [], "full_range": {"start_byte": 1881, "end_byte": 1926, "start_point": {"row": 104, "column": 4}, "end_point": {"row": 104, "column": 49}}, "declaration_range": {"start_byte": 1881, "end_byte": 1926, "start_point": {"row": 104, "column": 4}, "end_point": {"row": 104, "column": 49}}, "definition_range": {"start_byte": 1881, "end_byte": 1926, "start_point": {"row": 104, "column": 4}, "end_point": {"row": 104, "column": 49}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}, "type_": {"name": null, "inference_info": "new GenericNumber<number>()", "is_pod": false, "namespace": "", "guid": null, "nested_types": []}}}, {"FunctionCall": {"ast_fields": {"guid": "99ace3ce-107f-4d71-8bad-ab46749f06d7", "name": "log", "language": "TypeScript", "file_path": "file:///main.ts", "namespace": "", "parent_guid": "b9edf75c-e6cc-40f0-8304-15bac6bb6406", "childs_guid": [], "full_range": {"start_byte": 2016, "end_byte": 2054, "start_point": {"row": 108, "column": 0}, "end_point": {"row": 108, "column": 38}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": "1694b509-b0ec-450c-a9a1-4664345a71e2", "is_error": false}, "template_types": []}}, {"VariableDefinition": {"ast_fields": {"guid": "3d58d39d-51d9-4054-8f06-59a2f5dbd3dd", "name": "stringNumeric", "language": "TypeScript", "file_path": "file:///main.ts", "namespace": "", "parent_guid": "b9edf75c-e6cc-40f0-8304-15bac6bb6406", "childs_guid": [], "full_range": {"start_byte": 2075, "end_byte": 2110, "start_point": {"row": 110, "column": 4}, "end_point": {"row": 110, "column": 39}}, "declaration_range": {"start_byte": 2075, "end_byte": 2110, "start_point": {"row": 110, "column": 4}, "end_point": {"row": 110, "column": 39}}, "definition_range": {"start_byte": 2075, "end_byte": 2110, "start_point": {"row": 110, "column": 4}, "end_point": {"row": 110, "column": 39}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}, "type_": {"name": null, "inference_info": "new GenericNumber()", "is_pod": false, "namespace": "", "guid": null, "nested_types": []}}}, {"FunctionCall": {"ast_fields": {"guid": "b953deb9-5988-44fb-a23c-dacc7c839b6e", "name": "log", "language": "TypeScript", "file_path": "file:///main.ts", "namespace": "", "parent_guid": "b9edf75c-e6cc-40f0-8304-15bac6bb6406", "childs_guid": [], "full_range": {"start_byte": 2197, "end_byte": 2260, "start_point": {"row": 114, "column": 0}, "end_point": {"row": 114, "column": 63}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": "ba818cda-7015-4c74-9a28-3f5a98fb08dc", "is_error": false}, "template_types": []}}, {"VariableDefinition": {"ast_fields": {"guid": "2cb411ae-6d12-4d83-9b62-a24eca2aaa7c", "name": "numArray", "language": "TypeScript", "file_path": "file:///main.ts", "namespace": "", "parent_guid": "b9edf75c-e6cc-40f0-8304-15bac6bb6406", "childs_guid": [], "full_range": {"start_byte": 2383, "end_byte": 2424, "start_point": {"row": 121, "column": 4}, "end_point": {"row": 121, "column": 45}}, "declaration_range": {"start_byte": 2383, "end_byte": 2424, "start_point": {"row": 121, "column": 4}, "end_point": {"row": 121, "column": 45}}, "definition_range": {"start_byte": 2383, "end_byte": 2424, "start_point": {"row": 121, "column": 4}, "end_point": {"row": 121, "column": 45}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}, "type_": {"name": null, "inference_info": "getArray<number>([1, 2, 3, 4])", "is_pod": false, "namespace": "", "guid": null, "nested_types": []}}}, {"VariableDefinition": {"ast_fields": {"guid": "ebb66172-dd9b-4549-aa31-d1c0d325b232", "name": "strArray", "language": "TypeScript", "file_path": "file:///main.ts", "namespace": "", "parent_guid": "b9edf75c-e6cc-40f0-8304-15bac6bb6406", "childs_guid": [], "full_range": {"start_byte": 2430, "end_byte": 2482, "start_point": {"row": 122, "column": 4}, "end_point": {"row": 122, "column": 56}}, "declaration_range": {"start_byte": 2430, "end_byte": 2482, "start_point": {"row": 122, "column": 4}, "end_point": {"row": 122, "column": 56}}, "definition_range": {"start_byte": 2430, "end_byte": 2482, "start_point": {"row": 122, "column": 4}, "end_point": {"row": 122, "column": 56}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}, "type_": {"name": null, "inference_info": "getArray<string>(['<PERSON>', '<PERSON>', '<PERSON>'])", "is_pod": false, "namespace": "", "guid": null, "nested_types": []}}}, {"FunctionCall": {"ast_fields": {"guid": "3f370eca-6685-444f-ba5e-3cbb986eb7ae", "name": "log", "language": "TypeScript", "file_path": "file:///main.ts", "namespace": "", "parent_guid": "b9edf75c-e6cc-40f0-8304-15bac6bb6406", "childs_guid": [], "full_range": {"start_byte": 2485, "end_byte": 2506, "start_point": {"row": 124, "column": 0}, "end_point": {"row": 124, "column": 21}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": "29bf452d-97fd-4571-b8b6-e283e43374c7", "is_error": false}, "template_types": []}}, {"FunctionCall": {"ast_fields": {"guid": "5de56f10-7bab-450c-bc45-c9c979904536", "name": "log", "language": "TypeScript", "file_path": "file:///main.ts", "namespace": "", "parent_guid": "b9edf75c-e6cc-40f0-8304-15bac6bb6406", "childs_guid": [], "full_range": {"start_byte": 2508, "end_byte": 2537, "start_point": {"row": 125, "column": 0}, "end_point": {"row": 125, "column": 29}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": "165baa76-67c5-46b8-857a-3cf5302b0d55", "is_error": false}, "template_types": []}}, {"FunctionCall": {"ast_fields": {"guid": "533e1746-9e63-4eee-a4a4-2c56eb453668", "name": "log", "language": "TypeScript", "file_path": "file:///main.ts", "namespace": "", "parent_guid": "b9edf75c-e6cc-40f0-8304-15bac6bb6406", "childs_guid": [], "full_range": {"start_byte": 2949, "end_byte": 2993, "start_point": {"row": 142, "column": 0}, "end_point": {"row": 142, "column": 44}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": "7f3c4804-e209-4010-bcbd-3708b68079f0", "is_error": false}, "template_types": []}}, {"VariableDefinition": {"ast_fields": {"guid": "28b2206b-3275-4cd5-a7a3-69498ce18294", "name": "asd", "language": "TypeScript", "file_path": "file:///main.ts", "namespace": "", "parent_guid": "b9edf75c-e6cc-40f0-8304-15bac6bb6406", "childs_guid": [], "full_range": {"start_byte": 3047, "end_byte": 3056, "start_point": {"row": 145, "column": 4}, "end_point": {"row": 145, "column": 13}}, "declaration_range": {"start_byte": 3047, "end_byte": 3056, "start_point": {"row": 145, "column": 4}, "end_point": {"row": 145, "column": 13}}, "definition_range": {"start_byte": 3047, "end_byte": 3056, "start_point": {"row": 145, "column": 4}, "end_point": {"row": 145, "column": 13}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}, "type_": {"name": null, "inference_info": "122", "is_pod": false, "namespace": "", "guid": null, "nested_types": []}}}, {"VariableDefinition": {"ast_fields": {"guid": "13dbf4a2-7f42-4eb2-8067-5b6e9c44f5b8", "name": "runnable", "language": "TypeScript", "file_path": "file:///main.ts", "namespace": "", "parent_guid": "b9edf75c-e6cc-40f0-8304-15bac6bb6406", "childs_guid": [], "full_range": {"start_byte": 3066, "end_byte": 3153, "start_point": {"row": 148, "column": 6}, "end_point": {"row": 152, "column": 3}}, "declaration_range": {"start_byte": 3066, "end_byte": 3153, "start_point": {"row": 148, "column": 6}, "end_point": {"row": 152, "column": 3}}, "definition_range": {"start_byte": 3066, "end_byte": 3153, "start_point": {"row": 148, "column": 6}, "end_point": {"row": 152, "column": 3}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}, "type_": {"name": null, "inference_info": "new class extends Runnable {\n    run() {\n        // implement here\n    }\n}()", "is_pod": false, "namespace": "", "guid": null, "nested_types": []}}}, {"ClassFieldDeclaration": {"ast_fields": {"guid": "fd08fe81-7f6f-40db-98d9-87f69b03dcec", "name": "id", "language": "TypeScript", "file_path": "file:///main.ts", "namespace": "", "parent_guid": "5a9e441a-4499-436c-a34f-b943a903ca5d", "childs_guid": [], "full_range": {"start_byte": 944, "end_byte": 954, "start_point": {"row": 45, "column": 4}, "end_point": {"row": 45, "column": 14}}, "declaration_range": {"start_byte": 944, "end_byte": 954, "start_point": {"row": 45, "column": 4}, "end_point": {"row": 45, "column": 14}}, "definition_range": {"start_byte": 944, "end_byte": 954, "start_point": {"row": 45, "column": 4}, "end_point": {"row": 45, "column": 14}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}, "type_": {"name": "number", "inference_info": null, "is_pod": true, "namespace": "", "guid": null, "nested_types": []}}}, {"ClassFieldDeclaration": {"ast_fields": {"guid": "c660a91e-6b3c-4950-a686-ee817e0e5e57", "name": "name", "language": "TypeScript", "file_path": "file:///main.ts", "namespace": "", "parent_guid": "5a9e441a-4499-436c-a34f-b943a903ca5d", "childs_guid": [], "full_range": {"start_byte": 960, "end_byte": 972, "start_point": {"row": 46, "column": 4}, "end_point": {"row": 46, "column": 16}}, "declaration_range": {"start_byte": 960, "end_byte": 972, "start_point": {"row": 46, "column": 4}, "end_point": {"row": 46, "column": 16}}, "definition_range": {"start_byte": 960, "end_byte": 972, "start_point": {"row": 46, "column": 4}, "end_point": {"row": 46, "column": 16}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}, "type_": {"name": "string", "inference_info": null, "is_pod": true, "namespace": "", "guid": null, "nested_types": []}}}, {"FunctionDeclaration": {"ast_fields": {"guid": "383258f6-0ed6-4018-9956-da765329483c", "name": "constructor", "language": "TypeScript", "file_path": "file:///main.ts", "namespace": "", "parent_guid": "01208911-dd2a-4735-927b-f50a74405b05", "childs_guid": [], "full_range": {"start_byte": 1214, "end_byte": 1264, "start_point": {"row": 65, "column": 4}, "end_point": {"row": 65, "column": 54}}, "declaration_range": {"start_byte": 1214, "end_byte": 1262, "start_point": {"row": 65, "column": 4}, "end_point": {"row": 65, "column": 52}}, "definition_range": {"start_byte": 1262, "end_byte": 1264, "start_point": {"row": 65, "column": 52}, "end_point": {"row": 65, "column": 54}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}, "template_types": [], "args": [{"name": "x", "type_": {"name": "number", "inference_info": null, "is_pod": true, "namespace": "", "guid": null, "nested_types": []}}, {"name": "y", "type_": {"name": "number", "inference_info": null, "is_pod": true, "namespace": "", "guid": null, "nested_types": []}}], "return_type": null}}, {"FunctionDeclaration": {"ast_fields": {"guid": "6b94e540-6bf1-4aa7-bb75-e495d61810aa", "name": "euclideanDistance", "language": "TypeScript", "file_path": "file:///main.ts", "namespace": "", "parent_guid": "01208911-dd2a-4735-927b-f50a74405b05", "childs_guid": ["b4474e35-e769-46af-98c1-644d830fe81f", "44b47115-c6a0-4fec-99db-95979cbefd25", "afcf590f-8fea-4c5b-9ad6-461d4427febb", "9961390c-a184-4f88-bd38-c0021248b7d6", "071d8469-78b2-415e-95d7-dc11e0eee25a", "1cd2cdd4-c968-4c3f-9e52-ef470dac3d98", "776ccdb7-ff5c-4579-b08a-01b24b62d99b", "3af9cc54-be3d-4e8c-a362-0344f539da5e", "27b34780-f62f-46fd-bb32-eb79978d02dd", "2f8e3d70-30fd-40d2-848a-a8d3cc47a19f", "7a651f48-c3f7-40c1-b146-ff93c276c0d9", "21daccbe-54a6-4b79-ab1a-fe061f365359", "71a50340-60a3-4860-8203-d2bb74784caf", "55bc04c6-9fb8-4809-9551-f441d4797f47"], "full_range": {"start_byte": 1270, "end_byte": 1432, "start_point": {"row": 67, "column": 4}, "end_point": {"row": 71, "column": 5}}, "declaration_range": {"start_byte": 1270, "end_byte": 1310, "start_point": {"row": 67, "column": 4}, "end_point": {"row": 67, "column": 44}}, "definition_range": {"start_byte": 1310, "end_byte": 1432, "start_point": {"row": 67, "column": 44}, "end_point": {"row": 71, "column": 5}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}, "template_types": [], "args": [{"name": "other", "type_": {"name": "Point", "inference_info": null, "is_pod": false, "namespace": "", "guid": null, "nested_types": []}}], "return_type": {"name": "number", "inference_info": null, "is_pod": true, "namespace": "", "guid": null, "nested_types": []}}}, {"ClassFieldDeclaration": {"ast_fields": {"guid": "81f2c567-09e5-4479-88bf-6e541e8e8e4c", "name": "id", "language": "TypeScript", "file_path": "file:///main.ts", "namespace": "", "parent_guid": "ce7738ea-df82-45bd-b802-9ba645f1331f", "childs_guid": [], "full_range": {"start_byte": 1480, "end_byte": 1499, "start_point": {"row": 76, "column": 4}, "end_point": {"row": 76, "column": 23}}, "declaration_range": {"start_byte": 1480, "end_byte": 1499, "start_point": {"row": 76, "column": 4}, "end_point": {"row": 76, "column": 23}}, "definition_range": {"start_byte": 1480, "end_byte": 1499, "start_point": {"row": 76, "column": 4}, "end_point": {"row": 76, "column": 23}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}, "type_": {"name": "number", "inference_info": null, "is_pod": true, "namespace": "", "guid": null, "nested_types": []}}}, {"ClassFieldDeclaration": {"ast_fields": {"guid": "095dd73f-d6cb-4ce2-a6b7-c4b9d24af292", "name": "name", "language": "TypeScript", "file_path": "file:///main.ts", "namespace": "", "parent_guid": "ce7738ea-df82-45bd-b802-9ba645f1331f", "childs_guid": [], "full_range": {"start_byte": 1505, "end_byte": 1517, "start_point": {"row": 77, "column": 4}, "end_point": {"row": 77, "column": 16}}, "declaration_range": {"start_byte": 1505, "end_byte": 1517, "start_point": {"row": 77, "column": 4}, "end_point": {"row": 77, "column": 16}}, "definition_range": {"start_byte": 1505, "end_byte": 1517, "start_point": {"row": 77, "column": 4}, "end_point": {"row": 77, "column": 16}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}, "type_": {"name": "string", "inference_info": null, "is_pod": true, "namespace": "", "guid": null, "nested_types": []}}}, {"ClassFieldDeclaration": {"ast_fields": {"guid": "80160bb8-b6b6-4303-80e0-1041a16fc90c", "name": "age", "language": "TypeScript", "file_path": "file:///main.ts", "namespace": "", "parent_guid": "ce7738ea-df82-45bd-b802-9ba645f1331f", "childs_guid": [], "full_range": {"start_byte": 1523, "end_byte": 1535, "start_point": {"row": 78, "column": 4}, "end_point": {"row": 78, "column": 16}}, "declaration_range": {"start_byte": 1523, "end_byte": 1535, "start_point": {"row": 78, "column": 4}, "end_point": {"row": 78, "column": 16}}, "definition_range": {"start_byte": 1523, "end_byte": 1535, "start_point": {"row": 78, "column": 4}, "end_point": {"row": 78, "column": 16}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}, "type_": {"name": "number", "inference_info": null, "is_pod": true, "namespace": "", "guid": null, "nested_types": []}}}, {"ClassFieldDeclaration": {"ast_fields": {"guid": "f220583b-6059-4222-a210-77e75830947b", "name": "id", "language": "TypeScript", "file_path": "file:///main.ts", "namespace": "", "parent_guid": "1e343c61-cc36-42fd-9000-a7f2bf22684c", "childs_guid": [], "full_range": {"start_byte": 1634, "end_byte": 1644, "start_point": {"row": 88, "column": 4}, "end_point": {"row": 88, "column": 14}}, "declaration_range": {"start_byte": 1634, "end_byte": 1644, "start_point": {"row": 88, "column": 4}, "end_point": {"row": 88, "column": 14}}, "definition_range": {"start_byte": 1634, "end_byte": 1644, "start_point": {"row": 88, "column": 4}, "end_point": {"row": 88, "column": 14}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}, "type_": {"name": "number", "inference_info": null, "is_pod": true, "namespace": "", "guid": null, "nested_types": []}}}, {"ClassFieldDeclaration": {"ast_fields": {"guid": "f0533f88-6c34-4107-b119-efa31781ae82", "name": "name", "language": "TypeScript", "file_path": "file:///main.ts", "namespace": "", "parent_guid": "1e343c61-cc36-42fd-9000-a7f2bf22684c", "childs_guid": [], "full_range": {"start_byte": 1650, "end_byte": 1662, "start_point": {"row": 89, "column": 4}, "end_point": {"row": 89, "column": 16}}, "declaration_range": {"start_byte": 1650, "end_byte": 1662, "start_point": {"row": 89, "column": 4}, "end_point": {"row": 89, "column": 16}}, "definition_range": {"start_byte": 1650, "end_byte": 1662, "start_point": {"row": 89, "column": 4}, "end_point": {"row": 89, "column": 16}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}, "type_": {"name": "string", "inference_info": null, "is_pod": true, "namespace": "", "guid": null, "nested_types": []}}}, {"FunctionDeclaration": {"ast_fields": {"guid": "ff3cb01f-9bf7-42a0-a4e1-e3b472f85a4a", "name": "constructor", "language": "TypeScript", "file_path": "file:///main.ts", "namespace": "", "parent_guid": "1e343c61-cc36-42fd-9000-a7f2bf22684c", "childs_guid": ["5a706f7d-0f4a-463b-b885-b13ebddd073d", "741ae1df-2d45-441e-be1e-8af2064e78df", "6022cd69-3f81-4ca5-8801-22e7ef7577dd", "5a212f92-db09-4151-896f-3ce00fa67f3b"], "full_range": {"start_byte": 1669, "end_byte": 1762, "start_point": {"row": 91, "column": 4}, "end_point": {"row": 94, "column": 5}}, "declaration_range": {"start_byte": 1669, "end_byte": 1707, "start_point": {"row": 91, "column": 4}, "end_point": {"row": 91, "column": 42}}, "definition_range": {"start_byte": 1707, "end_byte": 1762, "start_point": {"row": 91, "column": 42}, "end_point": {"row": 94, "column": 5}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}, "template_types": [], "args": [{"name": "id", "type_": {"name": "number", "inference_info": null, "is_pod": true, "namespace": "", "guid": null, "nested_types": []}}, {"name": "name", "type_": {"name": "string", "inference_info": null, "is_pod": true, "namespace": "", "guid": null, "nested_types": []}}], "return_type": null}}, {"ClassFieldDeclaration": {"ast_fields": {"guid": "845c1b30-1854-4acf-b89f-5ccfb854852b", "name": "zeroValue", "language": "TypeScript", "file_path": "file:///main.ts", "namespace": "", "parent_guid": "3ae48c44-88ec-482a-a956-93f293f47cbf", "childs_guid": [], "full_range": {"start_byte": 1832, "end_byte": 1844, "start_point": {"row": 100, "column": 4}, "end_point": {"row": 100, "column": 16}}, "declaration_range": {"start_byte": 1832, "end_byte": 1844, "start_point": {"row": 100, "column": 4}, "end_point": {"row": 100, "column": 16}}, "definition_range": {"start_byte": 1832, "end_byte": 1844, "start_point": {"row": 100, "column": 4}, "end_point": {"row": 100, "column": 16}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}, "type_": {"name": "T", "inference_info": null, "is_pod": false, "namespace": "", "guid": null, "nested_types": []}}}, {"ClassFieldDeclaration": {"ast_fields": {"guid": "fbda431a-6f3a-41f5-8aa4-77bf7483dc3a", "name": "add", "language": "TypeScript", "file_path": "file:///main.ts", "namespace": "", "parent_guid": "3ae48c44-88ec-482a-a956-93f293f47cbf", "childs_guid": [], "full_range": {"start_byte": 1850, "end_byte": 1872, "start_point": {"row": 101, "column": 4}, "end_point": {"row": 101, "column": 26}}, "declaration_range": {"start_byte": 1850, "end_byte": 1872, "start_point": {"row": 101, "column": 4}, "end_point": {"row": 101, "column": 26}}, "definition_range": {"start_byte": 1850, "end_byte": 1872, "start_point": {"row": 101, "column": 4}, "end_point": {"row": 101, "column": 26}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}, "type_": {"name": "function", "inference_info": null, "is_pod": false, "namespace": "", "guid": null, "nested_types": [{"name": "T", "inference_info": null, "is_pod": false, "namespace": "", "guid": null, "nested_types": []}, {"name": "T", "inference_info": null, "is_pod": false, "namespace": "", "guid": null, "nested_types": []}, {"name": "T", "inference_info": null, "is_pod": false, "namespace": "", "guid": null, "nested_types": []}]}}}, {"VariableUsage": {"ast_fields": {"guid": "716861ca-a39d-4db9-8d89-aebbd3675635", "name": "zeroValue", "language": "TypeScript", "file_path": "file:///main.ts", "namespace": "", "parent_guid": "b9edf75c-e6cc-40f0-8304-15bac6bb6406", "childs_guid": [], "full_range": {"start_byte": 1928, "end_byte": 1953, "start_point": {"row": 105, "column": 0}, "end_point": {"row": 105, "column": 25}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": "2f6eaeaf-d2b8-4387-afa7-88400b812680", "is_error": false}}}, {"VariableUsage": {"ast_fields": {"guid": "ae97adc0-5c60-4483-9f9b-705a1ee0ef06", "name": "add", "language": "TypeScript", "file_path": "file:///main.ts", "namespace": "", "parent_guid": "b9edf75c-e6cc-40f0-8304-15bac6bb6406", "childs_guid": [], "full_range": {"start_byte": 1959, "end_byte": 1978, "start_point": {"row": 106, "column": 0}, "end_point": {"row": 106, "column": 19}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": "8b1841af-d3bb-4e3c-b581-cb1977213a46", "is_error": false}}}, {"FunctionDeclaration": {"ast_fields": {"guid": "8f1620fb-41d5-4f0c-9962-a84e136e1e58", "name": "", "language": "TypeScript", "file_path": "file:///main.ts", "namespace": "", "parent_guid": "b9edf75c-e6cc-40f0-8304-15bac6bb6406", "childs_guid": ["68e94931-a2b1-433a-887d-01fb0a3d3c2a", "5826214b-2b33-4844-9208-************"], "full_range": {"start_byte": 1981, "end_byte": 2013, "start_point": {"row": 106, "column": 22}, "end_point": {"row": 106, "column": 54}}, "declaration_range": {"start_byte": 1981, "end_byte": 1996, "start_point": {"row": 106, "column": 22}, "end_point": {"row": 106, "column": 37}}, "definition_range": {"start_byte": 1996, "end_byte": 2013, "start_point": {"row": 106, "column": 37}, "end_point": {"row": 106, "column": 54}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}, "template_types": [], "args": [{"name": "x", "type_": null}, {"name": "y", "type_": null}], "return_type": null}}, {"VariableUsage": {"ast_fields": {"guid": "1694b509-b0ec-450c-a9a1-4664345a71e2", "name": "console", "language": "TypeScript", "file_path": "file:///main.ts", "namespace": "", "parent_guid": "b9edf75c-e6cc-40f0-8304-15bac6bb6406", "childs_guid": [], "full_range": {"start_byte": 2016, "end_byte": 2023, "start_point": {"row": 108, "column": 0}, "end_point": {"row": 108, "column": 7}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}}}, {"FunctionCall": {"ast_fields": {"guid": "f82dd9b6-75a6-4c8a-a4e2-9449ff18fb29", "name": "add", "language": "TypeScript", "file_path": "file:///main.ts", "namespace": "", "parent_guid": "b9edf75c-e6cc-40f0-8304-15bac6bb6406", "childs_guid": [], "full_range": {"start_byte": 2028, "end_byte": 2053, "start_point": {"row": 108, "column": 12}, "end_point": {"row": 108, "column": 37}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": "5ef7a148-eac2-49c1-ab27-4c9bbd784209", "is_error": false}, "template_types": []}}, {"VariableUsage": {"ast_fields": {"guid": "b4f852eb-887b-44af-80fa-747653ed53eb", "name": "zeroValue", "language": "TypeScript", "file_path": "file:///main.ts", "namespace": "", "parent_guid": "b9edf75c-e6cc-40f0-8304-15bac6bb6406", "childs_guid": [], "full_range": {"start_byte": 2112, "end_byte": 2135, "start_point": {"row": 111, "column": 0}, "end_point": {"row": 111, "column": 23}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": "72d91751-f2fe-47cd-838e-646b26dd2718", "is_error": false}}}, {"VariableUsage": {"ast_fields": {"guid": "9fc33568-d64f-42c2-a708-492c03e19fee", "name": "add", "language": "TypeScript", "file_path": "file:///main.ts", "namespace": "", "parent_guid": "b9edf75c-e6cc-40f0-8304-15bac6bb6406", "childs_guid": [], "full_range": {"start_byte": 2142, "end_byte": 2159, "start_point": {"row": 112, "column": 0}, "end_point": {"row": 112, "column": 17}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": "b1821076-51ec-407a-a3e3-354493cb39ca", "is_error": false}}}, {"FunctionDeclaration": {"ast_fields": {"guid": "620692fb-f6b5-4da1-be22-177c60ead098", "name": "", "language": "TypeScript", "file_path": "file:///main.ts", "namespace": "", "parent_guid": "b9edf75c-e6cc-40f0-8304-15bac6bb6406", "childs_guid": ["4077f930-f17e-448a-8aeb-f6980b44866f", "5a379715-4aca-45bd-83c1-96473bfe76da"], "full_range": {"start_byte": 2162, "end_byte": 2194, "start_point": {"row": 112, "column": 20}, "end_point": {"row": 112, "column": 52}}, "declaration_range": {"start_byte": 2162, "end_byte": 2177, "start_point": {"row": 112, "column": 20}, "end_point": {"row": 112, "column": 35}}, "definition_range": {"start_byte": 2177, "end_byte": 2194, "start_point": {"row": 112, "column": 35}, "end_point": {"row": 112, "column": 52}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}, "template_types": [], "args": [{"name": "x", "type_": null}, {"name": "y", "type_": null}], "return_type": null}}, {"VariableUsage": {"ast_fields": {"guid": "ba818cda-7015-4c74-9a28-3f5a98fb08dc", "name": "console", "language": "TypeScript", "file_path": "file:///main.ts", "namespace": "", "parent_guid": "b9edf75c-e6cc-40f0-8304-15bac6bb6406", "childs_guid": [], "full_range": {"start_byte": 2197, "end_byte": 2204, "start_point": {"row": 114, "column": 0}, "end_point": {"row": 114, "column": 7}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}}}, {"FunctionCall": {"ast_fields": {"guid": "93673d8a-4482-48cd-bacb-d38f2ca5fa37", "name": "add", "language": "TypeScript", "file_path": "file:///main.ts", "namespace": "", "parent_guid": "b9edf75c-e6cc-40f0-8304-15bac6bb6406", "childs_guid": [], "full_range": {"start_byte": 2209, "end_byte": 2259, "start_point": {"row": 114, "column": 12}, "end_point": {"row": 114, "column": 62}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": "21b61734-**************-bcf227b4b7e5", "is_error": false}, "template_types": []}}, {"FunctionCall": {"ast_fields": {"guid": "08da2c02-ce1a-45a3-bf74-2c634c055b8f", "name": "getArray", "language": "TypeScript", "file_path": "file:///main.ts", "namespace": "", "parent_guid": "b9edf75c-e6cc-40f0-8304-15bac6bb6406", "childs_guid": [], "full_range": {"start_byte": 2394, "end_byte": 2424, "start_point": {"row": 121, "column": 15}, "end_point": {"row": 121, "column": 45}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": "75e24b98-84fd-4d63-baac-b736b13f9378", "is_error": false}, "template_types": [{"name": "number", "inference_info": null, "is_pod": true, "namespace": "", "guid": null, "nested_types": []}]}}, {"FunctionCall": {"ast_fields": {"guid": "5440052c-2d04-45c0-80e5-2ba00d457242", "name": "getArray", "language": "TypeScript", "file_path": "file:///main.ts", "namespace": "", "parent_guid": "b9edf75c-e6cc-40f0-8304-15bac6bb6406", "childs_guid": [], "full_range": {"start_byte": 2441, "end_byte": 2482, "start_point": {"row": 122, "column": 15}, "end_point": {"row": 122, "column": 56}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": "a01a313f-aa14-4508-b3b2-015e94b35667", "is_error": false}, "template_types": [{"name": "string", "inference_info": null, "is_pod": true, "namespace": "", "guid": null, "nested_types": []}]}}, {"VariableUsage": {"ast_fields": {"guid": "29bf452d-97fd-4571-b8b6-e283e43374c7", "name": "console", "language": "TypeScript", "file_path": "file:///main.ts", "namespace": "", "parent_guid": "b9edf75c-e6cc-40f0-8304-15bac6bb6406", "childs_guid": [], "full_range": {"start_byte": 2485, "end_byte": 2492, "start_point": {"row": 124, "column": 0}, "end_point": {"row": 124, "column": 7}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}}}, {"VariableUsage": {"ast_fields": {"guid": "0e28c448-6a59-4779-8d59-22e7c318e5a3", "name": "numArray", "language": "TypeScript", "file_path": "file:///main.ts", "namespace": "", "parent_guid": "b9edf75c-e6cc-40f0-8304-15bac6bb6406", "childs_guid": [], "full_range": {"start_byte": 2497, "end_byte": 2505, "start_point": {"row": 124, "column": 12}, "end_point": {"row": 124, "column": 20}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}}}, {"VariableUsage": {"ast_fields": {"guid": "165baa76-67c5-46b8-857a-3cf5302b0d55", "name": "console", "language": "TypeScript", "file_path": "file:///main.ts", "namespace": "", "parent_guid": "b9edf75c-e6cc-40f0-8304-15bac6bb6406", "childs_guid": [], "full_range": {"start_byte": 2508, "end_byte": 2523, "start_point": {"row": 125, "column": 0}, "end_point": {"row": 125, "column": 15}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": "cef7ef89-c570-4cef-9cae-50b1c781fbef", "is_error": false}}}, {"VariableUsage": {"ast_fields": {"guid": "57538542-205f-4ac7-bc1d-bc41034e4716", "name": "strArray", "language": "TypeScript", "file_path": "file:///main.ts", "namespace": "", "parent_guid": "b9edf75c-e6cc-40f0-8304-15bac6bb6406", "childs_guid": [], "full_range": {"start_byte": 2528, "end_byte": 2536, "start_point": {"row": 125, "column": 20}, "end_point": {"row": 125, "column": 28}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}}}, {"ClassFieldDeclaration": {"ast_fields": {"guid": "1b995663-ff61-490a-ac19-4f2b7b20dca6", "name": "length", "language": "TypeScript", "file_path": "file:///main.ts", "namespace": "", "parent_guid": "037b3d16-b01d-4008-9c63-85f70d90df00", "childs_guid": [], "full_range": {"start_byte": 2592, "end_byte": 2606, "start_point": {"row": 129, "column": 4}, "end_point": {"row": 129, "column": 18}}, "declaration_range": {"start_byte": 2592, "end_byte": 2606, "start_point": {"row": 129, "column": 4}, "end_point": {"row": 129, "column": 18}}, "definition_range": {"start_byte": 2592, "end_byte": 2606, "start_point": {"row": 129, "column": 4}, "end_point": {"row": 129, "column": 18}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}, "type_": {"name": "number", "inference_info": null, "is_pod": true, "namespace": "", "guid": null, "nested_types": []}}}, {"VariableUsage": {"ast_fields": {"guid": "7f3c4804-e209-4010-bcbd-3708b68079f0", "name": "console", "language": "TypeScript", "file_path": "file:///main.ts", "namespace": "", "parent_guid": "b9edf75c-e6cc-40f0-8304-15bac6bb6406", "childs_guid": [], "full_range": {"start_byte": 2949, "end_byte": 2956, "start_point": {"row": 142, "column": 0}, "end_point": {"row": 142, "column": 7}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}}}, {"FunctionCall": {"ast_fields": {"guid": "59df5fe5-6df2-46e2-8e7b-e268f93d4371", "name": "countAndDescribe", "language": "TypeScript", "file_path": "file:///main.ts", "namespace": "", "parent_guid": "b9edf75c-e6cc-40f0-8304-15bac6bb6406", "childs_guid": [], "full_range": {"start_byte": 2961, "end_byte": 2992, "start_point": {"row": 142, "column": 12}, "end_point": {"row": 142, "column": 43}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": "f2c4c376-a391-448f-aacd-1cde5d815bb9", "is_error": false}, "template_types": []}}, {"VariableDefinition": {"ast_fields": {"guid": "b4396882-25aa-4c3b-ac91-4a20d86f4dbb", "name": "j<PERSON><PERSON><PERSON>", "language": "TypeScript", "file_path": "file:///main.ts", "namespace": "", "parent_guid": "b9edf75c-e6cc-40f0-8304-15bac6bb6406", "childs_guid": [], "full_range": {"start_byte": 3008, "end_byte": 3041, "start_point": {"row": 144, "column": 12}, "end_point": {"row": 144, "column": 45}}, "declaration_range": {"start_byte": 3008, "end_byte": 3041, "start_point": {"row": 144, "column": 12}, "end_point": {"row": 144, "column": 45}}, "definition_range": {"start_byte": 3008, "end_byte": 3041, "start_point": {"row": 144, "column": 12}, "end_point": {"row": 144, "column": 45}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}, "type_": {"name": "function", "inference_info": null, "is_pod": false, "namespace": "", "guid": null, "nested_types": [{"name": "string", "inference_info": null, "is_pod": true, "namespace": "", "guid": null, "nested_types": []}, {"name": "any", "inference_info": null, "is_pod": true, "namespace": "", "guid": null, "nested_types": []}]}}}, {"VariableUsage": {"ast_fields": {"guid": "799839db-84e6-407f-8720-1205423aa369", "name": "cid", "language": "TypeScript", "file_path": "file:///main.ts", "namespace": "", "parent_guid": "b9edf75c-e6cc-40f0-8304-15bac6bb6406", "childs_guid": [], "full_range": {"start_byte": 1093, "end_byte": 1096, "start_point": {"row": 56, "column": 25}, "end_point": {"row": 56, "column": 28}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}}}, {"VariableDefinition": {"ast_fields": {"guid": "429abdda-c235-4cc3-b662-21b1b90f701c", "name": "s", "language": "TypeScript", "file_path": "file:///main.ts", "namespace": "", "parent_guid": "654f08a7-f558-47f3-be49-21696d5a12b0", "childs_guid": [], "full_range": {"start_byte": 1168, "end_byte": 1173, "start_point": {"row": 60, "column": 8}, "end_point": {"row": 60, "column": 13}}, "declaration_range": {"start_byte": 1168, "end_byte": 1173, "start_point": {"row": 60, "column": 8}, "end_point": {"row": 60, "column": 13}}, "definition_range": {"start_byte": 1168, "end_byte": 1173, "start_point": {"row": 60, "column": 8}, "end_point": {"row": 60, "column": 13}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}, "type_": {"name": null, "inference_info": "2", "is_pod": false, "namespace": "", "guid": null, "nested_types": []}}}, {"VariableUsage": {"ast_fields": {"guid": "c3eec8df-bb97-45fc-97ef-fcb82e9b1638", "name": "Person", "language": "TypeScript", "file_path": "file:///main.ts", "namespace": "", "parent_guid": "b9edf75c-e6cc-40f0-8304-15bac6bb6406", "childs_guid": [], "full_range": {"start_byte": 1783, "end_byte": 1789, "start_point": {"row": 97, "column": 17}, "end_point": {"row": 97, "column": 23}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}}}, {"VariableUsage": {"ast_fields": {"guid": "9ae5f507-f51b-4e00-8736-f977f670d86e", "name": "GenericNumber", "language": "TypeScript", "file_path": "file:///main.ts", "namespace": "", "parent_guid": "b9edf75c-e6cc-40f0-8304-15bac6bb6406", "childs_guid": [], "full_range": {"start_byte": 1903, "end_byte": 1916, "start_point": {"row": 104, "column": 26}, "end_point": {"row": 104, "column": 39}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}}}, {"VariableUsage": {"ast_fields": {"guid": "2f6eaeaf-d2b8-4387-afa7-88400b812680", "name": "myGenericNumber", "language": "TypeScript", "file_path": "file:///main.ts", "namespace": "", "parent_guid": "b9edf75c-e6cc-40f0-8304-15bac6bb6406", "childs_guid": [], "full_range": {"start_byte": 1928, "end_byte": 1943, "start_point": {"row": 105, "column": 0}, "end_point": {"row": 105, "column": 15}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}}}, {"VariableUsage": {"ast_fields": {"guid": "8b1841af-d3bb-4e3c-b581-cb1977213a46", "name": "myGenericNumber", "language": "TypeScript", "file_path": "file:///main.ts", "namespace": "", "parent_guid": "b9edf75c-e6cc-40f0-8304-15bac6bb6406", "childs_guid": [], "full_range": {"start_byte": 1959, "end_byte": 1974, "start_point": {"row": 106, "column": 0}, "end_point": {"row": 106, "column": 15}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}}}, {"VariableUsage": {"ast_fields": {"guid": "5ef7a148-eac2-49c1-ab27-4c9bbd784209", "name": "myGenericNumber", "language": "TypeScript", "file_path": "file:///main.ts", "namespace": "", "parent_guid": "b9edf75c-e6cc-40f0-8304-15bac6bb6406", "childs_guid": [], "full_range": {"start_byte": 2028, "end_byte": 2043, "start_point": {"row": 108, "column": 12}, "end_point": {"row": 108, "column": 27}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}}}, {"VariableUsage": {"ast_fields": {"guid": "000c7a71-b7ec-4bf0-8fce-a6f2dff444b1", "name": "GenericNumber", "language": "TypeScript", "file_path": "file:///main.ts", "namespace": "", "parent_guid": "b9edf75c-e6cc-40f0-8304-15bac6bb6406", "childs_guid": [], "full_range": {"start_byte": 2095, "end_byte": 2108, "start_point": {"row": 110, "column": 24}, "end_point": {"row": 110, "column": 37}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}}}, {"VariableUsage": {"ast_fields": {"guid": "72d91751-f2fe-47cd-838e-646b26dd2718", "name": "stringNumeric", "language": "TypeScript", "file_path": "file:///main.ts", "namespace": "", "parent_guid": "b9edf75c-e6cc-40f0-8304-15bac6bb6406", "childs_guid": [], "full_range": {"start_byte": 2112, "end_byte": 2125, "start_point": {"row": 111, "column": 0}, "end_point": {"row": 111, "column": 13}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}}}, {"VariableUsage": {"ast_fields": {"guid": "b1821076-51ec-407a-a3e3-354493cb39ca", "name": "stringNumeric", "language": "TypeScript", "file_path": "file:///main.ts", "namespace": "", "parent_guid": "b9edf75c-e6cc-40f0-8304-15bac6bb6406", "childs_guid": [], "full_range": {"start_byte": 2142, "end_byte": 2155, "start_point": {"row": 112, "column": 0}, "end_point": {"row": 112, "column": 13}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}}}, {"VariableUsage": {"ast_fields": {"guid": "21b61734-**************-bcf227b4b7e5", "name": "stringNumeric", "language": "TypeScript", "file_path": "file:///main.ts", "namespace": "", "parent_guid": "b9edf75c-e6cc-40f0-8304-15bac6bb6406", "childs_guid": [], "full_range": {"start_byte": 2209, "end_byte": 2222, "start_point": {"row": 114, "column": 12}, "end_point": {"row": 114, "column": 25}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}}}, {"VariableUsage": {"ast_fields": {"guid": "80aba414-27fb-45eb-a9ab-6be99d00ee70", "name": "zeroValue", "language": "TypeScript", "file_path": "file:///main.ts", "namespace": "", "parent_guid": "b9edf75c-e6cc-40f0-8304-15bac6bb6406", "childs_guid": [], "full_range": {"start_byte": 2227, "end_byte": 2250, "start_point": {"row": 114, "column": 30}, "end_point": {"row": 114, "column": 53}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": "19ff3819-9532-4516-b0ad-78326bb47211", "is_error": false}}}, {"FunctionCall": {"ast_fields": {"guid": "448bbb26-9bed-4b8a-9177-ca47afba4ae5", "name": "concat", "language": "TypeScript", "file_path": "file:///main.ts", "namespace": "", "parent_guid": "b8a4ec2a-83de-428b-9aee-8a30662e88a2", "childs_guid": [], "full_range": {"start_byte": 2346, "end_byte": 2374, "start_point": {"row": 118, "column": 11}, "end_point": {"row": 118, "column": 39}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": "32ee4527-aea8-4c03-9812-87cb1889638b", "is_error": false}, "template_types": []}}, {"VariableUsage": {"ast_fields": {"guid": "cef7ef89-c570-4cef-9cae-50b1c781fbef", "name": "console", "language": "TypeScript", "file_path": "file:///main.ts", "namespace": "", "parent_guid": "b9edf75c-e6cc-40f0-8304-15bac6bb6406", "childs_guid": [], "full_range": {"start_byte": 2508, "end_byte": 2515, "start_point": {"row": 125, "column": 0}, "end_point": {"row": 125, "column": 7}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}}}, {"VariableDefinition": {"ast_fields": {"guid": "806da964-b623-4687-a64a-9cb9e0bcffc4", "name": "descriptionText", "language": "TypeScript", "file_path": "file:///main.ts", "namespace": "", "parent_guid": "de64c75b-2e61-43c7-92b3-c5aa27910fa7", "childs_guid": [], "full_range": {"start_byte": 2691, "end_byte": 2724, "start_point": {"row": 133, "column": 8}, "end_point": {"row": 133, "column": 41}}, "declaration_range": {"start_byte": 2691, "end_byte": 2724, "start_point": {"row": 133, "column": 8}, "end_point": {"row": 133, "column": 41}}, "definition_range": {"start_byte": 2691, "end_byte": 2724, "start_point": {"row": 133, "column": 8}, "end_point": {"row": 133, "column": 41}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}, "type_": {"name": null, "inference_info": "'Got no value.'", "is_pod": false, "namespace": "", "guid": null, "nested_types": []}}}, {"StructDeclaration": {"ast_fields": {"guid": "640cbdbc-456b-465e-a09f-ecd070235605", "name": "anon-640cbdbc-456b-465e-a09f-ecd070235605", "language": "TypeScript", "file_path": "file:///main.ts", "namespace": "", "parent_guid": "b9edf75c-e6cc-40f0-8304-15bac6bb6406", "childs_guid": ["995ac063-5141-4e9a-8fd4-849fff7b7b48"], "full_range": {"start_byte": 3081, "end_byte": 3151, "start_point": {"row": 148, "column": 21}, "end_point": {"row": 152, "column": 1}}, "declaration_range": {"start_byte": 3081, "end_byte": 3103, "start_point": {"row": 148, "column": 21}, "end_point": {"row": 148, "column": 43}}, "definition_range": {"start_byte": 3104, "end_byte": 3151, "start_point": {"row": 148, "column": 44}, "end_point": {"row": 152, "column": 1}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}, "template_types": [], "inherited_types": []}}, {"VariableUsage": {"ast_fields": {"guid": "f24a36df-fe7d-4dc3-b622-76e641d8250f", "name": "x", "language": "TypeScript", "file_path": "file:///main.ts", "namespace": "", "parent_guid": "654f08a7-f558-47f3-be49-21696d5a12b0", "childs_guid": [], "full_range": {"start_byte": 1186, "end_byte": 1187, "start_point": {"row": 61, "column": 11}, "end_point": {"row": 61, "column": 12}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}}}, {"VariableUsage": {"ast_fields": {"guid": "57eb983a-f46e-46c9-9205-f7efda679e88", "name": "y", "language": "TypeScript", "file_path": "file:///main.ts", "namespace": "", "parent_guid": "654f08a7-f558-47f3-be49-21696d5a12b0", "childs_guid": [], "full_range": {"start_byte": 1190, "end_byte": 1191, "start_point": {"row": 61, "column": 15}, "end_point": {"row": 61, "column": 16}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}}}, {"VariableUsage": {"ast_fields": {"guid": "19ff3819-9532-4516-b0ad-78326bb47211", "name": "stringNumeric", "language": "TypeScript", "file_path": "file:///main.ts", "namespace": "", "parent_guid": "b9edf75c-e6cc-40f0-8304-15bac6bb6406", "childs_guid": [], "full_range": {"start_byte": 2227, "end_byte": 2240, "start_point": {"row": 114, "column": 30}, "end_point": {"row": 114, "column": 43}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}}}, {"VariableUsage": {"ast_fields": {"guid": "14b1155c-1072-4956-aad2-4b0bb6650cbf", "name": "items", "language": "TypeScript", "file_path": "file:///main.ts", "namespace": "", "parent_guid": "b8a4ec2a-83de-428b-9aee-8a30662e88a2", "childs_guid": [], "full_range": {"start_byte": 2368, "end_byte": 2373, "start_point": {"row": 118, "column": 33}, "end_point": {"row": 118, "column": 38}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}}}, {"VariableUsage": {"ast_fields": {"guid": "ed1fc537-3309-4fce-ba17-c1b6bc440f4d", "name": "element", "language": "TypeScript", "file_path": "file:///main.ts", "namespace": "", "parent_guid": "de64c75b-2e61-43c7-92b3-c5aa27910fa7", "childs_guid": [], "full_range": {"start_byte": 2919, "end_byte": 2926, "start_point": {"row": 139, "column": 12}, "end_point": {"row": 139, "column": 19}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}}}, {"VariableUsage": {"ast_fields": {"guid": "38cd32b6-985b-4e49-8484-228ddb854388", "name": "descriptionText", "language": "TypeScript", "file_path": "file:///main.ts", "namespace": "", "parent_guid": "de64c75b-2e61-43c7-92b3-c5aa27910fa7", "childs_guid": [], "full_range": {"start_byte": 2928, "end_byte": 2943, "start_point": {"row": 139, "column": 21}, "end_point": {"row": 139, "column": 36}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}}}, {"VariableDefinition": {"ast_fields": {"guid": "b4474e35-e769-46af-98c1-644d830fe81f", "name": "dx", "language": "TypeScript", "file_path": "file:///main.ts", "namespace": "", "parent_guid": "6b94e540-6bf1-4aa7-bb75-e495d61810aa", "childs_guid": [], "full_range": {"start_byte": 1324, "end_byte": 1345, "start_point": {"row": 68, "column": 12}, "end_point": {"row": 68, "column": 33}}, "declaration_range": {"start_byte": 1324, "end_byte": 1345, "start_point": {"row": 68, "column": 12}, "end_point": {"row": 68, "column": 33}}, "definition_range": {"start_byte": 1324, "end_byte": 1345, "start_point": {"row": 68, "column": 12}, "end_point": {"row": 68, "column": 33}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}, "type_": {"name": null, "inference_info": "other.x - this.x", "is_pod": false, "namespace": "", "guid": null, "nested_types": []}}}, {"VariableDefinition": {"ast_fields": {"guid": "071d8469-78b2-415e-95d7-dc11e0eee25a", "name": "dy", "language": "TypeScript", "file_path": "file:///main.ts", "namespace": "", "parent_guid": "6b94e540-6bf1-4aa7-bb75-e495d61810aa", "childs_guid": [], "full_range": {"start_byte": 1359, "end_byte": 1380, "start_point": {"row": 69, "column": 12}, "end_point": {"row": 69, "column": 33}}, "declaration_range": {"start_byte": 1359, "end_byte": 1380, "start_point": {"row": 69, "column": 12}, "end_point": {"row": 69, "column": 33}}, "definition_range": {"start_byte": 1359, "end_byte": 1380, "start_point": {"row": 69, "column": 12}, "end_point": {"row": 69, "column": 33}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}, "type_": {"name": null, "inference_info": "other.y - this.y", "is_pod": false, "namespace": "", "guid": null, "nested_types": []}}}, {"FunctionCall": {"ast_fields": {"guid": "27b34780-f62f-46fd-bb32-eb79978d02dd", "name": "sqrt", "language": "TypeScript", "file_path": "file:///main.ts", "namespace": "", "parent_guid": "6b94e540-6bf1-4aa7-bb75-e495d61810aa", "childs_guid": [], "full_range": {"start_byte": 1397, "end_byte": 1425, "start_point": {"row": 70, "column": 15}, "end_point": {"row": 70, "column": 43}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": "2f8e3d70-30fd-40d2-848a-a8d3cc47a19f", "is_error": false}, "template_types": []}}, {"VariableUsage": {"ast_fields": {"guid": "32ee4527-aea8-4c03-9812-87cb1889638b", "name": "Array", "language": "TypeScript", "file_path": "file:///main.ts", "namespace": "", "parent_guid": "b8a4ec2a-83de-428b-9aee-8a30662e88a2", "childs_guid": [], "full_range": {"start_byte": 2350, "end_byte": 2355, "start_point": {"row": 118, "column": 15}, "end_point": {"row": 118, "column": 20}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}}}, {"VariableUsage": {"ast_fields": {"guid": "a3ea76cf-0759-40bd-853d-4f30a86ef8da", "name": "length", "language": "TypeScript", "file_path": "file:///main.ts", "namespace": "", "parent_guid": "de64c75b-2e61-43c7-92b3-c5aa27910fa7", "childs_guid": [], "full_range": {"start_byte": 2734, "end_byte": 2748, "start_point": {"row": 134, "column": 8}, "end_point": {"row": 134, "column": 22}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": "5dd581c9-42fb-471c-8c2e-84ffdf96f2de", "is_error": false}}}, {"FunctionDeclaration": {"ast_fields": {"guid": "995ac063-5141-4e9a-8fd4-849fff7b7b48", "name": "run", "language": "TypeScript", "file_path": "file:///main.ts", "namespace": "", "parent_guid": "640cbdbc-456b-465e-a09f-ecd070235605", "childs_guid": ["11cc73ac-f6e2-413b-8bb2-ad1512da0a9a"], "full_range": {"start_byte": 3110, "end_byte": 3149, "start_point": {"row": 149, "column": 4}, "end_point": {"row": 151, "column": 5}}, "declaration_range": {"start_byte": 3110, "end_byte": 3116, "start_point": {"row": 149, "column": 4}, "end_point": {"row": 149, "column": 10}}, "definition_range": {"start_byte": 3116, "end_byte": 3149, "start_point": {"row": 149, "column": 10}, "end_point": {"row": 151, "column": 5}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}, "template_types": [], "args": [], "return_type": null}}, {"VariableUsage": {"ast_fields": {"guid": "2f8e3d70-30fd-40d2-848a-a8d3cc47a19f", "name": "Math", "language": "TypeScript", "file_path": "file:///main.ts", "namespace": "", "parent_guid": "6b94e540-6bf1-4aa7-bb75-e495d61810aa", "childs_guid": [], "full_range": {"start_byte": 1397, "end_byte": 1401, "start_point": {"row": 70, "column": 15}, "end_point": {"row": 70, "column": 19}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}}}, {"VariableUsage": {"ast_fields": {"guid": "5a706f7d-0f4a-463b-b885-b13ebddd073d", "name": "id", "language": "TypeScript", "file_path": "file:///main.ts", "namespace": "", "parent_guid": "ff3cb01f-9bf7-42a0-a4e1-e3b472f85a4a", "childs_guid": [], "full_range": {"start_byte": 1717, "end_byte": 1724, "start_point": {"row": 92, "column": 8}, "end_point": {"row": 92, "column": 15}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": "2d9abcee-dbf4-4c24-b6d7-05d1ce359835", "is_error": false}}}, {"VariableUsage": {"ast_fields": {"guid": "741ae1df-2d45-441e-be1e-8af2064e78df", "name": "id", "language": "TypeScript", "file_path": "file:///main.ts", "namespace": "", "parent_guid": "ff3cb01f-9bf7-42a0-a4e1-e3b472f85a4a", "childs_guid": [], "full_range": {"start_byte": 1727, "end_byte": 1729, "start_point": {"row": 92, "column": 18}, "end_point": {"row": 92, "column": 20}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}}}, {"VariableUsage": {"ast_fields": {"guid": "6022cd69-3f81-4ca5-8801-22e7ef7577dd", "name": "name", "language": "TypeScript", "file_path": "file:///main.ts", "namespace": "", "parent_guid": "ff3cb01f-9bf7-42a0-a4e1-e3b472f85a4a", "childs_guid": [], "full_range": {"start_byte": 1739, "end_byte": 1748, "start_point": {"row": 93, "column": 8}, "end_point": {"row": 93, "column": 17}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": "80c5ddf7-9f90-4208-a71a-94593dce6063", "is_error": false}}}, {"VariableUsage": {"ast_fields": {"guid": "5a212f92-db09-4151-896f-3ce00fa67f3b", "name": "name", "language": "TypeScript", "file_path": "file:///main.ts", "namespace": "", "parent_guid": "ff3cb01f-9bf7-42a0-a4e1-e3b472f85a4a", "childs_guid": [], "full_range": {"start_byte": 1751, "end_byte": 1755, "start_point": {"row": 93, "column": 20}, "end_point": {"row": 93, "column": 24}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}}}, {"VariableUsage": {"ast_fields": {"guid": "68e94931-a2b1-433a-887d-01fb0a3d3c2a", "name": "x", "language": "TypeScript", "file_path": "file:///main.ts", "namespace": "", "parent_guid": "8f1620fb-41d5-4f0c-9962-a84e136e1e58", "childs_guid": [], "full_range": {"start_byte": 2005, "end_byte": 2006, "start_point": {"row": 106, "column": 46}, "end_point": {"row": 106, "column": 47}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}}}, {"VariableUsage": {"ast_fields": {"guid": "5826214b-2b33-4844-9208-************", "name": "y", "language": "TypeScript", "file_path": "file:///main.ts", "namespace": "", "parent_guid": "8f1620fb-41d5-4f0c-9962-a84e136e1e58", "childs_guid": [], "full_range": {"start_byte": 2009, "end_byte": 2010, "start_point": {"row": 106, "column": 50}, "end_point": {"row": 106, "column": 51}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}}}, {"VariableUsage": {"ast_fields": {"guid": "4077f930-f17e-448a-8aeb-f6980b44866f", "name": "x", "language": "TypeScript", "file_path": "file:///main.ts", "namespace": "", "parent_guid": "620692fb-f6b5-4da1-be22-177c60ead098", "childs_guid": [], "full_range": {"start_byte": 2186, "end_byte": 2187, "start_point": {"row": 112, "column": 44}, "end_point": {"row": 112, "column": 45}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}}}, {"VariableUsage": {"ast_fields": {"guid": "5a379715-4aca-45bd-83c1-96473bfe76da", "name": "y", "language": "TypeScript", "file_path": "file:///main.ts", "namespace": "", "parent_guid": "620692fb-f6b5-4da1-be22-177c60ead098", "childs_guid": [], "full_range": {"start_byte": 2190, "end_byte": 2191, "start_point": {"row": 112, "column": 48}, "end_point": {"row": 112, "column": 49}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}}}, {"VariableUsage": {"ast_fields": {"guid": "5dd581c9-42fb-471c-8c2e-84ffdf96f2de", "name": "element", "language": "TypeScript", "file_path": "file:///main.ts", "namespace": "", "parent_guid": "de64c75b-2e61-43c7-92b3-c5aa27910fa7", "childs_guid": [], "full_range": {"start_byte": 2734, "end_byte": 2741, "start_point": {"row": 134, "column": 8}, "end_point": {"row": 134, "column": 15}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}}}, {"VariableUsage": {"ast_fields": {"guid": "c2fa459c-a3ee-4422-95b8-9a8f6a18e68d", "name": "descriptionText", "language": "TypeScript", "file_path": "file:///main.ts", "namespace": "", "parent_guid": "de64c75b-2e61-43c7-92b3-c5aa27910fa7", "childs_guid": [], "full_range": {"start_byte": 2766, "end_byte": 2781, "start_point": {"row": 135, "column": 8}, "end_point": {"row": 135, "column": 23}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}}}, {"VariableUsage": {"ast_fields": {"guid": "44b47115-c6a0-4fec-99db-95979cbefd25", "name": "x", "language": "TypeScript", "file_path": "file:///main.ts", "namespace": "", "parent_guid": "6b94e540-6bf1-4aa7-bb75-e495d61810aa", "childs_guid": [], "full_range": {"start_byte": 1329, "end_byte": 1336, "start_point": {"row": 68, "column": 17}, "end_point": {"row": 68, "column": 24}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": "afcf590f-8fea-4c5b-9ad6-461d4427febb", "is_error": false}}}, {"VariableUsage": {"ast_fields": {"guid": "9961390c-a184-4f88-bd38-c0021248b7d6", "name": "x", "language": "TypeScript", "file_path": "file:///main.ts", "namespace": "", "parent_guid": "6b94e540-6bf1-4aa7-bb75-e495d61810aa", "childs_guid": [], "full_range": {"start_byte": 1339, "end_byte": 1345, "start_point": {"row": 68, "column": 27}, "end_point": {"row": 68, "column": 33}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": "8a82f827-0037-485d-bae6-ee3cdd00613d", "is_error": false}}}, {"VariableUsage": {"ast_fields": {"guid": "1cd2cdd4-c968-4c3f-9e52-ef470dac3d98", "name": "y", "language": "TypeScript", "file_path": "file:///main.ts", "namespace": "", "parent_guid": "6b94e540-6bf1-4aa7-bb75-e495d61810aa", "childs_guid": [], "full_range": {"start_byte": 1364, "end_byte": 1371, "start_point": {"row": 69, "column": 17}, "end_point": {"row": 69, "column": 24}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": "776ccdb7-ff5c-4579-b08a-01b24b62d99b", "is_error": false}}}, {"VariableUsage": {"ast_fields": {"guid": "3af9cc54-be3d-4e8c-a362-0344f539da5e", "name": "y", "language": "TypeScript", "file_path": "file:///main.ts", "namespace": "", "parent_guid": "6b94e540-6bf1-4aa7-bb75-e495d61810aa", "childs_guid": [], "full_range": {"start_byte": 1374, "end_byte": 1380, "start_point": {"row": 69, "column": 27}, "end_point": {"row": 69, "column": 33}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": "6c1a4f09-2dce-45b1-ae1a-a469e5dbd1a0", "is_error": false}}}, {"VariableUsage": {"ast_fields": {"guid": "530b5b2a-2387-41d7-aaff-0e8e9ab1a1dd", "name": "length", "language": "TypeScript", "file_path": "file:///main.ts", "namespace": "", "parent_guid": "de64c75b-2e61-43c7-92b3-c5aa27910fa7", "childs_guid": [], "full_range": {"start_byte": 2815, "end_byte": 2829, "start_point": {"row": 136, "column": 15}, "end_point": {"row": 136, "column": 29}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": "63266d38-7f37-4ec5-9639-bcd0537aaf13", "is_error": false}}}, {"CommentDefinition": {"ast_fields": {"guid": "11cc73ac-f6e2-413b-8bb2-ad1512da0a9a", "name": "", "language": "TypeScript", "file_path": "file:///main.ts", "namespace": "", "parent_guid": "995ac063-5141-4e9a-8fd4-849fff7b7b48", "childs_guid": [], "full_range": {"start_byte": 3126, "end_byte": 3143, "start_point": {"row": 150, "column": 8}, "end_point": {"row": 150, "column": 25}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}}}, {"VariableUsage": {"ast_fields": {"guid": "afcf590f-8fea-4c5b-9ad6-461d4427febb", "name": "other", "language": "TypeScript", "file_path": "file:///main.ts", "namespace": "", "parent_guid": "6b94e540-6bf1-4aa7-bb75-e495d61810aa", "childs_guid": [], "full_range": {"start_byte": 1329, "end_byte": 1334, "start_point": {"row": 68, "column": 17}, "end_point": {"row": 68, "column": 22}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}}}, {"VariableUsage": {"ast_fields": {"guid": "776ccdb7-ff5c-4579-b08a-01b24b62d99b", "name": "other", "language": "TypeScript", "file_path": "file:///main.ts", "namespace": "", "parent_guid": "6b94e540-6bf1-4aa7-bb75-e495d61810aa", "childs_guid": [], "full_range": {"start_byte": 1364, "end_byte": 1369, "start_point": {"row": 69, "column": 17}, "end_point": {"row": 69, "column": 22}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}}}, {"VariableUsage": {"ast_fields": {"guid": "7a651f48-c3f7-40c1-b146-ff93c276c0d9", "name": "dx", "language": "TypeScript", "file_path": "file:///main.ts", "namespace": "", "parent_guid": "6b94e540-6bf1-4aa7-bb75-e495d61810aa", "childs_guid": [], "full_range": {"start_byte": 1407, "end_byte": 1409, "start_point": {"row": 70, "column": 25}, "end_point": {"row": 70, "column": 27}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}}}, {"VariableUsage": {"ast_fields": {"guid": "21daccbe-54a6-4b79-ab1a-fe061f365359", "name": "dx", "language": "TypeScript", "file_path": "file:///main.ts", "namespace": "", "parent_guid": "6b94e540-6bf1-4aa7-bb75-e495d61810aa", "childs_guid": [], "full_range": {"start_byte": 1412, "end_byte": 1414, "start_point": {"row": 70, "column": 30}, "end_point": {"row": 70, "column": 32}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}}}, {"VariableUsage": {"ast_fields": {"guid": "71a50340-60a3-4860-8203-d2bb74784caf", "name": "dy", "language": "TypeScript", "file_path": "file:///main.ts", "namespace": "", "parent_guid": "6b94e540-6bf1-4aa7-bb75-e495d61810aa", "childs_guid": [], "full_range": {"start_byte": 1417, "end_byte": 1419, "start_point": {"row": 70, "column": 35}, "end_point": {"row": 70, "column": 37}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}}}, {"VariableUsage": {"ast_fields": {"guid": "55bc04c6-9fb8-4809-9551-f441d4797f47", "name": "dy", "language": "TypeScript", "file_path": "file:///main.ts", "namespace": "", "parent_guid": "6b94e540-6bf1-4aa7-bb75-e495d61810aa", "childs_guid": [], "full_range": {"start_byte": 1422, "end_byte": 1424, "start_point": {"row": 70, "column": 40}, "end_point": {"row": 70, "column": 42}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}}}, {"VariableUsage": {"ast_fields": {"guid": "63266d38-7f37-4ec5-9639-bcd0537aaf13", "name": "element", "language": "TypeScript", "file_path": "file:///main.ts", "namespace": "", "parent_guid": "de64c75b-2e61-43c7-92b3-c5aa27910fa7", "childs_guid": [], "full_range": {"start_byte": 2815, "end_byte": 2822, "start_point": {"row": 136, "column": 15}, "end_point": {"row": 136, "column": 22}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}}}, {"VariableUsage": {"ast_fields": {"guid": "901f6b9c-3f30-4ecc-b887-e4de092f5330", "name": "descriptionText", "language": "TypeScript", "file_path": "file:///main.ts", "namespace": "", "parent_guid": "de64c75b-2e61-43c7-92b3-c5aa27910fa7", "childs_guid": [], "full_range": {"start_byte": 2845, "end_byte": 2860, "start_point": {"row": 137, "column": 8}, "end_point": {"row": 137, "column": 23}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}}}, {"VariableUsage": {"ast_fields": {"guid": "a08f15ea-57fb-4fd7-9967-8a6d24852c54", "name": "length", "language": "TypeScript", "file_path": "file:///main.ts", "namespace": "", "parent_guid": "de64c75b-2e61-43c7-92b3-c5aa27910fa7", "childs_guid": [], "full_range": {"start_byte": 2872, "end_byte": 2886, "start_point": {"row": 137, "column": 35}, "end_point": {"row": 137, "column": 49}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": "92b94910-2c81-492e-a683-d037b782851d", "is_error": false}}}, {"VariableUsage": {"ast_fields": {"guid": "92b94910-2c81-492e-a683-d037b782851d", "name": "element", "language": "TypeScript", "file_path": "file:///main.ts", "namespace": "", "parent_guid": "de64c75b-2e61-43c7-92b3-c5aa27910fa7", "childs_guid": [], "full_range": {"start_byte": 2872, "end_byte": 2879, "start_point": {"row": 137, "column": 35}, "end_point": {"row": 137, "column": 42}}, "declaration_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "definition_range": {"start_byte": 0, "end_byte": 0, "start_point": {"row": 0, "column": 0}, "end_point": {"row": 0, "column": 0}}, "linked_decl_guid": null, "caller_guid": null, "is_error": false}}}]