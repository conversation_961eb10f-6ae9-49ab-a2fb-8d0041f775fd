#[cfg(test)]  
mod tests {  
    use std::fs::canonicalize;  
    use std::path::PathBuf;  
  
    use crate::ast::treesitter::language_id::LanguageId;  
    use crate::ast::treesitter::parsers::AstLanguageParser;  
    use crate::ast::treesitter::parsers::html::HtmlParser;  
    use crate::ast::treesitter::parsers::tests::{base_declaration_formatter_test, base_parser_test, base_skeletonizer_test};  
  
    const MAIN_HTML_CODE: &str = include_str!("cases/html/main.html");  
    const MAIN_HTML_SYMBOLS: &str = include_str!("cases/html/main.html.json");  
  
    const FORM_HTML_CODE: &str = include_str!("cases/html/form.html");  
    // const FORM_HTML_SKELETON: &str = include_str!("cases/html/form.html.skeleton");  
    // const FORM_HTML_DECLS: &str = include_str!("cases/html/form.html.decl_json");  
  
    // #[test]  
    // fn parser_test() {  
    //     let mut parser: Box<dyn AstLanguageParser> = Box::new(HtmlParser::new().expect("HtmlParser::new"));  
    //     let path = PathBuf::from("file:///main.html");  
    //     base_parser_test(&mut parser, &path, MAIN_HTML_CODE, MAIN_HTML_SYMBOLS);  
    // }  
  
    // #[test]  
    // fn skeletonizer_test() {  
    //     let mut parser: Box<dyn AstLanguageParser> = Box::new(HtmlParser::new().expect("HtmlParser::new"));  
    //     let file = canonicalize(PathBuf::from(file!())).unwrap().parent().unwrap().join("cases/html/form.html");  
    //     assert!(file.exists());  
  
    //     base_skeletonizer_test(&LanguageId::Html, &mut parser, &file, FORM_HTML_CODE, FORM_HTML_SKELETON);  
    // }  
  
    #[test]  
    fn declaration_formatter_test() {  
        let mut parser: Box<dyn AstLanguageParser> = Box::new(HtmlParser::new().expect("HtmlParser::new"));  
        let file = canonicalize(PathBuf::from(file!())).unwrap().parent().unwrap().join("cases/html/form.html");  
        assert!(file.exists());  
        base_declaration_formatter_test(&LanguageId::Html, &mut parser, &file, FORM_HTML_CODE, FORM_HTML_DECLS);  
    }  

    #[test]  
    #[ignore] // Chỉ chạy khi cần generate  
    fn generate_reference_files() {  
        let mut parser = HtmlParser::new().expect("HtmlParser::new");  
        let path: PathBuf = PathBuf::from("/home/<USER>/Documents/GitHub/innocody-engine/src/ast/treesitter/parsers/tests/cases/html/form.html");  
        let symbols = parser.parse(MAIN_HTML_CODE, &path);  
        
        // Convert to JSON và save  
        let json_output = serde_json::to_string_pretty(&symbols).unwrap();  
        std::fs::write("main.html.json", json_output).unwrap();  
    }
}