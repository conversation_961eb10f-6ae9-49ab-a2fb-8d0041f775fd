# Tree-sitter Integration Module (`src/ast/treesitter`)

## Overview
This module provides a unified interface for parsing, analyzing, and extracting symbols from source code in multiple programming languages using [Tree-sitter](https://tree-sitter.github.io/tree-sitter/). It is the core engine for language parsing and symbol extraction in the project, enabling language-agnostic code intelligence and navigation.

---

## Key Responsibilities
- **Language Detection:** Identifies the programming language of a file using file extensions or content.
- **Parsing:** Uses Tree-sitter grammars and custom logic to parse code into ASTs.
- **Symbol Extraction:** Extracts rich symbol information (functions, classes, variables, etc.) with ranges, types, and relationships.
- **Skeletonization:** Generates simplified code skeletons for summarization, chunking, or display.
- **Extensibility:** Easily add support for new languages by implementing a parser and skeletonizer.

---

## Main Components

### Core Files
- **`mod.rs`**: Module entry point; re-exports all submodules.
- **`language_id.rs`**: Enum and utilities for supported languages; maps file extensions and Tree-sitter grammars to internal IDs.
- **`parsers.rs`**: Dispatcher and trait definitions for language parsers. Handles parser selection and normalization.
- **`structs.rs`**: Core types for symbol kinds (`SymbolType`), source code ranges, and points.
- **`ast_instance_structs.rs`**: Rich, extensible structs for all symbol types (functions, classes, variables, etc.), with trait-based polymorphism.
- **`skeletonizer.rs`**: Logic for generating code skeletons and extracting declarations with comments.
- **`file_ast_markup.rs`**: Utilities for marking up files with AST-derived symbol data.
- **`parser_utils.rs`**: Helpers for parser normalization (e.g., line endings).

### Language Parsers (`parsers/`)
- **Language-specific files**: Implement the `AstLanguageParser` trait for each language (e.g., `python.rs`, `cpp.rs`, `java.rs`, `js.rs`, `ts.rs`, `rust.rs`, `html.rs`).
- **Test Cases**: Comprehensive test suites for each language in `parsers/tests/` and `parsers/tests/cases/`.

---

## Data Flow & Logic
1. **Language Detection**: `get_language_id_by_filename` determines the language from the file extension.
2. **Parser Selection**: `get_ast_parser` or `get_ast_parser_by_filename` returns the appropriate parser.
3. **Parsing**: The parser builds an AST and extracts symbols as `AstSymbolInstance` objects.
4. **Symbol Modeling**: Symbols are represented with rich metadata (name, type, range, parent/child, etc.).
5. **Skeletonization**: Skeletonizers generate minimal code representations for summarization or chunking.

---

## Coding Style & Conventions
- **Trait-Based Design**: Uses Rust traits for extensibility and polymorphism (e.g., `AstLanguageParser`, `SkeletonFormatter`).
- **Async & Sync**: Uses `Arc<RwLock<...>>` for thread-safe, shareable AST nodes.
- **Serde Serialization**: All symbol types and ranges are serializable for database storage and IPC.
- **Idiomatic Rust**: Follows Rust best practices for error handling, modularity, and naming.

---

## Extending & Contributing
- **Add a Language**:
  1. Implement the `AstLanguageParser` trait in `parsers/<language>.rs`.
  2. Add skeletonizer logic if needed in `skeletonizer.rs`.
  3. Register the parser in `parsers.rs` and update `language_id.rs`.
  4. Add test cases in `parsers/tests/` and `parsers/tests/cases/`.
- **Add Symbol Types**: Extend `SymbolType` and implement new structs in `ast_instance_structs.rs`.
- **Testing**: Add or update tests for new features or languages.

---

## References & Further Reading
- [Tree-sitter Documentation](https://tree-sitter.github.io/tree-sitter/)
- [Serde](https://serde.rs/) — serialization framework used for AST data
- See language-specific files and test cases for implementation examples

---

*For more details, see the main project README or open an issue for questions and contributions!*
