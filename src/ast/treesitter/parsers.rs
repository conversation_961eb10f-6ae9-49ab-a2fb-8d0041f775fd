use std::borrow::Cow;
use std::fmt::Display;
use std::path::PathBuf;

use crate::ast::treesitter::parser_utils::normalize_line_endings;

use tracing::error;

use crate::ast::treesitter::ast_instance_structs::AstSymbolInstanceArc;
use crate::ast::treesitter::language_id::LanguageId;


pub(crate) mod python;
pub(crate) mod rust;
#[cfg(test)]
mod tests;
mod utils;
mod java;
mod cpp;
mod ts;
mod js;
pub(crate) mod html;


#[derive(Debug, PartialEq, Eq)]
pub struct ParserError {
    pub message: String,
}

pub trait AstLanguageParser: Send {
    fn parse(&mut self, code: &str, path: &PathBuf) -> Vec<AstSymbolInstanceArc>;
}

fn internal_error<E: Display>(err: E) -> ParserError {
    let err_msg = err.to_string();
    error!(err_msg);
    ParserError {
        message: err_msg.into(),
    }
}

pub struct NormalizedParserWrapper {
    inner: Box<dyn AstLanguageParser>,
}

impl NormalizedParserWrapper {
    pub fn new(inner: Box<dyn AstLanguageParser>) -> Self {
        Self { inner }
    }

    fn normalize_line_endings<'a>(&self, code: &'a str) -> Cow<'a, str> {
        return normalize_line_endings(code);
    }
}

impl AstLanguageParser for NormalizedParserWrapper {
    fn parse(&mut self, code: &str, path: &PathBuf) -> Vec<AstSymbolInstanceArc> {
        let normalized_code = self.normalize_line_endings(code);
        self.inner.parse(&normalized_code, path)
    }
}

pub(crate) fn get_ast_parser(language_id: LanguageId) -> Result<Box<dyn AstLanguageParser + 'static>, ParserError> {
    let parser: Box<dyn AstLanguageParser> = match language_id {
        LanguageId::Rust => Box::new(rust::RustParser::new()?),
        LanguageId::Python => Box::new(python::PythonParser::new()?),
        LanguageId::Java => Box::new(java::JavaParser::new()?),
        LanguageId::Cpp => Box::new(cpp::CppParser::new()?),
        LanguageId::TypeScript => Box::new(ts::TSParser::new()?),
        LanguageId::JavaScript => Box::new(js::JSParser::new()?),
        LanguageId::TypeScriptReact => Box::new(ts::TSParser::new()?),
        LanguageId::Html => Box::new(html::HtmlParser::new()?),
        other => {
            return Err(ParserError {
                message: "Unsupported language id: ".to_string() + &other.to_string(),
            });
        }
    };

    Ok(Box::new(NormalizedParserWrapper::new(parser)))
}


pub fn get_ast_parser_by_filename(filename: &PathBuf) -> Result<(Box<dyn AstLanguageParser + 'static>, LanguageId), ParserError> {
    let suffix = filename.extension().and_then(|e| e.to_str()).unwrap_or("").to_lowercase();
    let maybe_language_id = get_language_id_by_filename(filename);
    match maybe_language_id {
        Some(language_id) => {
            let parser = get_ast_parser(language_id)?;
            Ok((parser, language_id))
        }
        None => Err(ParserError { message: format!("not supported {}", suffix) }),
    }
}

pub fn get_language_id_by_filename(filename: &PathBuf) -> Option<LanguageId> {
    let suffix = filename.extension().and_then(|e| e.to_str()).unwrap_or("").to_lowercase();
    match suffix.as_str() {
        "cpp" | "cc" | "cxx" | "c++" | "c" | "h" | "hpp" | "hxx" | "hh" => Some(LanguageId::Cpp),
        "inl" | "inc" | "tpp" | "tpl" => Some(LanguageId::Cpp),
        "py" | "py3" | "pyx" => Some(LanguageId::Python),
        "java" => Some(LanguageId::Java),
        "js" | "jsx" => Some(LanguageId::JavaScript),
        "rs" => Some(LanguageId::Rust),
        "ts" => Some(LanguageId::TypeScript),
        "tsx" => Some(LanguageId::TypeScriptReact),
        "html" | "htm" => Some(LanguageId::Html),
        _ => None
    }
}

