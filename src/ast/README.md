# AST Module (`src/ast`)

## Overview
The `ast` module is responsible for parsing source code, extracting abstract syntax trees (ASTs), symbol information, and managing a persistent database of code structure and usage. It is the backbone for code intelligence, navigation, and analysis features in the project.

This module supports multiple programming languages (C++, Python, Java, JS, TS, Rust, etc.) via pluggable parsers and a unified symbol model. It enables features such as symbol lookup, usage tracking, type hierarchy analysis, and file chunking for vector databases.

---

## Key Components

### Main Files
- **`mod.rs`**: Module entry point; re-exports submodules and provides utility functions like `lowlevel_file_markup`.
- **`ast_structs.rs`**: Core data structures for AST definitions, usages, error reporting, and database state.
- **`ast_parse_anything.rs`**: Language-agnostic parsing logic; builds symbol maps and definitions from source code.
- **`ast_db.rs`**: Persistent storage for AST data using `sled` (key-value store). Handles symbol definitions, usages, class hierarchies, and counters.
- **`file_splitter.rs`**: Splits files into chunks for vectorization, using AST structure for intelligent boundaries.
- **`chunk_utils.rs`**: Utilities for splitting text into token-limited chunks, with or without tokenizers.
- **`treesitter/`**: Contains language-specific parsers, skeletonizers, and AST instance structures.

### Data Flow
1. **Parsing**: Source files are parsed using language-specific parsers (Tree-sitter-based or custom).
2. **Symbol Extraction**: Symbols (functions, classes, variables, etc.) are extracted and mapped with unique IDs.
3. **Definition & Usage Linking**: Usages are resolved to definitions using path heuristics and type inference.
4. **Database Storage**: All definitions, usages, and hierarchies are stored in a `sled` database for fast lookup and persistence.
5. **Chunking**: Files are split into meaningful chunks for downstream processing (e.g., vector DB ingestion).

---

## Coding Style & Conventions
- **Idiomatic Rust**: Uses `Arc`, `RwLock`, and async primitives for concurrency and shared state.
- **Error Handling**: Errors are collected in `AstErrorStats` and reported with context (file, line, message).
- **Serialization**: Uses `serde` for serializing AST data to/from the database.
- **Naming**: Symbol paths use `::` as a separator; unique IDs (UUIDs) are used for internal references.
- **Extensibility**: New languages can be added by implementing a parser in `treesitter/parsers/` and updating the dispatcher.

---

## Logic Highlights
- **Two-Pass Parsing**: First pass builds symbol map; second pass resolves usages and types.
- **Class Hierarchy**: Maintains inheritance relationships for type resolution and navigation.
- **Chunking**: AST-aware chunking ensures code is split at logical boundaries, not arbitrary lines.
- **Database Keys**: Uses structured keys for fast prefix scans (e.g., `d|` for definitions, `u|` for usages).

---

## Testing
- Unit and integration tests are in each file (see `#[cfg(test)]` modules).
- Test suites with real code samples are in `alt_testsuite/`.
- Run tests with `cargo test` from the project root.

---

## Extending & Contributing
- **Add a Language**: Implement a parser in `treesitter/parsers/`, update the dispatcher, and add tests.
- **Add a Feature**: Extend the relevant struct(s) and update parsing logic in `ast_parse_anything.rs` and/or `ast_db.rs`.
- **Testing**: Add tests in the corresponding file or in `alt_testsuite/`.
- **Style**: Follow Rust best practices; prefer explicit error handling and clear naming.

---

## References & Further Reading
- [Tree-sitter](https://tree-sitter.github.io/tree-sitter/) — incremental parsing library used for language support.
- [`sled`](https://sled.rs/) — embedded database used for AST storage.
- Project files: see `src/ast/treesitter/` for language-specific logic and `src/ast/alt_testsuite/` for test cases.

---

*For questions or contributions, see the main project README or open an issue!*
