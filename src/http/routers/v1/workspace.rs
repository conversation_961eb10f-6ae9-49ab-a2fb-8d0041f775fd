use std::sync::Arc;
use axum::Extension;
use axum::http::{Response, StatusCode};
use hyper::Body;
use serde::{Deserialize, Serialize};
use tokio::sync::RwLock as ARwLock;

use crate::custom_error::ScratchError;
use crate::global_context::GlobalContext;


#[derive(Serialize, Deserialize, Clone, Debug)]
pub struct SetActiveGroupIdPost {
    pub group_id: String,
}


pub async fn handle_v1_set_active_group_id(
    Extension(gcx): Extension<Arc<ARwLock<GlobalContext>>>,
    body_bytes: hyper::body::Bytes,
) -> Result<Response<Body>, ScratchError> {
    let post = serde_json::from_slice::<SetActiveGroupIdPost>(&body_bytes)
        .map_err(|e| ScratchError::new(StatusCode::UNPROCESSABLE_ENTITY, format!("JSON problem: {}", e)))?;

    gcx.write().await.active_group_id = Some(post.group_id);
    crate::cloud::threads_sub::trigger_threads_subscription_restart(gcx.clone()).await;
    
    Ok(Response::builder().status(StatusCode::OK).body(Body::from(
        serde_json::to_string(&serde_json::json!({ "success": true })).unwrap()
    )).unwrap())
}


pub async fn handle_v1_get_app_searchable_id(
    Extension(gcx): Extension<Arc<ARwLock<GlobalContext>>>,
    _body_bytes: hyper::body::Bytes,
) -> Result<Response<Body>, ScratchError> {
    Ok(Response::builder().status(StatusCode::OK).body(Body::from(
        serde_json::to_string(&serde_json::json!({ "app_searchable_id": gcx.read().await.app_searchable_id })).unwrap()
    )).unwrap())
}
