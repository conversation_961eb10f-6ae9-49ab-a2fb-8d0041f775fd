use chrono::{Utc, DateTime};
use std::path::PathBuf;
use std::sync::Arc;
use std::sync::atomic::AtomicBool;
use axum::Extension;
use axum::http::{Response, StatusCode};
use git2::Repository;
use hyper::Body;
use serde::{Deserialize, Serialize};
use tokio::sync::{Notify as ANotify, RwLock as ARwLock};
use url::Url;

use crate::call_validation::ChatMeta;
use crate::files_correction::{deserialize_path, serialize_path};
use crate::custom_error::ScratchError;
use crate::git::{CommitInfo, FileChange};
use crate::git::operations::{get_configured_author_email_and_name, stage_changes};
use crate::git::checkpoints::{
    create_workspace_snapshot, preview_changes_for_workspace_checkpoint, 
    restore_workspace_checkpoint, Checkpoint, remove_all_chat, remove_chat_with_base,
    merge_chat, sync_code_by_commit, sync_code_by_branch, sync_workspace_to_shadow_repo,
};
use crate::global_context::GlobalContext;

#[derive(Serialize, Deserialize, Debug)]
pub struct GitCommitPost {
    pub commits: Vec<CommitInfo>,
}

#[derive(Serialize, Deserialize, Debug)]
pub struct GitError {
    pub error_message: String,
    pub project_name: String,
    pub project_path: Url,
}

#[derive(Serialize, Deserialize, Debug)]
pub struct CheckpointsPost {
    pub checkpoints: Vec<Checkpoint>,
    pub meta: ChatMeta,
}

#[derive(Serialize, Deserialize, Debug)]
pub struct RemoveChatPost {
    pub chat_id: String,
    pub base_chat_id: Option<String>, // Thêm field này để xác định checkpoint gốc
}

#[derive(Serialize, Deserialize, Debug)]
pub struct MergeChatPost {
    pub chat_id: String,
}

#[derive(Serialize, Deserialize, Debug, Default)]
pub struct CheckpointsPreviewResponse {
    pub reverted_changes: Vec<WorkspaceChanges>,
    #[serde(serialize_with = "serialize_datetime_utc")]
    pub reverted_to: DateTime<Utc>,
    pub error_log: Vec<String>,
}

#[derive(Serialize, Deserialize, Debug, Default)]
pub struct CheckpointsRestoreResponse {
    pub success: bool, 
    pub error_log: Vec<String>,
}

#[derive(Serialize, Deserialize, Debug, Default)]
pub struct RemoveChatResponse {
    pub success: bool, 
    pub error_log: Vec<String>,
}

#[derive(Serialize, Deserialize, Debug, Default)]
pub struct MergeChatResponse {
    pub success: bool, 
    pub error_log: Vec<String>,
}

#[derive(Serialize, Deserialize, Debug, Default)]
pub struct SyncCodeChatPost {
    pub chat_id: String,
    pub commit_hash: Option<String>,
}

#[derive(Serialize, Deserialize, Debug, Default)]
pub struct SyncCodeChatResponse {
    pub success: bool, 
    pub error_log: Vec<String>,
}

#[derive(Serialize, Deserialize, Debug, Default)]
pub struct CreateSnapshotResponse {
    pub checkpoint: Checkpoint,
    pub error_log: Vec<String>,
}

#[derive(Serialize, Deserialize, Debug)]
pub struct CreateSnapshotPost {
    pub chat_id: String,
}

#[derive(Serialize, Deserialize, Debug)]
pub struct RestoreToBasePost {
    pub chat_id: String,
    pub base_chat_id: Option<String>, // Nếu None, sẽ restore về main branch
}

#[derive(Serialize, Deserialize, Debug, Default)]
pub struct RestoreToBaseResponse {
    pub success: bool, 
    pub error_log: Vec<String>,
}

fn serialize_datetime_utc<S: serde::Serializer>(dt: &DateTime<Utc>, serializer: S) -> Result<S::Ok, S::Error> {
    serializer.serialize_str(&dt.to_rfc3339_opts(chrono::SecondsFormat::Secs, true))
}

#[derive(Serialize, Deserialize, Debug, Default)]
pub struct WorkspaceChanges {
    #[serde(serialize_with = "serialize_path", deserialize_with = "deserialize_path")]
    pub workspace_folder: PathBuf,
    pub files_changed: Vec<FileChange>,
}

pub async fn handle_v1_git_commit(
    Extension(gcx): Extension<Arc<ARwLock<GlobalContext>>>,
    body_bytes: hyper::body::Bytes,
) -> Result<Response<Body>, ScratchError> {
    let post = serde_json::from_slice::<GitCommitPost>(&body_bytes)
        .map_err(|e| ScratchError::new(StatusCode::UNPROCESSABLE_ENTITY, format!("JSON problem: {}", e)))?;

    let mut error_log = Vec::new();
    let mut commits_applied = Vec::new();

    let abort_flag: Arc<AtomicBool> = gcx.read().await.git_operations_abort_flag.clone();
    for commit in post.commits {
        let repo_path = crate::files_correction::canonical_path(
            &commit.project_path.to_file_path().unwrap_or_default().display().to_string());

        let project_name = commit.project_path.to_file_path().ok()
            .and_then(|path| path.file_name().map(|name| name.to_string_lossy().into_owned()))
            .unwrap_or_else(|| "".to_string());

        let git_error = |msg: String| -> GitError {
            GitError {
                error_message: msg,
                project_name: project_name.clone(),
                project_path: commit.project_path.clone(),
            }
        };

        let repository = match Repository::open(&repo_path) {
            Ok(repo) => repo,
            Err(e) => { error_log.push(git_error(format!("Failed to open repo: {}", e))); continue; }
        };

        if let Err(stage_err) = stage_changes(&repository, &commit.unstaged_changes, &abort_flag) {
            error_log.push(git_error(stage_err));
            continue;
        }
        
        let (author_email, author_name) = match get_configured_author_email_and_name(&repository) {
            Ok(email_and_name) => email_and_name,
            Err(err) => { 
                error_log.push(git_error(err));
                continue; 
            }
        };
        
        let branch = match repository.head().map(|reference| git2::Branch::wrap(reference)) {
            Ok(branch) => branch,
            Err(e) => { error_log.push(git_error(format!("Failed to get current branch: {}", e))); continue; }
        };
        
        let commit_oid = match crate::git::operations::commit(&repository, &branch, &commit.commit_message, &author_name, &author_email) {
            Ok(oid) => oid,
            Err(e) => { error_log.push(git_error(e)); continue; }
        };

        commits_applied.push(serde_json::json!({
            "project_name": project_name,
            "project_path": commit.project_path.to_string(),
            "commit_oid": commit_oid.to_string(),
        }));
    }
    
    Ok(Response::builder()
        .status(StatusCode::OK)
        .header("Content-Type", "application/json")
        .body(Body::from(serde_json::to_string(&serde_json::json!({
            "commits_applied": commits_applied,
            "error_log": error_log,
        })).unwrap()))
        .unwrap())
}

pub async fn handle_v1_snapshot_create(
    Extension(gcx): Extension<Arc<ARwLock<GlobalContext>>>,
    body_bytes: hyper::body::Bytes,
) -> Result<Response<Body>, ScratchError> {
    let post = serde_json::from_slice::<CreateSnapshotPost>(&body_bytes)
        .map_err(|e| ScratchError::new(StatusCode::UNPROCESSABLE_ENTITY, format!("JSON problem: {}", e)))?;

    let chat_id = &post.chat_id;

    let response = match create_workspace_snapshot(
        gcx.clone(), 
        chat_id
    ).await {
        Ok((checkpoint, _repo)) => {
            CreateSnapshotResponse {
                checkpoint: checkpoint,
                error_log: vec![],
            }
        },
        Err(e) => {
            CreateSnapshotResponse {
                error_log: vec![e],
                ..Default::default()
            }
        }
    };

    Ok(Response::builder()
        .status(StatusCode::OK)
        .header("Content-Type", "application/json")
        .body(Body::from(serde_json::to_string(&response).unwrap()))
        .unwrap())
}

pub async fn handle_v1_checkpoints_preview(
    Extension(gcx): Extension<Arc<ARwLock<GlobalContext>>>,
    body_bytes: hyper::body::Bytes,
) -> Result<Response<Body>, ScratchError> {
    let post = serde_json::from_slice::<CheckpointsPost>(&body_bytes)
        .map_err(|e| ScratchError::new(StatusCode::UNPROCESSABLE_ENTITY, format!("JSON problem: {}", e)))?;

    if post.checkpoints.is_empty() {
        return Err(ScratchError::new(StatusCode::UNPROCESSABLE_ENTITY, "No checkpoints to restore".to_string()));
    }
    if post.checkpoints.len() > 1 {
        return Err(ScratchError::new(StatusCode::NOT_IMPLEMENTED, "Multiple checkpoints to restore not implemented yet".to_string()));
    }

    let response = match preview_changes_for_workspace_checkpoint(gcx.clone(), &post.checkpoints.first().unwrap(), &post.meta.chat_id).await {
        Ok((files_changed, reverted_to)) => {
            CheckpointsPreviewResponse {
                reverted_changes: vec![WorkspaceChanges {
                    workspace_folder: post.checkpoints.first().unwrap().workspace_folder.clone(),
                    files_changed,
                }],
                reverted_to,
                error_log: vec![],
            }
        },
        Err(e) => {
            CheckpointsPreviewResponse {
                error_log: vec![e],
                ..Default::default()
            }
        }
    };

    Ok(Response::builder()
        .status(StatusCode::OK)
        .header("Content-Type", "application/json")
        .body(Body::from(serde_json::to_string(&response).unwrap()))
        .unwrap())
}

pub async fn handle_v1_checkpoints_restore(
    Extension(gcx): Extension<Arc<ARwLock<GlobalContext>>>,
    body_bytes: hyper::body::Bytes,
) -> Result<Response<Body>, ScratchError> {
    let post = serde_json::from_slice::<CheckpointsPost>(&body_bytes)
        .map_err(|e| ScratchError::new(StatusCode::UNPROCESSABLE_ENTITY, format!("JSON problem: {}", e)))?;

    if post.checkpoints.is_empty() {
        return Err(ScratchError::new(StatusCode::UNPROCESSABLE_ENTITY, "No checkpoints to restore".to_string()));
    }
    if post.checkpoints.len() > 1 {
        return Err(ScratchError::new(StatusCode::NOT_IMPLEMENTED, "Multiple checkpoints to restore not implemented yet".to_string()));
    }

    let response = match restore_workspace_checkpoint(gcx.clone(), &post.checkpoints.first().unwrap(), &post.meta.chat_id).await {
        Ok(_) => {
            CheckpointsRestoreResponse {
                success: true,
                error_log: vec![],
            }
        },
        Err(e) => {
            CheckpointsRestoreResponse {
                error_log: vec![e],
                ..Default::default()
            }
        }
    };

    Ok(Response::builder()
        .status(StatusCode::OK)
        .header("Content-Type", "application/json")
        .body(Body::from(serde_json::to_string(&response).unwrap()))
        .unwrap())
}

pub async fn handle_v1_remove_chat(    
    Extension(gcx): Extension<Arc<ARwLock<GlobalContext>>>,
    body_bytes: hyper::body::Bytes,
) -> Result<Response<Body>, ScratchError> {

    let post = serde_json::from_slice::<RemoveChatPost>(&body_bytes)
        .map_err(|e| ScratchError::new(StatusCode::UNPROCESSABLE_ENTITY, format!("JSON problem: {}", e)))?;

    let response = match remove_chat_with_base(gcx.clone(), &post.chat_id, post.base_chat_id.as_deref()).await {
        Ok(_) => {
            RemoveChatResponse {
                success: true,
                error_log: vec![],
            }
        },
        Err(e) => {
            RemoveChatResponse {
                error_log: vec![e],
                ..Default::default()
            }
        }
    };

    Ok(Response::builder()
        .status(StatusCode::OK)
        .header("Content-Type", "application/json")
        .body(Body::from(serde_json::to_string(&response).unwrap()))
        .unwrap())
}

pub async fn handle_v1_remove_all_chat(    
    Extension(gcx): Extension<Arc<ARwLock<GlobalContext>>>,
    _body_bytes: hyper::body::Bytes,
) -> Result<Response<Body>, ScratchError> {

    let response = match remove_all_chat(gcx.clone()).await {
        Ok(_) => {
            RemoveChatResponse {
                success: true,
                error_log: vec![],
            }
        },
        Err(e) => {
            RemoveChatResponse {
                error_log: vec![e],
                ..Default::default()
            }
        }
    };

    Ok(Response::builder()
        .status(StatusCode::OK)
        .header("Content-Type", "application/json")
        .body(Body::from(serde_json::to_string(&response).unwrap()))
        .unwrap())
}

pub async fn handle_v1_checkpoints_merge(
    Extension(gcx): Extension<Arc<ARwLock<GlobalContext>>>,
    body_bytes: hyper::body::Bytes,
) -> Result<Response<Body>, ScratchError> {
    let post = serde_json::from_slice::<MergeChatPost>(&body_bytes)
        .map_err(|e| ScratchError::new(StatusCode::UNPROCESSABLE_ENTITY, format!("JSON problem: {}", e)))?;

    let chat_id = &post.chat_id;
    let response = match merge_chat(gcx.clone(), chat_id).await {
        Ok(_) => {
            MergeChatResponse {
                success: true,
                error_log: vec![],
            }
        },
        Err(e) => {
            MergeChatResponse {
                error_log: vec![e],
                ..Default::default()
            }
        }
    };

    Ok(Response::builder()
        .status(StatusCode::OK)
        .header("Content-Type", "application/json")
        .body(Body::from(serde_json::to_string(&response).unwrap()))
        .unwrap())
}

pub async fn handle_v1_checkpoint_sync_code(
    Extension(gcx): Extension<Arc<ARwLock<GlobalContext>>>,
    body_bytes: hyper::body::Bytes,
) -> Result<Response<Body>, ScratchError> {
    let post = serde_json::from_slice::<SyncCodeChatPost>(&body_bytes)
        .map_err(|e| ScratchError::new(StatusCode::UNPROCESSABLE_ENTITY, format!("JSON problem: {}", e)))?;

    let chat_id = &post.chat_id;
    let commit_hash = &post.commit_hash;

    tracing::info!("Sync code");

    // Step 1: Extract the Arc<Notify> for both vecdb and ast
    let (vecdb_notify_opt, ast_notify_opt): (Option<Arc<ANotify>>, Option<Arc<ANotify>>) = {
        let gcx_locked = gcx.read().await;

        // VecDB Notify
        let vecdb_notify = if let Some(vec_db) = gcx_locked.vec_db.lock().await.as_ref() {
            Some(vec_db.vectorizer_service.lock().await.vstatus_notify.clone())
        } else {
            None
        };

        // AST Notify
        let ast_notify = if let Some(ast_service) = gcx_locked.ast_service.as_ref() {
            Some(ast_service.lock().await.ast_sleeping_point.clone())
        } else {
            None
        };

        (vecdb_notify, ast_notify)
    };

    // Step 2: Prepare both notification futures (if present)
    let vecdb_fut = vecdb_notify_opt.map(|notify| {
        let notify_clone = notify.clone();
        async move { notify_clone.notified().await }
    });

    let ast_fut = ast_notify_opt.map(|notify| {
        let notify_clone = notify.clone();
        async move { notify_clone.notified().await }
    });

    // Step 3: Trigger sync
    let response = match commit_hash {
        Some(hash) => {
            match sync_code_by_commit(gcx.clone(), hash).await {
                Ok(_) => SyncCodeChatResponse {
                    success: true,
                    error_log: vec![],
                },
                Err(e) => SyncCodeChatResponse {
                    error_log: vec![e],
                    ..Default::default()
                },
            }
        },
        None => {
            match sync_code_by_branch(gcx.clone(), chat_id).await {
                Ok(_) => SyncCodeChatResponse {
                    success: true,
                    error_log: vec![],
                },
                Err(e) => SyncCodeChatResponse {
                    error_log: vec![e],
                    ..Default::default()
                },
            }
        }
    };

    // Step 4: Wait for both notifications if they exist
    if vecdb_fut.is_some() || ast_fut.is_some() {
        tokio::join!(
            async {
                if let Some(fut) = vecdb_fut {
                    let _ = fut.await;
                }
            },
            async {
                if let Some(fut) = ast_fut {
                    let _ = fut.await;
                }
            }
        );
    }

    tracing::info!("DONE SYNC");

    Ok(Response::builder()
        .status(StatusCode::OK)
        .header("Content-Type", "application/json")
        .body(Body::from(serde_json::to_string(&response).unwrap()))
        .unwrap())
}

pub async fn handle_v1_restore_to_base(
    Extension(gcx): Extension<Arc<ARwLock<GlobalContext>>>,
    body_bytes: hyper::body::Bytes,
) -> Result<Response<Body>, ScratchError> {
    let post = serde_json::from_slice::<RestoreToBasePost>(&body_bytes)
        .map_err(|e| ScratchError::new(StatusCode::UNPROCESSABLE_ENTITY, format!("JSON problem: {}", e)))?;

    let response = match crate::git::checkpoints::restore_workspace_to_base_state(
        gcx.clone(), 
        &post.chat_id, 
        post.base_chat_id.as_deref()
    ).await {
        Ok(_) => {
            RestoreToBaseResponse {
                success: true,
                error_log: vec![],
            }
        },
        Err(e) => {
            RestoreToBaseResponse {
                error_log: vec![e],
                ..Default::default()
            }
        }
    };

    Ok(Response::builder()
        .status(StatusCode::OK)
        .header("Content-Type", "application/json")
        .body(Body::from(serde_json::to_string(&response).unwrap()))
        .unwrap())
}

pub async fn handle_v1_sync_workspace(
    Extension(gcx): Extension<Arc<ARwLock<GlobalContext>>>,
) -> Result<Response<Body>, ScratchError> {
    let response = match sync_workspace_to_shadow_repo(gcx).await {
        Ok(_) => "Workspace changes synced to shadow repository successfully".to_string(),
        Err(e) => format!("Failed to sync workspace: {}", e),
    };

    Ok(Response::builder()
        .status(StatusCode::OK)
        .body(Body::from(response))
        .unwrap())
}
