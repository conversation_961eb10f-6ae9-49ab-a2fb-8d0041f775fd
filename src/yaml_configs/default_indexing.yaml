#
# Here you can block indexing of directories or files by Innocody Agent, similar to .gitignore file.
# You can also add external paths for indexing, useful for outside libraries.
#
# "Indexing" means adding them to Vecdb and AST, and also keeping track of the fact that they exist (try @file in chat).
# It's a good idea to block build/ dist/ and similar folders, because indexing might interfere with compilation, especially
# on Windows. But you likely don't have to do anything because blocklist has a reasonable default.
#
# You can use relative paths, that's even desirable if you are going to commit this file into your repo. Paths are relative
# to the project root, where you have your .git directory.
#
# This file can exist at:
# ~/.config/innocody/indexing.yaml
# ~/path/to/your/project/.innocody/indexing.yaml
#

blocklist:
  - "*/.*"
  - "*/target/*"
  - "*/node_modules/*"
  - "*/vendor/*"
  - "*/build/*"
  - "*/dist/*"
  - "*/bin/*"
  - "*/pkg/*"
  - "*/lib/*"
  - "*/obj/*"
  - "*/out/*"
  - "*/venv/*"
  - "*/env/*"
  - "*/tmp/*"
  - "*/temp/*"
  - "*/logs/*"
  - "*/coverage/*"
  - "*/backup/*"
  - "*/__pycache__/*"
  - "*/_trajectories/*"
  - "*/.gradle/*"

additional_indexing_dirs:
 - "~/my_favorite_library/"
