use std::sync::Arc;
use chrono::{DateTime, Utc};
use git2::{IndexAddOption, Oid, Repository, BranchType, ObjectType, Tree};
use tokio::sync::RwLock as ARwLock;
use tokio::sync::Mutex as AMutex;
use tokio::time::Instant;
use tokio::sync::oneshot;
use std::path::{Path, PathBuf};
use std::sync::atomic::{AtomicBool, Ordering};
use serde::{Serialize, Deserialize};
use tokio::task::JoinHandle;
use std::collections::HashSet;
use std::fs::{self, File};
use std::io::Write;
use std::str::FromStr;

use crate::ast::chunk_utils::official_text_hashing_function;
use crate::custom_error::MapErrToString;
use crate::files_blocklist::reload_indexing_everywhere_if_needed;
use crate::files_correction::{deserialize_path, get_active_workspace_folder, get_project_dirs, serialize_path};
use crate::global_context::GlobalContext;
use crate::git::{FileChange, FileChangeStatus, from_unix_glob_pattern_to_gitignore};
use crate::git::operations::{checkout_head_and_branch_to_commit, commit, get_commit_datetime, get_diff_statuses, get_diff_statuses_index_to_commit, get_or_create_branch_from_base, get_or_create_branch_from_main, stage_changes, open_or_init_repo, checkout_to_main_branch, get_main_branch, get_configured_author_email_and_name};
use crate::git::operations::{branch_points_to_commit, delete_local_branch, delete_all_branches, merge_branch_to_main, sync_code_branch, sync_code_commit};
use crate::git::operations::merge_branch_to_main_3way;


#[derive(Default, Serialize, Deserialize, Clone, Debug)]
pub struct Checkpoint {
    #[serde(serialize_with = "serialize_path", deserialize_with = "deserialize_path")]
    pub workspace_folder: PathBuf,
    pub commit_hash: String,
}

impl Checkpoint {
    pub fn workspace_hash(&self) -> String {
        official_text_hashing_function(&self.workspace_folder.to_string_lossy().to_string())
    }
}

async fn open_shadow_repo_and_nested_repos(
    gcx: Arc<ARwLock<GlobalContext>>, workspace_folder: &Path, allow_init_main_repo: bool,
) -> Result<(Repository, Vec<Repository>, String), String> {
    async fn open_repos(gcx: Arc<ARwLock<GlobalContext>>, paths: &[PathBuf], allow_init: bool, nested: bool, cache_dir: &Path) -> Result<Vec<Repository>, String> {
        let indexing_everywhere = reload_indexing_everywhere_if_needed(gcx).await;
        let mut result = Vec::new();
        for path in paths {
            let indexing_for_path = indexing_everywhere.indexing_for_path(path);
            let path_hash = official_text_hashing_function(&path.to_string_lossy().to_string());
            let git_dir_path = if nested {
                cache_dir.join("shadow_git").join("nested").join(&path_hash)
            } else {
                cache_dir.join("shadow_git").join(&path_hash)
            };

            tracing::info!("Opening repo for path: {}", git_dir_path.to_string_lossy());

            let repo = if allow_init {
                open_or_init_repo(&git_dir_path).map_err_to_string()
            } else {
                Repository::open(&git_dir_path).map_err_to_string()
            }?;
            repo.set_workdir(path, false).map_err_to_string()?;
            for blocklisted_rule in indexing_for_path.blocklist {
                if let Err(e) = repo.add_ignore_rule(&from_unix_glob_pattern_to_gitignore(&blocklisted_rule)) {
                    tracing::warn!("Failed to add ignore rule for {}: {}", path.to_string_lossy(), e);
                }
            }
            for additional_indexing_rule in indexing_for_path.additional_indexing_dirs {
                if let Err(e) = repo.add_ignore_rule(&format!("!{}", from_unix_glob_pattern_to_gitignore(&additional_indexing_rule))) {
                    tracing::warn!("Failed to add ignore rule for {}: {}", path.to_string_lossy(), e);
                }
            }
            result.push(repo);
        }
        Ok(result)
    }
    
    let (cache_dir, vcs_roots) = {
        let gcx_locked = gcx.read().await;
        (gcx_locked.cache_dir.clone(), gcx_locked.documents_state.workspace_vcs_roots.clone())
    };
    let nested_vcs_roots: Vec<PathBuf> = {
        let vcs_roots_locked = vcs_roots.lock().unwrap();
        vcs_roots_locked.iter()
            .filter(|vcs| vcs.starts_with(&workspace_folder) && **vcs != workspace_folder).cloned().collect()
    };
    let workspace_folder_hash = official_text_hashing_function(&workspace_folder.to_string_lossy().to_string());

    let repo = open_repos(gcx.clone(), &[workspace_folder.to_path_buf()], allow_init_main_repo, false, &cache_dir).await?
        .into_iter().next().unwrap();
    let nested_repos = open_repos(gcx.clone(), &nested_vcs_roots, true, true, &cache_dir).await?;

    Ok((repo, nested_repos, workspace_folder_hash))
}

fn get_file_changes_from_nested_repos<'a>(
    parent_repo: &'a Repository, nested_repos: &'a [Repository], include_abs_paths: bool
) -> Result<(Vec<(&'a Repository, Vec<FileChange>)>, Vec<FileChange>), String> {
    let repo_workdir = parent_repo.workdir().ok_or("Failed to get workdir.".to_string())?;
    let mut file_changes_per_repo = Vec::new();
    let mut file_changes_flatened = Vec::new();

    for nested_repo in nested_repos {
        let (_, nested_repo_changes) = get_diff_statuses(git2::StatusShow::Workdir, nested_repo, include_abs_paths)?;
        let nested_repo_workdir = nested_repo.workdir()
            .ok_or("Failed to get nested repo workdir".to_string())?;
        let nested_repo_rel_path = nested_repo_workdir.strip_prefix(repo_workdir).map_err_to_string()?;

        for change in &nested_repo_changes {
            file_changes_flatened.push(FileChange {
                relative_path: nested_repo_rel_path.join(&change.relative_path),
                absolute_path: change.absolute_path.clone(),
                status: change.status.clone(),
            });
        }
        file_changes_per_repo.push((nested_repo, nested_repo_changes));
    }

    Ok((file_changes_per_repo, file_changes_flatened))
}

/// Restore workspace về trạng thái gốc trước khi tạo chat mới
/// Chỉ được gọi khi tạo chat mới hoàn toàn, không phải khi tiếp tục chat hiện tại
/// 
/// @param force_initial_state: 
/// - true: Restore về initial commit (trạng thái ban đầu hoàn toàn) - cho "from none" thực sự
/// - false: Restore về latest commit (trạng thái mới nhất đã được merge) - cho "from none" sau merge
pub async fn restore_workspace_to_base_state_for_new_chat(
    gcx: Arc<ARwLock<GlobalContext>>,
    chat_id: &str,
    base_chat_id: Option<&str>,
    force_initial_state: bool,
) -> Result<(), String> {
    let workspace_folder = get_active_workspace_folder(gcx.clone()).await
        .ok_or_else(|| "No active workspace folder".to_string())?;

    tracing::info!("Restoring workspace to base state for new chat_id={} with base_chat_id={:?}, force_initial_state={}", 
                   chat_id, base_chat_id, force_initial_state);

    // Nếu có base_chat_id, không cần restore vì sẽ fork từ checkpoint đó
    if base_chat_id.is_some() {
        tracing::info!("base_chat_id provided, skipping workspace restore");
        return Ok(());
    }

    // Lấy checkpoint gốc từ main branch với force_initial_state
    let base_checkpoint = get_base_checkpoint_for_chat_with_force_initial(gcx.clone(), chat_id, None, force_initial_state).await?;
    
    if let Some(checkpoint) = base_checkpoint {
        // Chỉ sync code từ checkpoint gốc về workspace thực tế
        // Không gọi restore_workspace_checkpoint vì nó chỉ thay đổi Git repo
        sync_code_by_commit(gcx.clone(), &checkpoint.commit_hash).await?;
        
        let state_type = if force_initial_state { "initial" } else { "latest" };
        tracing::info!("Successfully restored workspace to {} state for new chat_id={}", state_type, chat_id);
    } else {
        tracing::warn!("No base checkpoint found for chat_id={}, workspace remains unchanged", chat_id);
    }

    Ok(())
}

/// Kiểm tra xem chat branch có tồn tại không
fn chat_branch_exists(repo: &Repository, chat_id: &str) -> bool {
    let branch_name = format!("innocody-{chat_id}");
    repo.find_branch(&branch_name, BranchType::Local).is_ok()
}

/// Tạo workspace checkpoint với force_initial_state = true (cho "from none" thực sự)
pub async fn create_workspace_checkpoint_from_none(
    gcx: Arc<ARwLock<GlobalContext>>,
    prev_checkpoint: Option<&Checkpoint>,
    base_checkpoint: Option<&Checkpoint>,
    chat_id: &str,
    base_chat_id: Option<&str>,
) -> Result<(Checkpoint, Repository), String> {
    create_workspace_checkpoint(gcx, prev_checkpoint, base_checkpoint, chat_id, base_chat_id, true).await
}

/// Tạo workspace checkpoint với force_initial_state = false (mặc định)
pub async fn create_workspace_checkpoint_default(
    gcx: Arc<ARwLock<GlobalContext>>,
    prev_checkpoint: Option<&Checkpoint>,
    base_checkpoint: Option<&Checkpoint>,
    chat_id: &str,
    base_chat_id: Option<&str>,
) -> Result<(Checkpoint, Repository), String> {
    create_workspace_checkpoint(gcx, prev_checkpoint, base_checkpoint, chat_id, base_chat_id, false).await
}

pub async fn create_workspace_checkpoint(
    gcx: Arc<ARwLock<GlobalContext>>,
    prev_checkpoint: Option<&Checkpoint>,
    base_checkpoint: Option<&Checkpoint>,
    chat_id: &str,
    base_chat_id: Option<&str>,
    force_initial_state: bool,
) -> Result<(Checkpoint, Repository), String> {
    let t0 = Instant::now();
    let abort_flag: Arc<AtomicBool> = gcx.read().await.git_operations_abort_flag.clone();
    
    let workspace_folder = get_active_workspace_folder(gcx.clone()).await
        .ok_or_else(|| "No active workspace folder".to_string())?;

    tracing::info!("Create workspace checkpoint for chat_id={} with force_initial_state={}", chat_id, force_initial_state);

    // Mở repo trước để kiểm tra branch
    let (repo, nested_repos, workspace_folder_hash) = 
        open_shadow_repo_and_nested_repos(gcx.clone(), &workspace_folder, false).await?;

    // Logic xử lý workspace restoration:
    // - Chỉ restore workspace về trạng thái gốc khi tạo chat mới "from none"
    // - Không restore khi: fork từ chat khác, tiếp tục chat hiện tại, hoặc chat đã tồn tại
    //
    // Các trường hợp cụ thể:
    // 1. Tạo chat mới "from none": base_chat_id=None, prev_checkpoint=None, branch không tồn tại → RESTORE
    // 2. Tiếp tục chat hiện tại: base_chat_id=None, prev_checkpoint=Some, branch tồn tại → KHÔNG RESTORE
    // 3. Fork từ chat khác: base_chat_id=Some, prev_checkpoint=None → KHÔNG RESTORE
    // 4. Chat đã tồn tại nhưng không có prev_checkpoint: base_chat_id=None, prev_checkpoint=None, branch tồn tại → KHÔNG RESTORE
    let should_restore_to_base = base_chat_id.is_none() && prev_checkpoint.is_none();
    
    if should_restore_to_base {
        // Kiểm tra xem branch cho chat_id này đã tồn tại chưa
        let branch_exists = chat_branch_exists(&repo, chat_id);
        
        tracing::info!("Checking if chat branch exists for chat_id={}: {}", chat_id, branch_exists);
        
        if !branch_exists {
            let state_type = if force_initial_state { "initial" } else { "latest" };
            tracing::info!("New chat from none detected (no existing branch), restoring workspace to {} state", state_type);
            if let Err(e) = restore_workspace_to_base_state_for_new_chat(gcx.clone(), chat_id, None, force_initial_state).await {
                tracing::warn!("Failed to restore workspace to base state: {}", e);
                // Continue anyway, but log the warning
            }
        } else {
            tracing::info!("Chat branch already exists, skipping workspace restore to preserve existing changes");
        }
    } else {
        tracing::info!("Not a new chat from none (base_chat_id={:?}, prev_checkpoint={:?}), skipping workspace restore", 
                      base_chat_id.is_some(), prev_checkpoint.is_some());
    }
    // Không gọi sync_workspace_to_shadow_repo ở đây vì:
    // 1. Nó có thể gây mất thay đổi nếu workspace đã được modify
    // 2. Thay đổi sẽ được commit trực tiếp trong quá trình tạo checkpoint
    // 3. Điều này đảm bảo workspace changes được preserve
    
    if let Some(prev_checkpoint) = prev_checkpoint {
        if prev_checkpoint.workspace_hash() != workspace_folder_hash {
            return Err("Can not create checkpoint for different workspace folder".to_string());
        }
    }

    if let Some(base_checkpoint) = base_checkpoint {
        if base_checkpoint.workspace_hash() != workspace_folder_hash {
            return Err("Can not create checkpoint for different workspace folder".to_string());
        }
    }

    let has_commits = repo.head().map(|head| head.target().is_some()).unwrap_or(false);
    if !has_commits {
        return Err("No commits in shadow git repo.".to_string());
    }

        let checkpoint = {
            let branch_name = format!("innocody-{chat_id}");
            // If a base commit is given, create the branch from it
            let branch = match (prev_checkpoint, base_checkpoint) {
                // 1) explicit fork: always cut from the base you specified
                (None, Some(base_cp)) => {
                    get_or_create_branch_from_base(&repo, &branch_name, base_cp)?
                }
                // 2) continue existing chat: create branch from prev_checkpoint
                (Some(prev_cp), _) => {
                    get_or_create_branch_from_base(&repo, &branch_name, prev_cp)?
                }
                // 3) new chat from none: create branch from main
                _ => {
                    get_or_create_branch_from_main(&repo, &branch_name)?
                }
            };

            repo.set_head(&format!("refs/heads/{branch_name}"))
                .map_err_with_prefix("Failed to set HEAD to new branch")?;

            // Update the working directory / index to match:
            // Chỉ checkout nếu tạo chat mới từ none (không có prev_checkpoint và base_checkpoint)
            // Nếu tiếp tục chat hoặc fork từ chat khác, giữ nguyên workspace để preserve changes
            if prev_checkpoint.is_none() && base_checkpoint.is_none() {
                repo.checkout_head(None)
                    .map_err_with_prefix("Failed to checkout new branch head")?;
            }

            let (_, mut file_changes) = get_diff_statuses(git2::StatusShow::Workdir, &repo, false)?;

        tracing::info!("File changes to be staged: {:?}", file_changes);

        let (nested_file_changes, flatened_nested_file_changes) = 
            get_file_changes_from_nested_repos(&repo, &nested_repos, false)?;
        file_changes.extend(flatened_nested_file_changes);

        // Stage và commit tất cả thay đổi trong workspace
        stage_changes(&repo, &file_changes, &abort_flag)?;
        let commit_oid = commit(&repo, &branch, &format!("CHECKPOINT {chat_id}"), "Innocody Agent", "<EMAIL>")?;
        
        // Stage changes trong nested repos nếu có
        for (nested_repo, changes) in nested_file_changes {
            stage_changes(&nested_repo, &changes, &abort_flag)?;
        }

        Checkpoint {workspace_folder, commit_hash: commit_oid.to_string()}
    };

    tracing::info!("Checkpoint created in {:.2}s", t0.elapsed().as_secs_f64());

    // Return checkpoint và repo để caller có thể sử dụng
    Ok((checkpoint, repo))
}

pub async fn create_workspace_snapshot(
    gcx: Arc<ARwLock<GlobalContext>>,
    chat_id: &str,
) -> Result<(Checkpoint, Repository), String> {
    let t0 = Instant::now();

    let abort_flag: Arc<AtomicBool> = gcx.read().await.git_operations_abort_flag.clone();
    let workspace_folder = get_active_workspace_folder(gcx.clone()).await
        .ok_or_else(|| "No active workspace folder".to_string())?;

    tracing::info!("Creating workspace snapshot for chat_id={}", chat_id);

    let (repo, nested_repos, _workspace_folder_hash) = 
        open_shadow_repo_and_nested_repos(gcx.clone(), &workspace_folder, false).await?;

    let snapshot = {
        let branch_name = format!("innocody-{chat_id}");
        // If a base commit is given, create the branch from it
        let branch = get_or_create_branch_from_main(&repo, &branch_name)?;

        repo.set_head(&format!("refs/heads/{branch_name}"))
            .map_err_with_prefix("Failed to set HEAD to new branch")?;

        // 2) Update the working directory / index to match:
        repo.checkout_head(None)
            .map_err_with_prefix("Failed to checkout new branch head")?;


        let (_, mut file_changes) = get_diff_statuses(git2::StatusShow::Workdir, &repo, false)?;

        tracing::info!("File changes to be staged: {:?}", file_changes);

        let (nested_file_changes, flatened_nested_file_changes) = 
            get_file_changes_from_nested_repos(&repo, &nested_repos, false)?;
        file_changes.extend(flatened_nested_file_changes);

        stage_changes(&repo, &file_changes, &abort_flag)?;
        let commit_oid = commit(&repo, &branch, &format!("SNAPSHOT {chat_id}"), "Innocody Agent", "<EMAIL>")?;
        
        for (nested_repo, changes) in nested_file_changes {
            stage_changes(&nested_repo, &changes, &abort_flag)?;
        }

        Checkpoint {workspace_folder, commit_hash: commit_oid.to_string()}
    };

    tracing::info!("Snapshot created in {:.2}s", t0.elapsed().as_secs_f64());

    Ok((snapshot, repo))
}

pub async fn preview_changes_for_workspace_checkpoint(
    gcx: Arc<ARwLock<GlobalContext>>, checkpoint_to_restore: &Checkpoint, chat_id: &str
) -> Result<(Vec<FileChange>, DateTime<Utc>), String> {
    let workspace_folder = get_active_workspace_folder(gcx.clone()).await
        .ok_or_else(|| "No active workspace folder".to_string())?;

    let (repo, nested_repos, workspace_folder_hash) = 
        open_shadow_repo_and_nested_repos(gcx.clone(), &workspace_folder, false).await?;

    let commit_to_restore_oid = Oid::from_str(&checkpoint_to_restore.commit_hash).map_err_to_string()?;
    let reverted_to = get_commit_datetime(&repo, &commit_to_restore_oid)?;

    let mut files_changed = get_diff_statuses_index_to_commit(&repo, &commit_to_restore_oid, true)?;

    // Invert status since we got changes in reverse order so that if it fails it does not update the workspace
    for change in &mut files_changed {
        change.status = match change.status {
            FileChangeStatus::ADDED => FileChangeStatus::DELETED,
            FileChangeStatus::DELETED => FileChangeStatus::ADDED,
            FileChangeStatus::MODIFIED => FileChangeStatus::MODIFIED,
        };
    }

    Ok((files_changed, reverted_to))
}

pub async fn restore_workspace_checkpoint(
    gcx: Arc<ARwLock<GlobalContext>>,
    checkpoint_to_restore: &Checkpoint,
    chat_id: &str
) -> Result<(), String> {
    let workspace_folder = get_active_workspace_folder(gcx.clone()).await
        .ok_or_else(|| "No active workspace folder".to_string())?;

    let (repo, nested_repos, workspace_folder_hash) = 
        open_shadow_repo_and_nested_repos(gcx.clone(), &workspace_folder, false).await?;

    if checkpoint_to_restore.workspace_hash() != workspace_folder_hash {
        return Err("Cannot restore checkpoint from a different workspace folder.".to_string());
    }

    let commit_oid = Oid::from_str(&checkpoint_to_restore.commit_hash)
        .map_err_to_string()?;

    let branch_name = format!("innocody-{}", chat_id);

    // Check if branch exists and is pointing to the same commit
    let branch_matches_checkpoint = branch_points_to_commit(&repo, &branch_name, commit_oid);

    // Perform the checkout and move HEAD
    checkout_head_and_branch_to_commit(&repo, &branch_name, &commit_oid)?;

    // Auto-remove the branch if it's exactly pointing to the restored checkpoint
    if branch_matches_checkpoint {
        delete_local_branch(&repo, &branch_name)?;
    }

    // Reset indexes in nested repos
    for nested_repo in &nested_repos {
        let reset_index_result = nested_repo.index()
            .and_then(|mut index| {
                index.add_all(["*"], IndexAddOption::DEFAULT, Some(&mut |path, _| {
                    if path.as_os_str().as_encoded_bytes().last() == Some(&b'/') && path.join(".git").exists() {
                        1
                    } else {
                        0
                    }
                }))?;
                index.write()
            });
        if let Err(e) = reset_index_result {
            let workdir = nested_repo.workdir().unwrap_or(&PathBuf::new()).to_string_lossy().to_string();
            tracing::error!("Failed to reset index for {workdir}: {e}");
        }
    }

    Ok(())
}

pub async fn init_shadow_repos_if_needed(gcx: Arc<ARwLock<GlobalContext>>) -> () {
    let init_shadow_repos_lock: Arc<AMutex<bool>> = gcx.read().await.init_shadow_repos_lock.clone();
    let _init_shadow_repos_lock = init_shadow_repos_lock.lock().await;  // wait for previous init

    let workspace_folders = get_project_dirs(gcx.clone()).await;
    let abort_flag: Arc<AtomicBool> = gcx.read().await.git_operations_abort_flag.clone();

    for workspace_folder in workspace_folders {
        let workspace_folder_str = workspace_folder.to_string_lossy().to_string();

        let (repo, nested_repos) = match open_shadow_repo_and_nested_repos(gcx.clone(), &workspace_folder, true).await {
            Ok((repo, nested_repos, _)) => (repo, nested_repos),
            Err(e) => {
                tracing::error!("Failed to open or init shadow repo for {workspace_folder_str}: {e}");
                continue;
            }
        };

        let has_commits = repo.head().map(|head| head.target().is_some()).unwrap_or(false);
        if has_commits {
            tracing::info!("Shadow git repo for {} is already initialized.", workspace_folder_str);
            continue;
        }

        let t0 = Instant::now();

        let initial_commit_result: Result<Oid, String> = (|| {
            let (_, mut file_changes) = get_diff_statuses(git2::StatusShow::Workdir, &repo, false)?;
            let (nested_file_changes, all_nested_changes) = 
                get_file_changes_from_nested_repos(&repo, &nested_repos, false)?;
            file_changes.extend(all_nested_changes);

            stage_changes(&repo, &file_changes, &abort_flag)?;
            
            let mut index = repo.index().map_err_to_string()?;
            let tree_id = index.write_tree().map_err_to_string()?;
            let tree = repo.find_tree(tree_id).map_err_to_string()?;
            let signature = git2::Signature::now("Innocody Agent", "<EMAIL>").map_err_to_string()?;
            let commit = repo.commit(Some("HEAD"), &signature, &signature, "Initial commit", &tree, &[]).map_err_to_string()?;
            
            for (nested_repo, changes) in nested_file_changes {
                stage_changes(&nested_repo, &changes, &abort_flag)?;
            }
            Ok(commit)
        })();

        match initial_commit_result {
            Ok(_) => tracing::info!("Shadow git repo for {} initialized in {:.2}s.", workspace_folder_str, t0.elapsed().as_secs_f64()),
            Err(e) => {
                tracing::error!("Initial commit for {workspace_folder_str} failed: {e}");
                continue;
            }
        }
    }
}


pub async fn enqueue_init_shadow_repos(
    _gcx: Arc<ARwLock<GlobalContext>>,
) -> JoinHandle<()> {
    let gcx_cloned = _gcx.clone();
    tokio::spawn(async move {
        init_shadow_repos_if_needed(gcx_cloned).await;
    })
}

pub async fn abort_init_shadow_repos(
    gcx: Arc<ARwLock<GlobalContext>>,
) {
    let mut gcx_locked = gcx.write().await;
    // NOTE: actually we can't abort git tasks, so we should use atomic abort_flag here
    gcx_locked.git_operations_abort_flag.store(true, Ordering::SeqCst);
    gcx_locked.init_shadow_repos_background_task_holder.abort().await;
}

pub async fn get_latest_checkpoint(
    gcx: Arc<ARwLock<GlobalContext>>,
    chat_id: &str,
) -> Result<Checkpoint, String> {
    tracing::info!("Starting get_latest_checkpoint for chat_id={}", chat_id);

    // 1. Figure out workspace folder
    let workspace_folder: PathBuf = get_active_workspace_folder(gcx.clone())
        .await
        .ok_or_else(|| {
            tracing::info!("No active workspace folder found");
            "No active workspace folder".to_string()
        })?;
    tracing::debug!("Active workspace folder: {:?}", workspace_folder);

    // 2. Compute its hash to locate the repo on disk
    let workspace_hash = official_text_hashing_function(&workspace_folder.to_string_lossy());
    tracing::debug!("Computed workspace hash: {}", workspace_hash);

    // 3. Build path to our shadow repo
    let cache_dir = gcx.read().await.cache_dir.clone();
    let git_dir_path = cache_dir.join("shadow_git").join(&workspace_hash);
    tracing::debug!("Shadow repo path: {:?}", git_dir_path);

    // 4. Open (but do not init) the main shadow repo
    let repo: Repository = Repository::open(&git_dir_path)
        .map_err_to_string()
        .map_err(|e| {
            tracing::info!("Failed to open shadow repo at {:?}: {}", git_dir_path, e);
            e
        })?;
    tracing::info!("Opened shadow repo successfully");

    // 5. Look up the branch for this chat
    let branch_name = format!("innocody-{}", chat_id);
    tracing::info!("Looking for branch '{}'", branch_name);
    let branch = repo
        .find_branch(&branch_name, BranchType::Local)
        .map_err_to_string()
        .map_err(|e| {
            tracing::info!("Branch {} not found: {}", branch_name, e);
            e
        })?;
    tracing::info!("Found branch '{}'", branch_name);

    // 6. Get the tip commit for that branch
    let commit = branch
        .get()
        .peel_to_commit()
        .map_err_to_string()
        .map_err(|e| {
            tracing::info!("Failed to peel branch head to commit: {}", e);
            e
        })?;
    let oid: Oid = commit.id();
    tracing::info!("Latest commit OID for {}: {}", branch_name, oid);

    // 7. Build and return the Checkpoint
    let checkpoint = Checkpoint {
        workspace_folder: workspace_folder.clone(),
        commit_hash: oid.to_string(),
    };
    tracing::info!("Returning checkpoint: {:?}", checkpoint);
    Ok(checkpoint)
}

/// Lấy checkpoint gốc (trước khi chat bắt đầu) với force_initial_state
/// Nếu chat có base_chat_id, lấy checkpoint từ chat đó
/// Nếu không có, lấy checkpoint từ main branch với force_initial_state
pub async fn get_base_checkpoint_for_chat_with_force_initial(
    gcx: Arc<ARwLock<GlobalContext>>,
    chat_id: &str,
    base_chat_id: Option<&str>,
    force_initial_state: bool,
) -> Result<Option<Checkpoint>, String> {
    let workspace_folder = get_active_workspace_folder(gcx.clone()).await
        .ok_or_else(|| "No active workspace folder".to_string())?;

    let (repo, _, _) = 
        open_shadow_repo_and_nested_repos(gcx.clone(), &workspace_folder, false).await?;

    // Nếu có base_chat_id, lấy checkpoint từ chat đó
    if let Some(base_id) = base_chat_id {
        match get_latest_checkpoint(gcx.clone(), base_id).await {
            Ok(checkpoint) => {
                tracing::info!("Found base checkpoint from chat {}: {:?}", base_id, checkpoint);
                Ok(Some(checkpoint))
            }
            Err(err) => {
                tracing::warn!("Failed to get base checkpoint from chat {}: {}", base_id, err);
                // Fallback to main branch
                get_main_branch_checkpoint(&repo, &workspace_folder, force_initial_state)
            }
        }
    } else {
        // Lấy checkpoint từ main branch với force_initial_state
        get_main_branch_checkpoint(&repo, &workspace_folder, force_initial_state)
    }
}

/// Lấy checkpoint gốc (trước khi chat bắt đầu)
/// Nếu chat có base_chat_id, lấy checkpoint từ chat đó
/// Nếu không có, lấy checkpoint từ main branch
pub async fn get_base_checkpoint_for_chat(
    gcx: Arc<ARwLock<GlobalContext>>,
    chat_id: &str,
    base_chat_id: Option<&str>,
) -> Result<Option<Checkpoint>, String> {
    // Gọi hàm mới với force_initial_state = false (mặc định)
    get_base_checkpoint_for_chat_with_force_initial(gcx, chat_id, base_chat_id, false).await
}

/// Lấy checkpoint từ main branch (commit cuối cùng để đảm bảo trạng thái mới nhất)
/// 
/// @param force_initial_state: 
/// - true: Lấy initial commit (trạng thái ban đầu hoàn toàn)
/// - false: Lấy latest commit (trạng thái mới nhất đã được merge)
fn get_main_branch_checkpoint(
    repo: &Repository,
    workspace_folder: &PathBuf,
    force_initial_state: bool,
) -> Result<Option<Checkpoint>, String> {
    let main_branch = match repo.find_branch("main", BranchType::Local) {
        Ok(branch) => branch,
        Err(_) => {
            // Thử với master branch
            repo.find_branch("master", BranchType::Local)
                .map_err(|e| format!("Failed to find main or master branch: {}", e))?
        }
    };

    let head_oid = main_branch.get().target()
        .ok_or("Main branch has no target commit")?;
    
    let target_commit_oid = if force_initial_state {
        // Lấy commit đầu tiên (initial commit) của main branch
        let mut current_commit = repo.find_commit(head_oid)
            .map_err(|e| format!("Failed to find commit {}: {}", head_oid, e))?;
        
        // Đi ngược lại đến commit đầu tiên
        while let Ok(parent) = current_commit.parent(0) {
            current_commit = parent;
        }
        
        current_commit.id()
    } else {
        // Lấy commit cuối cùng của main branch
        head_oid
    };

    tracing::info!("Main branch target OID (force_initial_state={}): {}", force_initial_state, target_commit_oid);

    let checkpoint = Checkpoint {
        workspace_folder: workspace_folder.clone(),
        commit_hash: target_commit_oid.to_string(),
    };

    let state_type = if force_initial_state { "initial" } else { "latest" };
    tracing::info!("Found {} main branch checkpoint: {:?}", state_type, checkpoint);
    Ok(Some(checkpoint))
}

/// Restore workspace về trạng thái gốc trước khi chat bắt đầu
pub async fn restore_workspace_to_base_state(
    gcx: Arc<ARwLock<GlobalContext>>,
    chat_id: &str,
    base_chat_id: Option<&str>,
) -> Result<(), String> {
    let workspace_folder = get_active_workspace_folder(gcx.clone()).await
        .ok_or_else(|| "No active workspace folder".to_string())?;

    tracing::info!("Restoring workspace to base state for chat_id={}", chat_id);

    // Lấy checkpoint gốc
    let base_checkpoint = get_base_checkpoint_for_chat(gcx.clone(), chat_id, base_chat_id).await?;
    
    if let Some(checkpoint) = base_checkpoint {
        // Restore workspace về trạng thái của checkpoint gốc
        restore_workspace_checkpoint(gcx.clone(), &checkpoint, chat_id).await?;
        
        // Sync code từ checkpoint về workspace thực tế
        sync_code_by_commit(gcx.clone(), &checkpoint.commit_hash).await?;
        
        tracing::info!("Successfully restored workspace to base state for chat_id={}", chat_id);
    } else {
        tracing::warn!("No base checkpoint found for chat_id={}, workspace remains unchanged", chat_id);
    }

    Ok(())
}

pub async fn remove_chat_with_base(
    gcx: Arc<ARwLock<GlobalContext>>,
    chat_id: &str,
    base_chat_id: Option<&str>,
) -> Result<(), String> {
    let workspace_folder = get_active_workspace_folder(gcx.clone()).await
        .ok_or_else(|| "No active workspace folder".to_string())?;

    tracing::info!("Remove workspace branch for chat_id={} with base_chat_id={:?}", chat_id, base_chat_id);

    let branch_name = format!("innocody-{chat_id}");
    
    let (repo, _, _) = 
        open_shadow_repo_and_nested_repos(gcx.clone(), &workspace_folder, false).await.unwrap();
    
    // Trước khi xóa branch, restore workspace về trạng thái gốc
    if let Err(e) = restore_workspace_to_base_state(gcx.clone(), chat_id, base_chat_id).await {
        tracing::error!("Failed to restore workspace to base state: {}", e);
        // Vẫn tiếp tục xóa branch ngay cả khi restore thất bại
    }
    
    delete_local_branch(&repo, &branch_name).unwrap();

    Ok(())
}

pub async fn remove_chat(
    gcx: Arc<ARwLock<GlobalContext>>,
    chat_id: &str,
) -> Result<(), String> {
    // Gọi hàm mới với base_chat_id = None
    remove_chat_with_base(gcx, chat_id, None).await
}

pub async fn remove_all_chat(
    gcx: Arc<ARwLock<GlobalContext>>,
) -> Result<(), String> {
    let workspace_folder = get_active_workspace_folder(gcx.clone()).await
        .ok_or_else(|| "No active workspace folder".to_string())?;

    let (repo, _, _) = 
        open_shadow_repo_and_nested_repos(gcx.clone(), &workspace_folder, false).await.unwrap();

    let _ = delete_all_branches(&repo);

    Ok(())
}

pub async fn merge_chat(
    gcx: Arc<ARwLock<GlobalContext>>,
    chat_id: &str,
) -> Result<(), String> {
    // Use the new merge function with workspace preservation
    merge_chat_with_workspace_preservation(gcx, chat_id).await
}

pub async fn merge_chat_with_workspace_preservation(
    gcx: Arc<ARwLock<GlobalContext>>,
    chat_id: &str,
) -> Result<(), String> {
    let workspace_folder = get_active_workspace_folder(gcx.clone()).await
        .ok_or_else(|| "No active workspace folder".to_string())?;

    tracing::info!("Merge workspace branch for chat_id={} with workspace preservation", chat_id);

    let branch_name = format!("innocody-{chat_id}");

    let (repo, _, _) = 
        open_shadow_repo_and_nested_repos(gcx.clone(), &workspace_folder, false).await?;

    // 0. Force sync workspace to shadow repo BEFORE getting changes
    tracing::info!("Force syncing workspace to shadow repo before merge");
    sync_workspace_to_shadow_repo(gcx.clone()).await?;

    // 1. Get current workspace changes AFTER syncing
    let (staged_changes, workspace_changes) = get_diff_statuses(
        git2::StatusShow::Workdir, 
        &repo, 
        true
    )?;

    tracing::info!("Found {} staged changes and {} workspace changes before merge", staged_changes.len(), workspace_changes.len());
    
    // Debug: log all workspace changes
    for (i, change) in workspace_changes.iter().enumerate() {
        tracing::info!("Workspace change {}: {:?} - {:?}", i, change.status, change.relative_path);
    }

    // 2. Merge the chat branch to main (3-way)
    merge_branch_to_main_3way(&repo, &branch_name)?;

    // 3. Apply workspace changes on top of the merged state
    if !workspace_changes.is_empty() {
        tracing::info!("Applying {} workspace changes after merge", workspace_changes.len());
        
        // Stage workspace changes
        stage_changes(&repo, &workspace_changes, &Arc::new(AtomicBool::new(false)))?;
        
        // Commit workspace changes
        let main_branch = get_main_branch(&repo)?;
        let (author_email, author_name) = get_configured_author_email_and_name(&repo)?;
        let commit_message = format!("Apply workspace changes after merging {}", chat_id);
        commit(&repo, &main_branch, &commit_message, &author_name, &author_email)?;
    } else {
        tracing::warn!("No workspace changes to apply after merge!");
    }

    // 4. Sync the final state back to workspace with workspace preservation
    sync_code_branch_with_workspace_preservation(&repo, &workspace_folder, "main", &workspace_changes)?;

    Ok(())
}

/// Sync code from branch to workspace while preserving existing workspace changes
fn sync_code_branch_with_workspace_preservation(
    repo: &Repository,
    workspace_folder: &PathBuf,
    branch_name: &str,
    workspace_changes: &[crate::git::FileChange],
) -> Result<(), String> {
    // 1. Get the commit associated with the branch
    let reference = repo
        .find_branch(branch_name, git2::BranchType::Local)
        .map_err(|e| format!("Branch '{}' not found: {}", branch_name, e))?;
    let commit = reference
        .get()
        .peel_to_commit()
        .map_err(|e| format!("Failed to get commit: {}", e))?;
    let tree = commit.tree().map_err(|e| format!("Failed to get tree: {}", e))?;

    // 2. Ensure the target folder exists
    if !workspace_folder.exists() {
        fs::create_dir_all(workspace_folder)
            .map_err(|e| format!("Failed to create workspace folder: {}", e))?;
    }

    // 3. Write files from Git tree into the workspace
    write_tree_to_path_with_preservation(repo, &tree, workspace_folder, workspace_changes)
        .map_err(|e| format!("Failed to write tree to workspace: {}", e))?;

    Ok(())
}

/// Write tree to path while preserving workspace changes
fn write_tree_to_path_with_preservation(
    repo: &Repository,
    tree: &git2::Tree,
    dest: &Path,
    workspace_changes: &[crate::git::FileChange],
) -> Result<(), git2::Error> {
    let mut allowed_paths = HashSet::new();
    
    // Create a set of files that have workspace changes
    let workspace_changed_files: HashSet<PathBuf> = workspace_changes
        .iter()
        .filter(|change| change.status == crate::git::FileChangeStatus::MODIFIED)
        .map(|change| change.relative_path.clone())
        .collect();
    
    // First, write all files from the tree EXCEPT those with workspace changes
    write_tree_recursive_with_preservation(repo, tree, dest, dest, &mut allowed_paths, &workspace_changed_files)?;
    
    // Clean up extra files/directories not in Git tree, preserving .git and workspace changes
    remove_extra(dest, &allowed_paths, dest)
        .map_err(|e| git2::Error::from_str(&format!("Cleanup failed: {}", e)))?;

    Ok(())
}

/// Write tree recursively to path while preserving workspace changes
fn write_tree_recursive_with_preservation(
    repo: &Repository,
    tree: &Tree,
    dest: &Path,
    base: &Path,
    allowed: &mut HashSet<PathBuf>,
    workspace_changed_files: &HashSet<PathBuf>,
) -> Result<(), git2::Error> {
    for entry in tree.iter() {
        let name = entry.name().unwrap_or_default();
        let full_path = dest.join(name);
        let rel_path = full_path.strip_prefix(base).unwrap().to_path_buf();

        tracing::info!(
            "Processing tree entry: name = {}, relative_path = {}",
            name,
            rel_path.display()
        );

        allowed.insert(rel_path.clone());

        match entry.kind() {
            Some(ObjectType::Blob) => {
                // Skip writing files that have workspace changes
                if workspace_changed_files.contains(&rel_path) {
                    tracing::info!("Skipping file with workspace changes: {:?}", rel_path);
                    continue;
                }
                
                let blob = repo.find_blob(entry.id())?;
                if let Some(parent) = full_path.parent() {
                    fs::create_dir_all(parent).map_err(|e| {
                        git2::Error::from_str(&format!("Failed to create parent dir: {}", e))
                    })?;
                }
                let mut file = File::create(&full_path).map_err(|e| {
                    git2::Error::from_str(&format!("Failed to create file {:?}: {}", full_path, e))
                })?;
                file.write_all(blob.content()).map_err(|e| {
                    git2::Error::from_str(&format!("Failed to write to file {:?}: {}", full_path, e))
                })?;
            }
            Some(ObjectType::Tree) => {
                fs::create_dir_all(&full_path).map_err(|e| {
                    git2::Error::from_str(&format!("Failed to create dir {:?}: {}", full_path, e))
                })?;
                let subtree = repo.find_tree(entry.id())?;
                write_tree_recursive_with_preservation(repo, &subtree, &full_path, base, allowed, workspace_changed_files)?;
            }
            _ => {
                // Skip unsupported object types (e.g., commits, tags)
            }
        }
    }
    Ok(())
}

pub async fn sync_code_by_branch(
    gcx: Arc<ARwLock<GlobalContext>>,
    chat_id: &str,
) -> Result<(), String> {
    let workspace_folder = get_active_workspace_folder(gcx.clone()).await
        .ok_or_else(|| "No active workspace folder".to_string())?;

    tracing::info!("Sync workspace branch for chat_id={}", chat_id);

    let branch_name = match chat_id {
        "main" | "master" => chat_id.to_string(),
        _ => format!("innocody-{chat_id}"),
    };

    let (repo, _, _) = 
        open_shadow_repo_and_nested_repos(gcx.clone(), &workspace_folder, false).await.unwrap();

    sync_code_branch(&repo, &workspace_folder, &branch_name).unwrap();
    Ok(())
}

pub async fn sync_code_by_commit(
    gcx: Arc<ARwLock<GlobalContext>>,
    commit_sha: &str,
) -> Result<(), String> {
    let workspace_folder = get_active_workspace_folder(gcx.clone()).await
        .ok_or_else(|| "No active workspace folder".to_string())?;

    tracing::info!("Sync workspace from commit {}", commit_sha);

    let (repo, _, _) = 
        open_shadow_repo_and_nested_repos(gcx.clone(), &workspace_folder, false).await.unwrap();

    sync_code_commit(&repo, &workspace_folder, commit_sha)?;
    Ok(())
}

pub async fn sync_workspace_to_shadow_repo(
    gcx: Arc<ARwLock<GlobalContext>>,
) -> Result<(), String> {
    let workspace_folder = get_active_workspace_folder(gcx.clone()).await
        .ok_or_else(|| "No active workspace folder".to_string())?;

    tracing::info!("Syncing workspace changes to shadow repo");

    let (repo, _, _) = 
        open_shadow_repo_and_nested_repos(gcx.clone(), &workspace_folder, false).await?;

    // 1. Checkout to main branch
    checkout_to_main_branch(&repo)?;

    // 2. Get current workspace changes
    let (_, workspace_changes) = get_diff_statuses(
        git2::StatusShow::Workdir, 
        &repo, 
        true
    )?;

    if workspace_changes.is_empty() {
        tracing::info!("No workspace changes to sync");
        return Ok(());
    }

    // 3. Stage all changes
    stage_changes(&repo, &workspace_changes, &Arc::new(AtomicBool::new(false)))?;

    // 4. Get author info
    let (author_email, author_name) = get_configured_author_email_and_name(&repo)?;

    // 5. Commit changes
    let main_branch = get_main_branch(&repo)?;
    let commit_message = format!("Sync workspace changes: {} files modified", workspace_changes.len());
    
    commit(&repo, &main_branch, &commit_message, &author_name, &author_email)?;

    tracing::info!("Successfully synced {} workspace changes to shadow repo", workspace_changes.len());

    Ok(())
}

pub async fn ensure_workspace_synced(
    gcx: Arc<ARwLock<GlobalContext>>,
) -> Result<(), String> {
    // Sync workspace changes to shadow repo before any checkpoint operations
    sync_workspace_to_shadow_repo(gcx).await
}

/// Write tree recursively to path
fn write_tree_recursive(
    repo: &Repository,
    tree: &Tree,
    dest: &Path,
    base: &Path,
    allowed: &mut HashSet<PathBuf>,
) -> Result<(), git2::Error> {
    for entry in tree.iter() {
        let name = entry.name().unwrap_or_default();
        let full_path = dest.join(name);
        let rel_path = full_path.strip_prefix(base).unwrap().to_path_buf();

        tracing::info!(
            "Processing tree entry: name = {}, relative_path = {}",
            name,
            rel_path.display()
        );

        allowed.insert(rel_path.clone());

        match entry.kind() {
            Some(ObjectType::Blob) => {
                let blob = repo.find_blob(entry.id())?;
                if let Some(parent) = full_path.parent() {
                    fs::create_dir_all(parent).map_err(|e| {
                        git2::Error::from_str(&format!("Failed to create parent dir: {}", e))
                    })?;
                }
                let mut file = File::create(&full_path).map_err(|e| {
                    git2::Error::from_str(&format!("Failed to create file {:?}: {}", full_path, e))
                })?;
                file.write_all(blob.content()).map_err(|e| {
                    git2::Error::from_str(&format!("Failed to write to file {:?}: {}", full_path, e))
                })?;
            }
            Some(ObjectType::Tree) => {
                fs::create_dir_all(&full_path).map_err(|e| {
                    git2::Error::from_str(&format!("Failed to create dir {:?}: {}", full_path, e))
                })?;
                let subtree = repo.find_tree(entry.id())?;
                write_tree_recursive(repo, &subtree, &full_path, base, allowed)?;
            }
            _ => {
                // Skip unsupported object types (e.g., commits, tags)
            }
        }
    }
    Ok(())
}

/// Remove extra files not in the allowed set
fn remove_extra(dest: &Path, allowed: &HashSet<PathBuf>, base: &Path) -> std::io::Result<()> {
    for entry in fs::read_dir(dest)? {
        let entry = entry?;
        let path = entry.path();
        let rel_path = path.strip_prefix(base).unwrap();

        if rel_path == Path::new(".git") {
            continue; // Never touch .git directory
        }

        if !allowed.contains(rel_path) {
            if path.is_dir() {
                fs::remove_dir_all(&path)?;
            } else {
                fs::remove_file(&path)?;
            }
        } else if path.is_dir() {
            remove_extra(&path, allowed, base)?;
        }
    }
    Ok(())
}
