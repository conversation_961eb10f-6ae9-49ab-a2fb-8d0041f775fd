use std::path::{Path, PathBuf};
use chrono::{DateTime, TimeZone, Utc};
use git2::{Branch, BranchType, DiffOptions, Oid, Repository, Tree, ObjectType};
use tracing::error;
use url::Url;
use crate::custom_error::MapErrToString;
use crate::files_correction::canonical_path;
use crate::git::{FileChange, FileChangeStatus};
use std::sync::Arc;
use std::sync::atomic::{AtomicBool, Ordering};
use std::fs::{self, File};
use std::io::Write;
use crate::git::checkpoints::Checkpoint;
use std::collections::HashSet;

fn status_options(include_unmodified: bool, show: git2::StatusShow) -> git2::StatusOptions {
    let mut options = git2::StatusOptions::new();
    options
        .disable_pathspec_match(true)
        .include_ignored(false)
        .include_unmodified(include_unmodified)
        .include_unreadable(false)
        .include_untracked(true)
        .recurse_ignored_dirs(false)
        .recurse_untracked_dirs(true)
        .rename_threshold(100)
        .update_index(true)
        .show(show);
    options
}

#[allow(dead_code)]
pub fn get_git_remotes(repository_path: &Path) -> Result<Vec<(String, String)>, String> {
    let repository = Repository::discover(repository_path)
        .map_err(|e| format!("Failed to open repository: {}", e))?;
    let remotes = repository.remotes()
        .map_err(|e| format!("Failed to get remotes: {}", e))?;
    let mut result = Vec::new();
    for name in remotes.iter().flatten() {
        if let Ok(remote) = repository.find_remote(name) {
            if let Some(url) = remote.url() {
                if let Ok(mut parsed_url) = Url::parse(url) {
                    parsed_url.set_username("").ok();
                    parsed_url.set_password(None).ok();
                    result.push((name.to_string(), parsed_url.to_string()));
                } else {
                    result.push((name.to_string(), url.to_string()));
                }                
            }
        }
    }
    Ok(result)
}

pub fn git_ls_files(repository_path: &PathBuf) -> Option<Vec<PathBuf>> {
    let repository = Repository::open(repository_path)
        .map_err(|e| error!("Failed to open repository: {}", e)).ok()?;

    let statuses = repository.statuses(Some(
        &mut status_options(true, git2::StatusShow::IndexAndWorkdir)))
        .map_err(|e| error!("Failed to get statuses: {}", e)).ok()?;

    let mut files = Vec::new();
    for entry in statuses.iter() {
        let path = String::from_utf8_lossy(entry.path_bytes()).to_string();
        files.push(repository_path.join(path));
    }
    if !files.is_empty() { Some(files) } else { None }
}

pub fn get_main_branch<'repo>(repo: &'repo Repository) -> Result<Branch<'repo>, String> {
    repo.find_branch("main", BranchType::Local).map_err(|e| e.to_string())
        .or_else(|_| repo.find_branch("master", BranchType::Local).map_err(|e| e.to_string()))
        .map_err(|e| format!("Failed to find 'main' or 'master' branch: {}", e))
}

pub fn checkout_to_main_branch(repo: &Repository) -> Result<String, String> {
    let main_branch = get_main_branch(repo)?;

    let main_ref = main_branch.get().name()
        .ok_or_else(|| "Main branch has no valid reference name".to_string())?;

    // Set HEAD to the main branch reference
    repo.set_head(main_ref)
        .map_err(|e| format!("Failed to set HEAD to '{}': {}", main_ref, e))?;

    // Checkout the working directory to match the HEAD
    repo.checkout_head(None)
        .map_err(|e| format!("Failed to checkout working directory to '{}': {}", main_ref, e))?;

    // Get the branch name (shorthand like "main" or "master")
    let branch_name = main_branch.name()
        .map_err(|e| format!("Failed to get branch name: {}", e))?
        .ok_or_else(|| "Main branch has no valid UTF-8 name".to_string())?;

    tracing::info!("Checked out to main branch '{}'", branch_name);

    Ok(branch_name.to_string())
}

pub fn get_or_create_branch_from_main<'repo>(
    repo: &'repo Repository,
    branch_name: &str,
) -> Result<Branch<'repo>, String> {
    // 1) If branch already exists, just return it.
    if let Ok(existing_branch) = repo.find_branch(branch_name, BranchType::Local) {
        return Ok(existing_branch);
    }

    // 2) Find the "main" or fallback to "master"
    let main_branch = get_main_branch(repo)?;
    let main_commit = main_branch
        .get()
        .peel_to_commit()
        .map_err(|e| format!("Failed to peel main/master to commit: {}", e))?;

    // 3) Create the new branch at main's tip
    let new_branch = repo
        .branch(branch_name, &main_commit, false)
        .map_err(|e| format!("Failed to create branch {}: {}", branch_name, e))?;

    Ok(new_branch)
}

/// Forks a new branch from a base checkpoint, ensuring divergent commit history.
pub fn get_or_create_branch_from_base<'repo>(
    repo: &'repo Repository,
    branch_name: &str,
    base_checkpoint: &Checkpoint,
) -> Result<Branch<'repo>, String> {
    if let Ok(existing_branch) = repo.find_branch(branch_name, BranchType::Local) {
        return Ok(existing_branch);
    }

    let base_oid = Oid::from_str(&base_checkpoint.commit_hash)
        .map_err(|e| format!("Invalid commit hash {}: {}", base_checkpoint.commit_hash, e))?;
    let base_commit = repo.find_commit(base_oid)
        .map_err(|e| format!("Base commit {} not found: {}", base_oid, e))?;

    let branch = repo.branch(branch_name, &base_commit, false)
        .map_err(|e| format!("Failed to create fork branch {}: {}", branch_name, e))?;

    Ok(branch)
}



fn is_changed_in_wt(status: git2::Status) -> bool {
    status.intersects(git2::Status::WT_NEW | 
        git2::Status::WT_MODIFIED | 
        git2::Status::WT_DELETED | 
        git2::Status::WT_RENAMED | 
        git2::Status::WT_TYPECHANGE)
}

fn is_changed_in_index(status: git2::Status) -> bool {
    status.intersects(git2::Status::INDEX_NEW | 
        git2::Status::INDEX_MODIFIED | 
        git2::Status::INDEX_DELETED | 
        git2::Status::INDEX_RENAMED | 
        git2::Status::INDEX_TYPECHANGE)
}

/// Returns (staged_changes, unstaged_changes), note that one of them may be always empty based on show_opt
/// 
/// If include_abs_path is true, they are included in the FileChanges result, use it if they need to be 
/// returned to the client or the absolute paths are needed
pub fn get_diff_statuses(show_opt: git2::StatusShow, repo: &Repository, include_abs_paths: bool) -> Result<(Vec<FileChange>, Vec<FileChange>), String> {
    let repo_workdir = repo.workdir()
        .ok_or("Failed to get workdir from repository".to_string())?;

    let mut staged_changes = Vec::new();
    let mut unstaged_changes = Vec::new();
    let statuses = repo.statuses(Some(&mut status_options(false, show_opt)))
        .map_err_with_prefix("Failed to get statuses:")?;
    
    for entry in statuses.iter() {
        let status = entry.status();
        let relative_path = PathBuf::from(String::from_utf8_lossy(entry.path_bytes()).to_string());
        
        if entry.path_bytes().last() == Some(&b'/') && repo_workdir.join(&relative_path).join(".git").exists() {
            continue;
        }

        let should_not_be_present = match show_opt {
            git2::StatusShow::Index => is_changed_in_wt(status) || status.is_index_renamed(),
            git2::StatusShow::Workdir => is_changed_in_index(status) || status.is_wt_renamed(),
            git2::StatusShow::IndexAndWorkdir => status.is_index_renamed() || status.is_wt_renamed(),
        };
        if should_not_be_present {
            tracing::error!("File status is {:?} for file {:?}, which should not be present due to status options.", status, relative_path);
            continue;
        }

        let absolute_path = if include_abs_paths && (is_changed_in_index(status) || is_changed_in_wt(status)) { 
            canonical_path(repo_workdir.join(&relative_path).to_string_lossy())
        } else {
            PathBuf::new()
        };

        if is_changed_in_index(status) {
            staged_changes.push(FileChange {
                status: match status {
                    s if s.is_index_new() => FileChangeStatus::ADDED,
                    s if s.is_index_deleted() => FileChangeStatus::DELETED,
                    _ => FileChangeStatus::MODIFIED,
                },
                absolute_path: absolute_path.clone(),
                relative_path: relative_path.clone(),
            });
        }

        if is_changed_in_wt(status) {
            unstaged_changes.push(FileChange {
                status: match status {
                    s if s.is_wt_new() => FileChangeStatus::ADDED,
                    s if s.is_wt_deleted() => FileChangeStatus::DELETED,
                    _ => FileChangeStatus::MODIFIED,
                },
                absolute_path,
                relative_path,
            });
        }
    }

    Ok((staged_changes, unstaged_changes))
}

pub fn get_diff_statuses_index_to_commit(repository: &Repository, commit_oid: &git2::Oid, include_abs_paths: bool) -> Result<Vec<FileChange>, String> {
    let head = repository.head().map_err_with_prefix("Failed to get HEAD:")?;
    let original_head_ref = head.is_branch().then(|| head.name().map(ToString::to_string)).flatten();
    let original_head_oid = head.target();

    repository.set_head_detached(commit_oid.clone()).map_err_with_prefix("Failed to set HEAD:")?;

    let result = get_diff_statuses(git2::StatusShow::Index, repository, include_abs_paths);

    let restore_result = match (&original_head_ref, original_head_oid) {
        (Some(head_ref), _) => repository.set_head(head_ref),
        (None, Some(oid)) => repository.set_head_detached(oid),
        (None, None) => Ok(()),
    };

    if let Err(restore_err) = restore_result {
        let prev_err = result.as_ref().err().cloned().unwrap_or_default();
        return Err(format!("{}\nFailed to restore head: {}", prev_err, restore_err));
    }

    result.map(|(staged_changes, _unstaged_changes)| staged_changes)
}

pub fn stage_changes(repository: &Repository, file_changes: &Vec<FileChange>, abort_flag: &Arc<AtomicBool>) -> Result<(), String> {
    let mut index = repository.index().map_err_with_prefix("Failed to get index:")?;

    for file_change in file_changes {
        // NOTE: this loop can take a lot of time (25s for linux) when we just init the repo
        if abort_flag.load(Ordering::SeqCst) {
            return Err("stage_changes aborted".to_string());
        }
        match file_change.status {
            FileChangeStatus::ADDED | FileChangeStatus::MODIFIED => {
                index.add_path(&file_change.relative_path)
                    .map_err_with_prefix("Failed to add file to index:")?;
            },
            FileChangeStatus::DELETED => {
                index.remove_path(&file_change.relative_path)
                    .map_err_with_prefix("Failed to remove file from index:")?;
            },
        }
    }

    index.write().map_err_with_prefix("Failed to write index:")?;
    Ok(())
}

pub fn get_configured_author_email_and_name(repository: &Repository) -> Result<(String, String), String> {
    let config = repository.config()
        .map_err_with_prefix("Failed to get repository config:")?;
    let author_email = config.get_string("user.email")
        .map_err_with_prefix("Failed to get author email:")?;
    let author_name = config.get_string("user.name")
        .map_err_with_prefix("Failed to get author name:")?;
    Ok((author_email, author_name))
}

pub fn commit(repository: &Repository, branch: &Branch, message: &str, author_name: &str, author_email: &str) -> Result<Oid, String> {
    let mut index = repository.index().map_err_with_prefix("Failed to get index:")?;
    let tree_id = index.write_tree().map_err_with_prefix("Failed to write tree:")?;
    let tree = repository.find_tree(tree_id).map_err_with_prefix("Failed to find tree:")?;

    let signature = git2::Signature::now(author_name, author_email)
        .map_err_with_prefix("Failed to create signature:")?;
    let branch_ref_name = branch.get().name().ok_or("Invalid branch name".to_string())?;

    let parent_commit = if let Some(target) = branch.get().target() {
        repository.find_commit(target)
            .map_err(|e| format!("Failed to find branch commit: {}", e))?
    } else {
        return Err("No parent commits found".to_string());
    };

    let commit = repository.commit(
        Some(branch_ref_name), &signature, &signature, message, &tree, &[&parent_commit]
    ).map_err(|e| format!("Failed to create commit: {}", e))?;

    repository.set_head(branch_ref_name).map_err_with_prefix("Failed to set branch as head:")?;

    Ok(commit)
}

pub fn open_or_init_repo(path: &Path) -> Result<Repository, String> {
    match Repository::open(path) {
        Ok(repo) => Ok(repo),
        Err(e) if e.code() == git2::ErrorCode::NotFound => {
            Repository::init(path).map_err_to_string()
        },
        Err(e) => Err(e.to_string()),
    }
}

pub fn get_commit_datetime(repository: &Repository, commit_oid: &Oid) -> Result<DateTime<Utc>, String> {
    let commit = repository.find_commit(commit_oid.clone()).map_err_to_string()?;

    Utc.timestamp_opt(commit.time().seconds(), 0).single()
        .ok_or_else(|| "Failed to get commit datetime".to_string())
}

pub fn git_diff_head_to_workdir<'repo>(repository: &'repo Repository) -> Result<git2::Diff<'repo>, String> {
    let mut diff_options = DiffOptions::new();
    diff_options.include_untracked(true);
    diff_options.recurse_untracked_dirs(true);

    let head = repository.head().and_then(|head_ref| head_ref.peel_to_tree())
        .map_err(|e| format!("Failed to get HEAD tree: {}", e))?;

    let diff = repository.diff_tree_to_workdir(Some(&head), Some(&mut diff_options))
        .map_err(|e| format!("Failed to generate diff: {}", e))?;
    
    Ok(diff)
}

pub fn git_diff_head_to_workdir_as_string(repository: &Repository, max_size: usize) -> Result<String, String> {
    let diff = git_diff_head_to_workdir(repository)?;

    let mut diff_str = String::new();
    diff.print(git2::DiffFormat::Patch, |_, _, line| {
        let line_content = std::str::from_utf8(line.content()).unwrap_or("");
        if diff_str.len() + line_content.len() < max_size {
            diff_str.push(line.origin());
            diff_str.push_str(line_content);
            if diff_str.len() > max_size {
                diff_str.truncate(max_size - 4);
                diff_str.push_str("...\n");
            }
        }
        true
    }).map_err(|e| format!("Failed to print diff: {}", e))?;

    Ok(diff_str)
}

pub fn checkout_head_and_branch_to_commit(repo: &Repository, branch_name: &str, commit_oid: &Oid) -> Result<(), String> {
    let commit = repo.find_commit(commit_oid.clone()).map_err_with_prefix("Failed to find commit:")?;

    let mut branch_ref = repo.find_branch(branch_name, git2::BranchType::Local)
        .map_err_with_prefix("Failed to get branch:")?.into_reference();
    branch_ref.set_target(commit.id(),"Restoring checkpoint")
        .map_err_with_prefix("Failed to update branch reference:")?;

    repo.set_head(&format!("refs/heads/{}", branch_name))
        .map_err_with_prefix("Failed to set HEAD:")?;

    let mut checkout_opts = git2::build::CheckoutBuilder::new();
    checkout_opts.force().update_index(true);
    repo.checkout_head(Some(&mut checkout_opts)).map_err_with_prefix("Failed to checkout HEAD:")?;

    Ok(())
}

pub fn branch_points_to_commit(repo: &Repository, branch_name: &str, commit_oid: Oid) -> bool {
    match repo.find_branch(branch_name, BranchType::Local) {
        Ok(branch) => branch.get().target() == Some(commit_oid),
        Err(_) => false,
    }
}

pub fn delete_local_branch(repo: &Repository, branch_name: &str) -> Result<(), String> {
    tracing::info!("Start deleting branch '{}'", branch_name);

    checkout_to_main_branch(repo)?;

    // Now safe to delete the branch
    match repo.find_branch(branch_name, BranchType::Local) {
        Ok(mut branch) => {
            branch.delete()
                .map_err(|e| format!("Failed to delete branch '{}': {}", branch_name, e))?;
            tracing::info!("Deleted branch '{}'", branch_name);
        },
        Err(e) => {
            tracing::info!("Failed to find branch '{}': {}", branch_name, e);
        },
    }

    Ok(())
}


pub fn merge_branch_to_main(repo: &Repository, branch_name: &str) -> Result<(), String> {
    let main_branch = get_main_branch(repo)?;
    let main_refname = main_branch.get().name().ok_or("Invalid main branch name")?;

    // Find the branch to merge
    let branch = repo.find_branch(branch_name, BranchType::Local)
        .map_err(|e| format!("Branch {} not found: {}", branch_name, e))?;
    let branch_oid = branch.get().target().ok_or("Branch has no target commit")?;

    // Check if we can do fast-forward merge
    let main_oid = main_branch.get().target().ok_or("Main branch has no target commit")?;
    
    // If main is ahead of branch, we need to preserve workspace changes
    let is_main_ahead = repo.graph_descendant_of(main_oid, branch_oid)
        .map_err(|e| format!("Failed to check graph relationship: {}", e))?;
    
    if is_main_ahead {
        // Main is ahead, workspace changes should be preserved
        tracing::info!("Main branch is ahead of {} branch, preserving workspace changes", branch_name);
        
        // Just update the branch pointer but don't overwrite workspace
        let mut main_ref = repo.find_reference(main_refname)
            .map_err(|e| format!("Failed to find main ref {}: {}", main_refname, e))?;
        main_ref.set_target(branch_oid, &format!("Merge {} into main (preserving workspace)", branch_name))
            .map_err(|e| format!("Failed to set target on main: {}", e))?;
    } else {
        // Standard fast-forward merge
    let mut main_ref = repo.find_reference(main_refname)
        .map_err(|e| format!("Failed to find main ref {}: {}", main_refname, e))?;
    main_ref.set_target(branch_oid, &format!("Fast-forward merge {} into main", branch_name))
        .map_err(|e| format!("Failed to set target on main: {}", e))?;

    // Set HEAD to main and checkout the updated state
    repo.set_head(main_refname)
        .map_err_with_prefix("Failed to set HEAD to main")?;

        let mut checkout = git2::build::CheckoutBuilder::new();
        checkout.force().update_index(true);
        repo.checkout_head(Some(&mut checkout))
            .map_err_with_prefix("Failed to checkout HEAD after merge")?;
    }

    Ok(())
}

pub fn merge_branch_to_main_3way(repo: &Repository, branch_name: &str) -> Result<(), String> {
    let main_branch = get_main_branch(repo)?;
    let main_refname = main_branch.get().name().ok_or("Invalid main branch name")?;
    let main_oid = main_branch.get().target().ok_or("Main branch has no target commit")?;

    // Find the branch to merge
    let branch = repo.find_branch(branch_name, BranchType::Local)
        .map_err(|e| format!("Branch {} not found: {}", branch_name, e))?;
    let branch_oid = branch.get().target().ok_or("Branch has no target commit")?;

    // Find merge base
    let merge_base = repo.merge_base(main_oid, branch_oid)
        .map_err(|e| format!("Failed to find merge base: {}", e))?;
    let ancestor = repo.find_commit(merge_base)
        .map_err(|e| format!("Failed to find ancestor commit: {}", e))?;
    let main_commit = repo.find_commit(main_oid)
        .map_err(|e| format!("Failed to find main commit: {}", e))?;
    let branch_commit = repo.find_commit(branch_oid)
        .map_err(|e| format!("Failed to find branch commit: {}", e))?;

    let ancestor_tree = ancestor.tree().map_err(|e| format!("Failed to get ancestor tree: {}", e))?;
    let main_tree = main_commit.tree().map_err(|e| format!("Failed to get main tree: {}", e))?;
    let branch_tree = branch_commit.tree().map_err(|e| format!("Failed to get branch tree: {}", e))?;

    // Do the merge
    let mut idx = repo.merge_trees(&ancestor_tree, &main_tree, &branch_tree, None)
        .map_err(|e| format!("Failed to merge trees: {}", e))?;

    if idx.has_conflicts() {
        return Err("Merge conflict detected! Please resolve manually.".to_string());
    }

    // Write the merged tree
    let merged_tree_oid = idx.write_tree_to(repo)
        .map_err(|e| format!("Failed to write merged tree: {}", e))?;
    let merged_tree = repo.find_tree(merged_tree_oid)
        .map_err(|e| format!("Failed to find merged tree: {}", e))?;

    // Create merge commit
    let sig = repo.signature().map_err(|e| format!("Failed to get signature: {}", e))?;
    let parents = vec![&main_commit, &branch_commit];
    repo.commit(Some(main_refname), &sig, &sig, &format!("Merge {} into main (3-way)", branch_name), &merged_tree, &parents)
        .map_err(|e| format!("Failed to create merge commit: {}", e))?;

    // Checkout merged state
    repo.set_head(main_refname)
        .map_err_with_prefix("Failed to set HEAD to main after merge")?;
    let mut checkout = git2::build::CheckoutBuilder::new();
    checkout.force().update_index(true);
    repo.checkout_head(Some(&mut checkout))
        .map_err_with_prefix("Failed to checkout HEAD after merge")?;

    Ok(())
}

pub fn delete_all_branches(repo: &Repository) -> Result<(), String> {
    let main_name = checkout_to_main_branch(repo)?;

    // Now safe to delete other branches
    for branch in repo
        .branches(Some(BranchType::Local))
        .map_err_with_prefix("Failed to list local branches")?
    {
        let (mut branch, _) = branch.map_err_to_string()?;
        let name = branch
            .name()
            .map_err_to_string()?
            .unwrap_or_default()
            .to_string();

        if name == main_name {
            continue; // Don't delete the current/main branch
        }

        branch
            .delete()
            .map_err(|e| format!("Failed to delete branch '{}': {}", name, e))?;
    }

    Ok(())
}


pub fn sync_code_branch(
    repo: &Repository,
    workspace_folder: &PathBuf,
    branch_name: &str
) -> Result<(), String> {
    // 1. Get the commit associated with the branch
    let reference = repo
        .find_branch(branch_name, git2::BranchType::Local)
        .map_err(|e| format!("Branch '{}' not found: {}", branch_name, e))?;
    let commit = reference
        .get()
        .peel_to_commit()
        .map_err(|e| format!("Failed to get commit: {}", e))?;
    let tree = commit.tree().map_err(|e| format!("Failed to get tree: {}", e))?;

    // 2. Ensure the target folder exists
    if !workspace_folder.exists() {
        fs::create_dir_all(workspace_folder)
            .map_err(|e| format!("Failed to create workspace folder: {}", e))?;
    }

    // 3. Write files from Git tree into the workspace
    write_tree_to_path(repo, &tree, workspace_folder)
        .map_err(|e| format!("Failed to write tree to workspace: {}", e))?;

    Ok(())
}

pub fn sync_code_commit(
    repo: &Repository,
    workspace_folder: &PathBuf,
    commit_sha: &str
) -> Result<(), String> {
    // Parse the SHA into an Oid
    let oid = Oid::from_str(commit_sha)
        .map_err(|e| format!("Invalid commit SHA '{}': {}", commit_sha, e))?;

    // Find the commit object
    let commit = repo
        .find_commit(oid)
        .map_err(|e| format!("Commit '{}' not found: {}", commit_sha, e))?;

    let tree = commit
        .tree()
        .map_err(|e| format!("Failed to get tree from commit '{}': {}", commit_sha, e))?;

    // Ensure workspace folder exists
    if !workspace_folder.exists() {
        std::fs::create_dir_all(workspace_folder)
            .map_err(|e| format!("Failed to create workspace folder: {}", e))?;
    }

    // Write the tree to the workspace
    write_tree_to_path(repo, &tree, workspace_folder)
        .map_err(|e| format!("Failed to write tree to workspace: {}", e))?;

    Ok(())
}

pub fn write_tree_to_path(
    repo: &Repository,
    tree: &Tree,
    dest: &Path,
) -> Result<(), git2::Error> {
    let mut allowed_paths = HashSet::new();
    write_tree_recursive(repo, tree, dest, dest, &mut allowed_paths)?;

    // Clean up extra files/directories not in Git tree, preserving .git
    remove_extra(dest, &allowed_paths, dest)
        .map_err(|e| git2::Error::from_str(&format!("Cleanup failed: {}", e)))?;

    Ok(())
}

fn write_tree_recursive(
    repo: &Repository,
    tree: &Tree,
    dest: &Path,
    base: &Path,
    allowed: &mut HashSet<PathBuf>,
) -> Result<(), git2::Error> {
    for entry in tree.iter() {
        let name = entry.name().unwrap_or_default();
        let full_path = dest.join(name);
        let rel_path = full_path.strip_prefix(base).unwrap().to_path_buf();

        tracing::info!(
            "Processing tree entry: name = {}, relative_path = {}",
            name,
            rel_path.display()
        );


        allowed.insert(rel_path.clone());

        match entry.kind() {
            Some(ObjectType::Blob) => {
                let blob = repo.find_blob(entry.id())?;
                if let Some(parent) = full_path.parent() {
                    fs::create_dir_all(parent).map_err(|e| {
                        git2::Error::from_str(&format!("Failed to create parent dir: {}", e))
                    })?;
                }
                let mut file = File::create(&full_path).map_err(|e| {
                    git2::Error::from_str(&format!("Failed to create file {:?}: {}", full_path, e))
                })?;
                file.write_all(blob.content()).map_err(|e| {
                    git2::Error::from_str(&format!("Failed to write to file {:?}: {}", full_path, e))
                })?;
            }
            Some(ObjectType::Tree) => {
                fs::create_dir_all(&full_path).map_err(|e| {
                    git2::Error::from_str(&format!("Failed to create dir {:?}: {}", full_path, e))
                })?;
                let subtree = repo.find_tree(entry.id())?;
                write_tree_recursive(repo, &subtree, &full_path, base, allowed)?;
            }
            _ => {
                // Skip unsupported object types (e.g., commits, tags)
            }
        }
    }
    Ok(())
}

fn remove_extra(dest: &Path, allowed: &HashSet<PathBuf>, base: &Path) -> std::io::Result<()> {
    for entry in fs::read_dir(dest)? {
        let entry = entry?;
        let path = entry.path();
        let rel_path = path.strip_prefix(base).unwrap();

        if rel_path == Path::new(".git") {
            continue; // Never touch .git directory
        }

        if !allowed.contains(rel_path) {
            if path.is_dir() {
                fs::remove_dir_all(&path)?;
            } else {
                fs::remove_file(&path)?;
            }
        } else if path.is_dir() {
            remove_extra(&path, allowed, base)?;
        }
    }
    Ok(())
}