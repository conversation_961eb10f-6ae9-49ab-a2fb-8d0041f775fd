<!DOCTYPE html>
<!-- saved from url=(0130)https://gitlab-v2.innotech-vn.com/the-ai-team/innocody/innocody-engine/-/commit/84acef7360827487fbc4ee5895d38b2c8c04a0c5/pipelines -->
<html class="gl-dark ui-neutral with-top-bar " lang="en" style="--broadcast-message-height: 0px;"><head prefix="og: http://ogp.me/ns#"><meta http-equiv="Content-Type" content="text/html; charset=UTF-8">

<meta content="IE=edge" http-equiv="X-UA-Compatible">
<meta content="width=device-width, initial-scale=1" name="viewport">
<title>Pipelines · feat: html parser enhance init (84acef73) · Commits · The AI Team / innocody / innocody-engine · GitLab</title>
<script>
//<![CDATA[
window.gon={};gon.api_version="v4";gon.default_avatar_url="https://gitlab-v2.innotech-vn.com/assets/no_avatar-849f9c04a3a0d0cea2424ae97b27447dc64a7dbfae83c036c45b403392f0e8ba.png";gon.max_file_size=100;gon.asset_host=null;gon.webpack_public_path="/assets/webpack/";gon.relative_url_root="";gon.user_color_mode="gl-dark";gon.user_color_scheme="white";gon.markdown_surround_selection=true;gon.markdown_automatic_lists=true;gon.math_rendering_limits_enabled=true;gon.recaptcha_api_server_url="https://www.recaptcha.net/recaptcha/api.js";gon.recaptcha_sitekey=null;gon.gitlab_url="https://gitlab-v2.innotech-vn.com";gon.promo_url="https://about.gitlab.com";gon.forum_url="https://forum.gitlab.com";gon.docs_url="https://docs.gitlab.com";gon.revision="8a1c2c14173";gon.feature_category="source_code_management";gon.gitlab_logo="/assets/gitlab_logo-2957169c8ef64c58616a1ac3f4fc626e8a35ce4eb3ed31bb0d873712f2a041a0.png";gon.secure=true;gon.sprite_icons="/assets/icons-ca86b3ebff8cbe14f7728864eabad153d00b66986018226fe439015884de11c2.svg";gon.sprite_file_icons="/assets/file_icons/file_icons-88a95467170997d6a4052c781684c8250847147987090747773c1ee27c513c5f.svg";gon.emoji_sprites_css_path="/assets/emoji_sprites-bd26211944b9d072037ec97cb138f1a52cd03ef185cd38b8d1fcc963245199a1.css";gon.emoji_backend_version=4;gon.gridstack_css_path="/assets/lazy_bundles/gridstack-4cd1da7c8adb8553e78a4f5545a8ab57a46258e091e6ac0382e6de79bca5ea3c.css";gon.test_env=false;gon.disable_animations=false;gon.suggested_label_colors={"#cc338b":"Magenta-pink","#dc143c":"Crimson","#c21e56":"Rose red","#cd5b45":"Dark coral","#ed9121":"Carrot orange","#eee600":"Titanium yellow","#009966":"Green-cyan","#8fbc8f":"Dark sea green","#6699cc":"Blue-gray","#e6e6fa":"Lavender","#9400d3":"Dark violet","#330066":"Deep violet","#36454f":"Charcoal grey","#808080":"Gray"};gon.first_day_of_week=0;gon.time_display_relative=true;gon.time_display_format=0;gon.ee=false;gon.jh=false;gon.dot_com=false;gon.uf_error_prefix="UF";gon.pat_prefix="glpat-";gon.keyboard_shortcuts_enabled=true;gon.diagramsnet_url="https://embed.diagrams.net";gon.version="18.0.2";gon.current_user_id=4;gon.current_username="khangnh";gon.current_user_fullname="Nguyen Hoang Khang";gon.current_user_avatar_url="https://secure.gravatar.com/avatar/e5f52437484dc7deba0f9392a01ad4a1929ce99d3379b7674061c774ed6c7441?s=80\u0026d=identicon";gon.current_user_use_work_items_view=false;gon.text_editor="not_set";gon.features={"uiForOrganizations":false,"organizationSwitching":false,"findAndReplace":false,"removeMonitorMetrics":true,"workItemsViewPreference":true,"workItemViewForIssues":true,"searchButtonTopRight":false,"mergeRequestDashboard":false,"newProjectCreationForm":false,"workItemsClientSideBoards":false,"glqlWorkItems":false};
//]]>
</script>
<script>
//<![CDATA[
window.uploads_path = "/the-ai-team/innocody/innocody-engine/uploads";



//]]>
</script>





<meta content="dark light" name="color-scheme">
<link rel="stylesheet" href="./test_files/application_dark-1a373982e463cf433069c1368cf7ac2476890a6702c4cdd8eabace37d76dec70.css">
<link rel="stylesheet" href="./test_files/pipelines-6ba5ae9caa773b87981434d8647999760be98ed9899bda3c0ecd3fa85ff8434a.css"><link rel="stylesheet" href="./test_files/ci_status-20afc8363b8143e2aa515e8598c1f170a245cefb1394c00334d621e05236657b.css"><link rel="stylesheet" href="./test_files/commit_description-1e2cba4dda3c7b30dd84924809020c569f1308dea51520fe1dd5d4ce31403195.css"><link rel="stylesheet" href="./test_files/work_items-719106b9e2288f0ecc75a8684a0312e38134efe78ac4cc5a9e7e37e93741fb3e.css"><link rel="stylesheet" href="./test_files/notes_shared-dcc7282569d2548ab3f480f68ca656dfaffd9d58ccaf6c8aac8a297bd5249d1f.css">
<link rel="stylesheet" href="./test_files/application_utilities_dark-f77f86f78d4146d4c2c821bc481cee77b897df284886ad189d8dcb1234cb9651.css">
<link rel="stylesheet" href="./test_files/tailwind-8a6161be68949b504e420e6e326210e08b447ec6230509ff23b0a9be20b24052.css">


<link rel="stylesheet" href="./test_files/fonts-fae5d3f79948bd85f18b6513a025f863b19636e85b09a1492907eb4b1bb0557b.css">
<link rel="stylesheet" href="./test_files/white-e4167b85702e96dd41cb029f9684388ac04731836d742ce6e8b65e2f8c2c96fd.css">

<script src="./test_files/runtime.24239d66.bundle.js" defer="defer"></script>
<script src="./test_files/main.acd40823.chunk.js" defer="defer"></script>
<script src="./test_files/tracker.474aab07.chunk.js" defer="defer"></script>
<script>
//<![CDATA[
window.snowplowOptions = {"namespace":"gl","hostname":"gitlab-v2.innotech-vn.com:443","postPath":"/-/collect_events","forceSecureTracker":true,"appId":"gitlab_sm"}

gl = window.gl || {};
gl.snowplowStandardContext = {"schema":"iglu:com.gitlab/gitlab_standard/jsonschema/1-1-6","data":{"environment":"self-managed","source":"gitlab-rails","correlation_id":"01K1ZASF6XNPAYSP8YT7GH6M2J","plan":"default","extra":{},"user_id":"yX752vyYLiA+wHcAVhZS4ATl2xMCJmLMUu9Oeadu53M=","global_user_id":"3kc5eC7Ysyz8ZM/Zy29tPxBTQ1XKc6+WEnZr6f5CDRU=","is_gitlab_team_member":null,"namespace_id":8,"ultimate_parent_namespace_id":7,"project_id":8,"feature_enabled_by_namespace_ids":null,"realm":null,"instance_id":null,"unique_instance_id":"b293c948-c055-5e9a-9c0a-e72f60c39cc0","host_name":"gitlab-v2.innotech-vn.com","instance_version":"18.0.2","context_generated_at":"2025-08-06T16:18:26.882+07:00"}}
gl.snowplowPseudonymizedPageUrl = "https://gitlab-v2.innotech-vn.com/namespace8/project8/-/commit/84acef7360827487fbc4ee5895d38b2c8c04a0c5/pipelines";
gl.maskedDefaultReferrerUrl = "https://gitlab-v2.innotech-vn.com/namespace/project/-/commit/84acef7360827487fbc4ee5895d38b2c8c04a0c5";
gl.ga4MeasurementId = 'G-ENFH3X7M5Y';
gl.duoEvents = [];
gl.onlySendDuoEvents = false;


//]]>
</script>
<link rel="preload" href="./test_files/application_utilities_dark-f77f86f78d4146d4c2c821bc481cee77b897df284886ad189d8dcb1234cb9651.css" as="style" type="text/css">
<link rel="preload" href="./test_files/application_dark-1a373982e463cf433069c1368cf7ac2476890a6702c4cdd8eabace37d76dec70.css" as="style" type="text/css">
<link rel="preload" href="./test_files/white-e4167b85702e96dd41cb029f9684388ac04731836d742ce6e8b65e2f8c2c96fd.css" as="style" type="text/css">
<link crossorigin="" href="https://events.gitlab.net/" rel="preconnect">
<link as="font" crossorigin="" href="https://gitlab-v2.innotech-vn.com/assets/gitlab-sans/GitLabSans-1e0a5107ea3bbd4be93e8ad2c503467e43166cd37e4293570b490e0812ede98b.woff2" rel="preload">
<link as="font" crossorigin="" href="https://gitlab-v2.innotech-vn.com/assets/gitlab-sans/GitLabSans-Italic-38eaf1a569a54ab28c58b92a4a8de3afb96b6ebc250cf372003a7b38151848cc.woff2" rel="preload">
<link as="font" crossorigin="" href="https://gitlab-v2.innotech-vn.com/assets/gitlab-mono/GitLabMono-08d2c5e8ff8fd3d2d6ec55bc7713380f8981c35f9d2df14e12b835464d6e8f23.woff2" rel="preload">
<link as="font" crossorigin="" href="https://gitlab-v2.innotech-vn.com/assets/gitlab-mono/GitLabMono-Italic-38e58d8df29485a20c550da1d0111e2c2169f6dcbcf894f2cd3afbdd97bcc588.woff2" rel="preload">
<link rel="preload" href="./test_files/fonts-fae5d3f79948bd85f18b6513a025f863b19636e85b09a1492907eb4b1bb0557b.css" as="style" type="text/css">




<script src="./test_files/commons-pages.admin.abuse_reports.show-pages.admin.topics.edit-pages.admin.topics.new-pages.groups.i-62354b5a.6612f85a.chunk.js" defer="defer"></script>
<script src="./test_files/commons-pages.groups.harbor.repositories-pages.groups.new-pages.groups.packages-pages.groups.registr-aba9f596.88d51da4.chunk.js" defer="defer"></script>
<script src="./test_files/commons-pages.search.show-super_sidebar.42cee40f.chunk.js" defer="defer"></script>
<script src="./test_files/super_sidebar.7c5590c6.chunk.js" defer="defer"></script>
<script src="./test_files/commons-pages.projects-pages.projects.activity-pages.projects.alert_management.details-pages.project-f24f3db4.4f50ca28.chunk.js" defer="defer"></script>
<script src="./test_files/27.3e24d9c4.chunk.js" defer="defer"></script>
<script src="./test_files/73.dc51f457.chunk.js" defer="defer"></script>
<script src="./test_files/commons-pages.projects.ci.pipeline_editor.show-pages.projects.commit.pipelines-pages.projects.commit-4ed71dfb.9be2850e.chunk.js" defer="defer"></script>
<script src="./test_files/commons-pages.projects.commit.pipelines-pages.projects.commit.rapid_diffs-pages.projects.commit.show.3db0f005.chunk.js" defer="defer"></script>
<script src="./test_files/pages.projects.commit.pipelines.9d73f450.chunk.js" defer="defer"></script>

<meta content="object" property="og:type">
<meta content="GitLab" property="og:site_name">
<meta content="Pipelines · feat: html parser enhance init (84acef73) · Commits · The AI Team / innocody / innocody-engine · GitLab" property="og:title">
<meta content="GitLab Community Edition" property="og:description">
<meta content="https://gitlab-v2.innotech-vn.com/assets/twitter_card-570ddb06edf56a2312253c5872489847a0f385112ddbcd71ccfa1570febab5d2.jpg" property="og:image">
<meta content="64" property="og:image:width">
<meta content="64" property="og:image:height">
<meta content="https://gitlab-v2.innotech-vn.com/the-ai-team/innocody/innocody-engine/-/commit/84acef7360827487fbc4ee5895d38b2c8c04a0c5/pipelines" property="og:url">
<meta content="summary" property="twitter:card">
<meta content="Pipelines · feat: html parser enhance init (84acef73) · Commits · The AI Team / innocody / innocody-engine · GitLab" property="twitter:title">
<meta content="GitLab Community Edition" property="twitter:description">
<meta content="https://gitlab-v2.innotech-vn.com/assets/twitter_card-570ddb06edf56a2312253c5872489847a0f385112ddbcd71ccfa1570febab5d2.jpg" property="twitter:image">

<meta name="csrf-param" content="authenticity_token">
<meta name="csrf-token" content="MEvEJDhqtX6S7JoyDJ5pWz7ZRHf6v3gL8UjJdhvpvk8EbMkuFqs6vnosoLjC0ufNGHuUfDNBp98ndCQwC2Yiow">
<meta name="csp-nonce">
<meta name="action-cable-url" content="/-/cable">
<link href="https://gitlab-v2.innotech-vn.com/-/manifest.json" rel="manifest">
<link rel="icon" type="image/png" href="https://gitlab-v2.innotech-vn.com/uploads/-/system/appearance/favicon/1/download__3_.png" id="favicon" data-original-href="/uploads/-/system/appearance/favicon/1/download__3_.png">
<link rel="apple-touch-icon" type="image/x-icon" href="https://gitlab-v2.innotech-vn.com/assets/apple-touch-icon-b049d4bc0dd9626f31db825d61880737befc7835982586d015bded10b4435460.png">
<link href="https://gitlab-v2.innotech-vn.com/search/opensearch.xml" rel="search" title="Search GitLab" type="application/opensearchdescription+xml">




<meta content="GitLab Community Edition" name="description">
<meta content="#ececef" name="theme-color">
<style>
svg[data-v-9534917c] {
  pointer-events: none;

  position: fixed;
  right: 0;
}
svg polygon[data-v-9534917c],
svg rect[data-v-9534917c] {
  pointer-events: auto;
}
</style><style>
.fake-input[data-v-26a01bbc] {
  position: absolute;
  top: 14px;
  left: 39px;
}
</style><style>
.input-box-wrapper[data-v-31424bc0] {
  position: relative;
}
.fake-input-wrapper[data-v-31424bc0] {
  position: absolute;
}
</style><script charset="utf-8" src="./test_files/87.220ef488.chunk.js"></script><script charset="utf-8" src="./test_files/121.79545acb.chunk.js"></script><script charset="utf-8" src="./test_files/vendors-content_editor-statusModalBundle.d55f3e23.chunk.js"></script><script charset="utf-8" src="./test_files/vendors-statusModalBundle.a8d5511d.chunk.js"></script><script charset="utf-8" src="./test_files/statusModalBundle.3a251f08.chunk.js"></script><script charset="utf-8" src="./test_files/shortcutsBundle.94c47c24.chunk.js"></script><script charset="utf-8" src="./test_files/hello.409c74c1.chunk.js"></script><script charset="utf-8" src="./test_files/25.6210b2c2.chunk.js"></script><script charset="utf-8" src="./test_files/863.0d64de5b.chunk.js"></script><script charset="utf-8" src="./test_files/52.5b9b2131.chunk.js"></script><script charset="utf-8" src="./test_files/224.41f7aae1.chunk.js"></script><script charset="utf-8" src="./test_files/initInviteMembersModal.608af029.chunk.js"></script><script charset="utf-8" src="./test_files/initInviteMembersTrigger.17b91217.chunk.js"></script></head>

<body class="tab-width-8 gl-browser-edge gl-platform-linux body-fixed-scrollbar page-initialised" data-group="innocody" data-group-full-path="the-ai-team/innocody" data-namespace-id="8" data-page="projects:commit:pipelines" data-page-type-id="84acef7360827487fbc4ee5895d38b2c8c04a0c5" data-project="innocody-engine" data-project-full-path="the-ai-team/innocody/innocody-engine" data-project-id="8">
<div id="js-tooltips-container"></div>

<script>
//<![CDATA[
gl = window.gl || {};
gl.client = {"isEdge":true,"isLinux":true};


//]]>
</script>


<div class="layout-page page-with-super-sidebar">
<div><div class="super-sidebar-overlay"></div> <a data-testid="super-sidebar-skip-to" href="https://gitlab-v2.innotech-vn.com/the-ai-team/innocody/innocody-engine/-/commit/84acef7360827487fbc4ee5895d38b2c8c04a0c5/pipelines#content-body" class="btn super-sidebar-skip-to gl-sr-only !gl-fixed gl-left-0 !gl-m-3 !gl-px-4 focus:gl-not-sr-only btn-confirm btn-md gl-button"><!----> <!---->  <span class="gl-button-text">
    Skip to main content
  </span></a> <nav id="super-sidebar" aria-labelledby="super-sidebar-heading" data-testid="super-sidebar" class="super-sidebar"><h2 id="super-sidebar-heading" class="gl-sr-only">
      Primary navigation
    </h2> <div class="user-bar gl-flex gl-gap-1 gl-p-3 gl-flex-col gl-gap-3"><div class="gl-flex gl-items-center gl-gap-1"><a href="https://gitlab-v2.innotech-vn.com/" data-track-action="click_link" data-track-label="gitlab_logo_link" data-track-property="nav_core_menu" class="brand-logo gl-inline-block gl-rounded-base gl-border-none gl-bg-transparent gl-p-2 focus:gl-focus active:gl-focus"><span class="gl-sr-only">Homepage</span> <span aria-hidden="true" data-testid="brand-header-default-logo"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 25 24" height="24" width="25" class="tanuki-logo" role="img" aria-hidden="true">
  <path fill="#E24329" d="m24.507 9.5-.034-.09L21.082.562a.896.896 0 0 0-1.694.091l-2.29 7.01H7.825L5.535.653a.898.898 0 0 0-1.694-.09L.451 9.411.416 9.5a6.297 6.297 0 0 0 2.09 7.278l.012.01.03.022 5.16 3.867 2.56 1.935 1.554 1.176a1.051 1.051 0 0 0 1.268 0l1.555-1.176 2.56-1.935 5.197-3.89.014-.01A6.297 6.297 0 0 0 24.507 9.5Z" class="tanuki-shape tanuki"></path>
  <path fill="#FC6D26" d="m24.507 9.5-.034-.09a11.44 11.44 0 0 0-4.56 2.051l-7.447 5.632 4.742 3.584 5.197-3.89.014-.01A6.297 6.297 0 0 0 24.507 9.5Z" class="tanuki-shape right-cheek"></path>
  <path fill="#FCA326" d="m7.707 20.677 2.56 1.935 1.555 1.176a1.051 1.051 0 0 0 1.268 0l1.555-1.176 2.56-1.935-4.743-3.584-4.755 3.584Z" class="tanuki-shape chin"></path>
  <path fill="#FC6D26" d="M5.01 11.461a11.43 11.43 0 0 0-4.56-2.05L.416 9.5a6.297 6.297 0 0 0 2.09 7.278l.012.01.03.022 5.16 3.867 4.745-3.584-7.444-5.632Z" class="tanuki-shape left-cheek"></path>
</svg>
</span></a> <!----> <div class="gl-grow"></div> <button aria-controls="super-sidebar" aria-expanded="true" aria-label="Primary navigation sidebar" type="button" class="btn btn-default btn-md gl-button btn-default-tertiary btn-icon js-super-sidebar-toggle-collapse" data-testid="super-sidebar-collapse-button"><!----> <svg data-testid="sidebar-icon" role="img" aria-hidden="true" class="gl-button-icon gl-icon s16 gl-fill-current"><use href="/assets/icons-ca86b3ebff8cbe14f7728864eabad153d00b66986018226fe439015884de11c2.svg#sidebar"></use></svg>  <!----></button> <div class="gl-disclosure-dropdown gl-new-dropdown" data-testid="new-menu-toggle"><button id="create-menu-toggle" data-testid="base-dropdown-toggle" aria-expanded="false" aria-controls="base-dropdown-2" type="button" class="btn btn-default btn-md gl-button btn-default-tertiary gl-new-dropdown-toggle gl-new-dropdown-icon-only btn-icon gl-new-dropdown-toggle-no-caret"><!----> <svg data-testid="plus-icon" role="img" aria-hidden="true" class="gl-button-icon gl-icon s16 gl-fill-current"><use href="/assets/icons-ca86b3ebff8cbe14f7728864eabad153d00b66986018226fe439015884de11c2.svg#plus"></use></svg>  <span class="gl-button-text"><span class="gl-new-dropdown-button-text gl-sr-only">
        Create new…
      </span> <!----></span></button> <div id="base-dropdown-2" data-testid="base-dropdown-menu" class="gl-new-dropdown-panel !gl-w-31"><div class="gl-new-dropdown-arrow"></div> <div class="gl-new-dropdown-inner">  <ul id="disclosure-1" aria-labelledby="create-menu-toggle" data-testid="disclosure-content" tabindex="-1" class="gl-new-dropdown-contents"><li><div id="gl-disclosure-dropdown-group-3" class="gl-py-2 gl-pl-4 gl-text-sm gl-font-bold gl-text-strong">In this project</div> <ul aria-labelledby="gl-disclosure-dropdown-group-3" class="gl-mb-0 gl-list-none gl-pl-0"><li tabindex="0" data-testid="disclosure-dropdown-item" class="gl-new-dropdown-item"><a href="https://gitlab-v2.innotech-vn.com/the-ai-team/innocody/innocody-engine/-/issues/new" tabindex="-1" data-track-label="new_issue" data-track-action="click_link" data-track-property="nav_create_menu" data-testid="create_menu_item" data-qa-create-menu-item="new_issue" class="gl-new-dropdown-item-content"><span class="gl-new-dropdown-item-text-wrapper">
          New issue
        </span></a></li><li tabindex="0" data-testid="disclosure-dropdown-item" class="gl-new-dropdown-item"><a href="https://gitlab-v2.innotech-vn.com/the-ai-team/innocody/innocody-engine/-/merge_requests/new" tabindex="-1" data-track-label="new_mr" data-track-action="click_link" data-track-property="nav_create_menu" data-testid="create_menu_item" data-qa-create-menu-item="new_mr" class="gl-new-dropdown-item-content"><span class="gl-new-dropdown-item-text-wrapper">
          New merge request
        </span></a></li><li tabindex="0" data-testid="disclosure-dropdown-item" class="gl-new-dropdown-item"><a href="https://gitlab-v2.innotech-vn.com/the-ai-team/innocody/innocody-engine/-/snippets/new" tabindex="-1" data-track-label="new_snippet" data-track-action="click_link" data-track-property="nav_create_menu" data-testid="create_menu_item" data-qa-create-menu-item="new_snippet" class="gl-new-dropdown-item-content"><span class="gl-new-dropdown-item-text-wrapper">
          New snippet
        </span></a></li><li tabindex="0" data-testid="invite-members-button" class="gl-new-dropdown-item"><button tabindex="-1" type="button" class="gl-new-dropdown-item-content"><span class="gl-new-dropdown-item-text-wrapper">
          Invite team members
        </span></button></li></ul></li><li class="gl-border-t gl-border-t-dropdown-divider gl-pt-2 gl-mt-2"><div id="gl-disclosure-dropdown-group-4" class="gl-py-2 gl-pl-4 gl-text-sm gl-font-bold gl-text-strong">In GitLab</div> <ul aria-labelledby="gl-disclosure-dropdown-group-4" class="gl-mb-0 gl-list-none gl-pl-0"><li tabindex="0" data-testid="disclosure-dropdown-item" class="gl-new-dropdown-item"><a href="https://gitlab-v2.innotech-vn.com/projects/new" tabindex="-1" data-track-label="general_new_project" data-track-action="click_link" data-track-property="nav_create_menu" data-testid="create_menu_item" data-qa-create-menu-item="general_new_project" class="gl-new-dropdown-item-content"><span class="gl-new-dropdown-item-text-wrapper">
          New project/repository
        </span></a></li><li tabindex="0" data-testid="disclosure-dropdown-item" class="gl-new-dropdown-item"><a href="https://gitlab-v2.innotech-vn.com/groups/new" tabindex="-1" data-track-label="general_new_group" data-track-action="click_link" data-track-property="nav_create_menu" data-testid="create_menu_item" data-qa-create-menu-item="general_new_group" class="gl-new-dropdown-item-content"><span class="gl-new-dropdown-item-text-wrapper">
          New group
        </span></a></li><li tabindex="0" data-testid="disclosure-dropdown-item" class="gl-new-dropdown-item"><a href="https://gitlab-v2.innotech-vn.com/-/snippets/new" tabindex="-1" data-track-label="general_new_snippet" data-track-action="click_link" data-track-property="nav_create_menu" data-testid="create_menu_item" data-qa-create-menu-item="general_new_snippet" class="gl-new-dropdown-item-content"><span class="gl-new-dropdown-item-text-wrapper">
          New snippet
        </span></a></li></ul></li> <!----> <!----></ul> </div></div></div> <div><div class="gl-disclosure-dropdown gl-new-dropdown" data-testid="user-dropdown"><div id="dropdown-toggle-btn-5" data-testid="base-dropdown-toggle" class="gl-new-dropdown-custom-toggle"><button data-testid="user-menu-toggle" type="button" class="btn user-bar-dropdown-toggle btn-with-notification btn-default btn-md gl-button btn-default-tertiary"><!----> <!---->  <span class="gl-button-text"><span class="gl-sr-only">Nguyen Hoang Khang user’s menu</span> <img src="./test_files/e5f52437484dc7deba0f9392a01ad4a1929ce99d3379b7674061c774ed6c7441" alt="avatar" class="gl-avatar gl-avatar-circle gl-avatar-s24" aria-hidden="true" data-testid="user-avatar-content"> <!----></span></button></div> <div id="base-dropdown-7" data-testid="base-dropdown-menu" class="gl-new-dropdown-panel !gl-w-31"><div class="gl-new-dropdown-arrow"></div> <div class="gl-new-dropdown-inner">  <ul id="disclosure-6" aria-labelledby="dropdown-toggle-btn-5" data-testid="disclosure-content" tabindex="-1" class="gl-new-dropdown-contents"> <li><!----> <ul class="gl-mb-0 gl-list-none gl-pl-0"><li tabindex="0" data-testid="disclosure-dropdown-item" class="gl-new-dropdown-item"><a href="https://gitlab-v2.innotech-vn.com/khangnh" tabindex="-1" data-track-property="nav_user_menu" data-track-action="click_link" data-track-label="user_profile" data-testid="user-profile-link" class="gl-new-dropdown-item-content"><span class="gl-new-dropdown-item-text-wrapper"><span class="gl-flex gl-flex-col"><span><span class="gl-font-bold">
          Nguyen Hoang Khang
        </span> <!----></span> <span class="gl-break-all gl-text-subtle">@khangnh</span> <!----></span></span></a></li></ul></li> <li class="gl-border-t gl-border-t-dropdown-divider gl-pt-2 gl-mt-2"><!----> <ul class="gl-mb-0 gl-list-none gl-pl-0"><li tabindex="0" data-testid="status-item" class="gl-new-dropdown-item" role="button"><button tabindex="-1" data-track-property="nav_user_menu" data-track-action="click_link" data-track-label="user_edit_status" type="button" class="gl-new-dropdown-item-content"><span class="gl-new-dropdown-item-text-wrapper">
          Set status
        </span></button></li> <!----> <li tabindex="0" data-testid="edit-profile-item" class="gl-new-dropdown-item"><a href="https://gitlab-v2.innotech-vn.com/-/user_settings/profile" tabindex="-1" data-testid="edit-profile-link" data-track-property="nav_user_menu" data-track-action="click_link" data-track-label="user_edit_profile" class="gl-new-dropdown-item-content"><span class="gl-new-dropdown-item-text-wrapper">
          Edit profile
        </span></a></li> <li tabindex="0" data-testid="preferences-item" class="gl-new-dropdown-item"><a href="https://gitlab-v2.innotech-vn.com/-/profile/preferences" tabindex="-1" data-track-property="nav_user_menu" data-track-action="click_link" data-track-label="user_preferences" class="gl-new-dropdown-item-content"><span class="gl-new-dropdown-item-text-wrapper">
          Preferences
        </span></a></li> <!----> <!----> <!----> <!----></ul></li> <li class="gl-border-t gl-border-t-dropdown-divider gl-pt-2 gl-mt-2" data-testid="sign-out-group"><!----> <ul class="gl-mb-0 gl-list-none gl-pl-0"><li tabindex="0" data-testid="disclosure-dropdown-item" class="gl-new-dropdown-item"><a href="https://gitlab-v2.innotech-vn.com/users/sign_out" tabindex="-1" data-method="post" data-testid="sign-out-link" class="gl-new-dropdown-item-content sign-out-link"><span class="gl-new-dropdown-item-text-wrapper">
          Sign out
        </span></a></li></ul></li></ul> </div></div></div> <!----></div> <!----></div> <!----> <div class="gl-flex gl-justify-between gl-gap-2"><a aria-label="Assigned issues 0" href="https://gitlab-v2.innotech-vn.com/dashboard/issues?assignee_username=khangnh" class="user-bar-button gl-block gl-grow gl-rounded-base gl-py-3 gl-text-center gl-text-sm gl-leading-1 hover:gl-no-underline dashboard-shortcuts-issues gl-basis-1/3" data-testid="issues-shortcut-button" data-track-action="click_link" data-track-label="issues_link" data-track-property="nav_core_menu"><svg data-testid="issues-icon" role="img" aria-hidden="true" class="gl-icon s16 gl-fill-current"><use href="/assets/icons-ca86b3ebff8cbe14f7728864eabad153d00b66986018226fe439015884de11c2.svg#issues"></use></svg> <!----></a> <div class="gl-disclosure-dropdown !gl-block gl-basis-1/3 gl-new-dropdown"><div id="dropdown-toggle-btn-12" data-testid="base-dropdown-toggle" class="gl-new-dropdown-custom-toggle"><button aria-label="Merge requests 3" class="user-bar-button gl-block gl-grow gl-rounded-base gl-py-3 gl-text-center gl-text-sm gl-leading-1 hover:gl-no-underline gl-w-full" data-testid="merge-requests-shortcut-button" data-track-action="click_dropdown" data-track-label="merge_requests_menu" data-track-property="nav_core_menu"><svg data-testid="merge-request-icon" role="img" aria-hidden="true" class="gl-icon s16 gl-fill-current"><use href="/assets/icons-ca86b3ebff8cbe14f7728864eabad153d00b66986018226fe439015884de11c2.svg#merge-request"></use></svg> <span aria-hidden="true" class="gl-ml-1">3</span></button></div> <div id="base-dropdown-14" data-testid="base-dropdown-menu" class="gl-new-dropdown-panel !gl-w-31"><div class="gl-new-dropdown-arrow"></div> <div class="gl-new-dropdown-inner">  <ul id="disclosure-13" aria-labelledby="dropdown-toggle-btn-12" data-testid="disclosure-content" tabindex="-1" class="gl-new-dropdown-contents"><li><div id="gl-disclosure-dropdown-group-15" class="gl-py-2 gl-pl-4 gl-text-sm gl-font-bold gl-text-strong">Merge requests</div> <ul aria-labelledby="gl-disclosure-dropdown-group-15" class="gl-mb-0 gl-list-none gl-pl-0"><li tabindex="0" data-testid="disclosure-dropdown-item" class="gl-new-dropdown-item"><a href="https://gitlab-v2.innotech-vn.com/dashboard/merge_requests?assignee_username=khangnh" tabindex="-1" data-track-action="click_link" data-track-label="merge_requests_assigned" data-track-property="nav_core_menu" class="gl-new-dropdown-item-content dashboard-shortcuts-merge_requests"><span class="gl-new-dropdown-item-text-wrapper"><span class="gl-flex gl-items-center gl-justify-between">
      Assigned
      <span class="gl-badge badge badge-pill badge-neutral" pill=""><!----> <span class="gl-badge-content">3</span></span></span></span></a></li><li tabindex="0" data-testid="disclosure-dropdown-item" class="gl-new-dropdown-item"><a href="https://gitlab-v2.innotech-vn.com/dashboard/merge_requests?reviewer_username=khangnh" tabindex="-1" data-track-action="click_link" data-track-label="merge_requests_to_review" data-track-property="nav_core_menu" class="gl-new-dropdown-item-content dashboard-shortcuts-review_requests"><span class="gl-new-dropdown-item-text-wrapper"><span class="gl-flex gl-items-center gl-justify-between">
      Review requests
      <span class="gl-badge badge badge-pill badge-neutral" pill=""><!----> <span class="gl-badge-content">0</span></span></span></span></a></li></ul></li></ul> </div></div></div> <a aria-label="To-Do List 10" href="https://gitlab-v2.innotech-vn.com/dashboard/todos" class="user-bar-button gl-block gl-grow gl-rounded-base gl-py-3 gl-text-center gl-text-sm gl-leading-1 hover:gl-no-underline shortcuts-todos js-todos-count gl-basis-1/3" data-testid="todos-shortcut-button" data-track-action="click_link" data-track-label="todos_link" data-track-property="nav_core_menu"><svg data-testid="todo-done-icon" role="img" aria-hidden="true" class="gl-icon s16 gl-fill-current"><use href="/assets/icons-ca86b3ebff8cbe14f7728864eabad153d00b66986018226fe439015884de11c2.svg#todo-done"></use></svg> <span aria-hidden="true" class="gl-ml-1">10</span></a></div> <div class="gl-grow"><button id="super-sidebar-search" data-testid="super-sidebar-search-button" type="button" class="btn user-bar-button gl-border-none btn-default btn-md btn-block gl-button"><!----> <!---->  <span class="gl-button-text"><svg data-testid="search-icon" role="img" aria-hidden="true" class="gl-icon s16 gl-fill-current"><use href="/assets/icons-ca86b3ebff8cbe14f7728864eabad153d00b66986018226fe439015884de11c2.svg#search"></use></svg>
      Search or go to…
    </span></button> <!----></div></div> <div class="contextual-nav gl-flex gl-grow gl-flex-col gl-overflow-hidden"><div id="super-sidebar-context-header" class="super-sidebar-context-header gl-m-0 gl-px-4 gl-py-3 gl-font-bold gl-leading-reset">
        Project
      </div> <div class="gl-scroll-scrim gl-overflow-auto gl-grow top-scrim-visible gl-border-t bottom-scrim-visible gl-border-b" data-testid="nav-container"><div class="top-scrim-wrapper"><div class="top-scrim"></div></div> <div></div> <div class="gl-relative gl-p-2"><ul data-testid="static-items-section" class="gl-m-0 gl-list-none gl-p-0"><li data-testid="nav-item" class="show-on-focus-or-hover--context hide-on-focus-or-hover--context transition-opacity-on-hover--context gl-relative"><a href="https://gitlab-v2.innotech-vn.com/the-ai-team/innocody/innocody-engine" class="super-sidebar-nav-item show-on-focus-or-hover--control hide-on-focus-or-hover--control gl-relative gl-mb-1 gl-flex gl-min-h-7 gl-items-center gl-gap-3 gl-py-2 !gl-text-default !gl-no-underline focus:gl-focus gl-px-3 gl-rounded-base shortcuts-project" data-testid="nav-item-link" aria-label="innocody-engine" data-track-action="click_menu_item" data-track-label="project_overview" data-track-property="nav_panel_project" data-qa-submenu-item="project-overview"><div aria-hidden="true" data-testid="active-indicator" class="active-indicator gl-absolute gl-bottom-2 gl-left-2 gl-top-2 gl-transition-all gl-duration-slow gl-opacity-0" style="width: 3px; border-radius: 3px; margin-right: 1px; transform: translateX(-1px);"></div> <div class="gl-flex gl-w-6 gl-shrink-0 gl-self-start"><div class="gl-avatar gl-avatar-identicon gl-avatar-s24 gl-avatar-identicon-bg2">
  I
</div></div> <div data-testid="nav-item-link-label" class="gl-grow gl-text-default gl-break-anywhere">
      innocody-engine
      <!----></div>  <!----></a> <!----></li></ul> <div><!----> <button id="menu-section-button-pinned" data-testid="menu-section-button" data-qa-section-name="Pinned" aria-controls="pinned" aria-expanded="true" data-qa-menu-item="Pinned" class="super-sidebar-nav-item gl-relative gl-mb-2 gl-flex gl-min-h-7 gl-w-full gl-appearance-none gl-items-center gl-gap-3 gl-rounded-base gl-border-0 gl-bg-transparent gl-px-3 gl-py-2 gl-text-left !gl-text-default !gl-no-underline focus:gl-focus"><span aria-hidden="true" class="gl-absolute gl-bottom-2 gl-left-2 gl-top-2 gl-transition-all gl-duration-slow gl-bg-transparent" style="width: 3px; border-radius: 3px; margin-right: 1px;"></span> <span class="gl-flex gl-w-6 gl-shrink-0"><svg data-testid="thumbtack-icon" role="img" aria-hidden="true" class="super-sidebar-nav-item-icon gl-m-auto gl-icon s16 gl-fill-current"><use href="/assets/icons-ca86b3ebff8cbe14f7728864eabad153d00b66986018226fe439015884de11c2.svg#thumbtack"></use></svg></span> <span class="gl-truncate-end gl-grow gl-text-default">
      Pinned
    </span> <span class="gl-text-right gl-text-subtle"><svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg" class="gl-animated-icon gl-animated-icon-on gl-animated-icon-current"><path d="M6.75 4.75L10 8L6.75 11.25" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="gl-animated-chevron-right-down-arrow"></path></svg></span></button> <!----> <div class="gl-m-0 gl-list-none gl-p-0 gl-duration-medium gl-ease-ease collapse show" id="pinned" data-testid="menu-section" data-qa-section-name="Pinned"><ul data-testid="pinned-nav-items" class="gl-m-0 gl-list-none gl-p-0"><li data-testid="nav-item" class="show-on-focus-or-hover--context hide-on-focus-or-hover--context transition-opacity-on-hover--context gl-relative"><a href="https://gitlab-v2.innotech-vn.com/the-ai-team/innocody/innocody-engine/-/issues" class="super-sidebar-nav-item show-on-focus-or-hover--control hide-on-focus-or-hover--control gl-relative gl-mb-1 gl-flex gl-min-h-7 gl-items-center gl-gap-3 gl-py-2 !gl-text-default !gl-no-underline focus:gl-focus gl-px-3 gl-rounded-base shortcuts-issues has-sub-items" data-testid="nav-item-link" aria-label="Issues" data-track-action="click_pinned_menu_item" data-track-label="project_issue_list" data-track-property="nav_panel_project" data-qa-submenu-item="Issues"><div aria-hidden="true" data-testid="active-indicator" class="active-indicator gl-absolute gl-bottom-2 gl-left-2 gl-top-2 gl-transition-all gl-duration-slow gl-opacity-0" style="width: 3px; border-radius: 3px; margin-right: 1px;"></div> <div class="gl-flex gl-w-6 gl-shrink-0"><svg data-testid="grip-icon" role="img" aria-hidden="true" class="js-draggable-icon show-on-focus-or-hover--target super-sidebar-mix-blend-mode gl-m-auto gl-cursor-grab gl-icon s16 gl-fill-icon-subtle"><use href="/assets/icons-ca86b3ebff8cbe14f7728864eabad153d00b66986018226fe439015884de11c2.svg#grip"></use></svg></div> <div data-testid="nav-item-link-label" class="gl-grow gl-text-default gl-break-anywhere">
      Issues
      <!----></div>  <span class="gl-flex gl-min-w-6 gl-items-start gl-justify-end"><span class="gl-badge badge badge-pill badge-neutral hide-on-focus-or-hover--target transition-opacity-on-hover--target"><!----> <span class="gl-badge-content">
        0
      </span></span></span></a> <button aria-label="Unpin Issues" data-testid="nav-item-unpin" type="button" class="btn show-on-focus-or-hover--target transition-opacity-on-hover--target always-animate gl-absolute gl-right-3 gl-top-1/2 -gl-translate-y-1/2 btn-default btn-sm gl-button btn-default-tertiary btn-icon gl-pointer-events-none"><!----> <svg data-testid="thumbtack-solid-icon" role="img" aria-hidden="true" class="gl-button-icon gl-icon s16 gl-fill-current"><use href="/assets/icons-ca86b3ebff8cbe14f7728864eabad153d00b66986018226fe439015884de11c2.svg#thumbtack-solid"></use></svg>  <!----></button></li><li data-testid="nav-item" class="show-on-focus-or-hover--context hide-on-focus-or-hover--context transition-opacity-on-hover--context gl-relative"><a href="https://gitlab-v2.innotech-vn.com/the-ai-team/innocody/innocody-engine/-/merge_requests" class="super-sidebar-nav-item show-on-focus-or-hover--control hide-on-focus-or-hover--control gl-relative gl-mb-1 gl-flex gl-min-h-7 gl-items-center gl-gap-3 gl-py-2 !gl-text-default !gl-no-underline focus:gl-focus gl-px-3 gl-rounded-base shortcuts-merge_requests" data-testid="nav-item-link" aria-label="Merge requests" data-track-action="click_pinned_menu_item" data-track-label="project_merge_request_list" data-track-property="nav_panel_project" data-qa-submenu-item="Merge requests"><div aria-hidden="true" data-testid="active-indicator" class="active-indicator gl-absolute gl-bottom-2 gl-left-2 gl-top-2 gl-transition-all gl-duration-slow gl-opacity-0" style="width: 3px; border-radius: 3px; margin-right: 1px;"></div> <div class="gl-flex gl-w-6 gl-shrink-0"><svg data-testid="grip-icon" role="img" aria-hidden="true" class="js-draggable-icon show-on-focus-or-hover--target super-sidebar-mix-blend-mode gl-m-auto gl-cursor-grab gl-icon s16 gl-fill-icon-subtle"><use href="/assets/icons-ca86b3ebff8cbe14f7728864eabad153d00b66986018226fe439015884de11c2.svg#grip"></use></svg></div> <div data-testid="nav-item-link-label" class="gl-grow gl-text-default gl-break-anywhere">
      Merge requests
      <!----></div>  <span class="gl-flex gl-min-w-6 gl-items-start gl-justify-end"><span class="gl-badge badge badge-pill badge-neutral hide-on-focus-or-hover--target transition-opacity-on-hover--target"><!----> <span class="gl-badge-content">
        1
      </span></span></span></a> <button aria-label="Unpin Merge requests" data-testid="nav-item-unpin" type="button" class="btn show-on-focus-or-hover--target transition-opacity-on-hover--target always-animate gl-absolute gl-right-3 gl-top-1/2 -gl-translate-y-1/2 btn-default btn-sm gl-button btn-default-tertiary btn-icon gl-pointer-events-none"><!----> <svg data-testid="thumbtack-solid-icon" role="img" aria-hidden="true" class="gl-button-icon gl-icon s16 gl-fill-current"><use href="/assets/icons-ca86b3ebff8cbe14f7728864eabad153d00b66986018226fe439015884de11c2.svg#thumbtack-solid"></use></svg>  <!----></button></li><li data-testid="nav-item" class="show-on-focus-or-hover--context hide-on-focus-or-hover--context transition-opacity-on-hover--context gl-relative"><a href="https://gitlab-v2.innotech-vn.com/the-ai-team/innocody/innocody-engine/-/pipelines" class="super-sidebar-nav-item show-on-focus-or-hover--control hide-on-focus-or-hover--control gl-relative gl-mb-1 gl-flex gl-min-h-7 gl-items-center gl-gap-3 gl-py-2 !gl-text-default !gl-no-underline focus:gl-focus gl-px-3 gl-rounded-base shortcuts-pipelines" data-testid="nav-item-link" aria-label="Pipelines" data-track-action="click_pinned_menu_item" data-track-label="pipelines" data-track-property="nav_panel_project" data-qa-submenu-item="Pipelines"><div aria-hidden="true" data-testid="active-indicator" class="active-indicator gl-absolute gl-bottom-2 gl-left-2 gl-top-2 gl-transition-all gl-duration-slow gl-opacity-0" style="width: 3px; border-radius: 3px; margin-right: 1px;"></div> <div class="gl-flex gl-w-6 gl-shrink-0"><svg data-testid="grip-icon" role="img" aria-hidden="true" class="js-draggable-icon show-on-focus-or-hover--target super-sidebar-mix-blend-mode gl-m-auto gl-cursor-grab gl-icon s16 gl-fill-icon-subtle"><use href="/assets/icons-ca86b3ebff8cbe14f7728864eabad153d00b66986018226fe439015884de11c2.svg#grip"></use></svg></div> <div data-testid="nav-item-link-label" class="gl-grow gl-text-default gl-break-anywhere">
      Pipelines
      <!----></div>  <span class="gl-flex gl-min-w-6 gl-items-start gl-justify-end"><!----></span></a> <button aria-label="Unpin Pipelines" data-testid="nav-item-unpin" type="button" class="btn show-on-focus-or-hover--target transition-opacity-on-hover--target always-animate gl-absolute gl-right-3 gl-top-1/2 -gl-translate-y-1/2 btn-default btn-sm gl-button btn-default-tertiary btn-icon gl-pointer-events-none"><!----> <svg data-testid="thumbtack-solid-icon" role="img" aria-hidden="true" class="gl-button-icon gl-icon s16 gl-fill-current"><use href="/assets/icons-ca86b3ebff8cbe14f7728864eabad153d00b66986018226fe439015884de11c2.svg#thumbtack-solid"></use></svg>  <!----></button></li><li data-testid="nav-item" class="show-on-focus-or-hover--context hide-on-focus-or-hover--context transition-opacity-on-hover--context gl-relative"><a href="https://gitlab-v2.innotech-vn.com/the-ai-team/innocody/innocody-engine/container_registry" class="super-sidebar-nav-item show-on-focus-or-hover--control hide-on-focus-or-hover--control gl-relative gl-mb-1 gl-flex gl-min-h-7 gl-items-center gl-gap-3 gl-py-2 !gl-text-default !gl-no-underline focus:gl-focus gl-px-3 gl-rounded-base" data-testid="nav-item-link" aria-label="Container registry" data-track-action="click_pinned_menu_item" data-track-label="container_registry" data-track-property="nav_panel_project" data-qa-submenu-item="Container registry"><div aria-hidden="true" data-testid="active-indicator" class="active-indicator gl-absolute gl-bottom-2 gl-left-2 gl-top-2 gl-transition-all gl-duration-slow gl-opacity-0" style="width: 3px; border-radius: 3px; margin-right: 1px;"></div> <div class="gl-flex gl-w-6 gl-shrink-0"><svg data-testid="grip-icon" role="img" aria-hidden="true" class="js-draggable-icon show-on-focus-or-hover--target super-sidebar-mix-blend-mode gl-m-auto gl-cursor-grab gl-icon s16 gl-fill-icon-subtle"><use href="/assets/icons-ca86b3ebff8cbe14f7728864eabad153d00b66986018226fe439015884de11c2.svg#grip"></use></svg></div> <div data-testid="nav-item-link-label" class="gl-grow gl-text-default gl-break-anywhere">
      Container registry
      <!----></div>  <span class="gl-flex gl-min-w-6 gl-items-start gl-justify-end"><!----></span></a> <button aria-label="Unpin Container registry" data-testid="nav-item-unpin" type="button" class="btn show-on-focus-or-hover--target transition-opacity-on-hover--target always-animate gl-absolute gl-right-3 gl-top-1/2 -gl-translate-y-1/2 btn-default btn-sm gl-button btn-default-tertiary btn-icon gl-pointer-events-none"><!----> <svg data-testid="thumbtack-solid-icon" role="img" aria-hidden="true" class="gl-button-icon gl-icon s16 gl-fill-current"><use href="/assets/icons-ca86b3ebff8cbe14f7728864eabad153d00b66986018226fe439015884de11c2.svg#thumbtack-solid"></use></svg>  <!----></button></li></ul></div></div> <hr aria-hidden="true" data-testid="main-menu-separator" class="gl-mx-4 gl-my-2"> <ul aria-labelledby="super-sidebar-context-header" data-testid="non-static-items-section" class="gl-mb-0 gl-list-none gl-p-0"><li><!----> <button id="menu-section-button-manage" data-testid="menu-section-button" data-qa-section-name="Manage" aria-controls="manage" aria-expanded="false" data-qa-menu-item="Manage" class="super-sidebar-nav-item gl-relative gl-mb-2 gl-flex gl-min-h-7 gl-w-full gl-appearance-none gl-items-center gl-gap-3 gl-rounded-base gl-border-0 gl-bg-transparent gl-px-3 gl-py-2 gl-text-left !gl-text-default !gl-no-underline focus:gl-focus"><span aria-hidden="true" class="gl-absolute gl-bottom-2 gl-left-2 gl-top-2 gl-transition-all gl-duration-slow gl-bg-transparent" style="width: 3px; border-radius: 3px; margin-right: 1px;"></span> <span class="gl-flex gl-w-6 gl-shrink-0"><svg data-testid="users-icon" role="img" aria-hidden="true" class="super-sidebar-nav-item-icon gl-m-auto gl-icon s16 gl-fill-current"><use href="/assets/icons-ca86b3ebff8cbe14f7728864eabad153d00b66986018226fe439015884de11c2.svg#users"></use></svg></span> <span class="gl-truncate-end gl-grow gl-text-default">
      Manage
    </span> <span class="gl-text-right gl-text-subtle"><svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg" class="gl-animated-icon gl-animated-icon-off gl-animated-icon-current"><path d="M6.75 4.75L10 8L6.75 11.25" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="gl-animated-chevron-right-down-arrow"></path></svg></span></button> <!----> <div class="gl-m-0 gl-list-none gl-p-0 gl-duration-medium gl-ease-ease collapse" id="manage" data-testid="menu-section" data-qa-section-name="Manage" style="display: none;"><ul aria-label="Manage" class="gl-m-0 gl-list-none gl-p-0"><li data-testid="nav-item" class="show-on-focus-or-hover--context hide-on-focus-or-hover--context transition-opacity-on-hover--context gl-relative"><a href="https://gitlab-v2.innotech-vn.com/the-ai-team/innocody/innocody-engine/activity" class="super-sidebar-nav-item show-on-focus-or-hover--control hide-on-focus-or-hover--control gl-relative gl-mb-1 gl-flex gl-min-h-7 gl-items-center gl-gap-3 gl-py-2 !gl-text-default !gl-no-underline focus:gl-focus gl-px-3 gl-rounded-base shortcuts-project-activity" data-testid="nav-item-link" aria-label="Activity" data-track-action="click_menu_item" data-track-label="activity" data-track-property="nav_panel_project" data-qa-submenu-item="Activity"><div aria-hidden="true" data-testid="active-indicator" class="active-indicator gl-absolute gl-bottom-2 gl-left-2 gl-top-2 gl-transition-all gl-duration-slow gl-opacity-0" style="width: 3px; border-radius: 3px; margin-right: 1px;"></div> <div class="gl-flex gl-w-6 gl-shrink-0"><!----></div> <div data-testid="nav-item-link-label" class="gl-grow gl-text-default gl-break-anywhere">
      Activity
      <!----></div>  <span class="gl-flex gl-min-w-6 gl-items-start gl-justify-end"><!----></span></a> <button aria-label="Pin Activity" data-testid="nav-item-pin" type="button" class="btn show-on-focus-or-hover--target transition-opacity-on-hover--target always-animate gl-absolute gl-right-3 gl-top-1/2 -gl-translate-y-1/2 btn-default btn-sm gl-button btn-default-tertiary btn-icon gl-pointer-events-none"><!----> <svg data-testid="thumbtack-icon" role="img" aria-hidden="true" class="gl-button-icon gl-icon s16 gl-fill-current"><use href="/assets/icons-ca86b3ebff8cbe14f7728864eabad153d00b66986018226fe439015884de11c2.svg#thumbtack"></use></svg>  <!----></button></li><li data-testid="nav-item" class="show-on-focus-or-hover--context hide-on-focus-or-hover--context transition-opacity-on-hover--context gl-relative"><a href="https://gitlab-v2.innotech-vn.com/the-ai-team/innocody/innocody-engine/-/project_members" class="super-sidebar-nav-item show-on-focus-or-hover--control hide-on-focus-or-hover--control gl-relative gl-mb-1 gl-flex gl-min-h-7 gl-items-center gl-gap-3 gl-py-2 !gl-text-default !gl-no-underline focus:gl-focus gl-px-3 gl-rounded-base" data-testid="nav-item-link" aria-label="Members" data-track-action="click_menu_item" data-track-label="members" data-track-property="nav_panel_project" data-qa-submenu-item="Members"><div aria-hidden="true" data-testid="active-indicator" class="active-indicator gl-absolute gl-bottom-2 gl-left-2 gl-top-2 gl-transition-all gl-duration-slow gl-opacity-0" style="width: 3px; border-radius: 3px; margin-right: 1px;"></div> <div class="gl-flex gl-w-6 gl-shrink-0"><!----></div> <div data-testid="nav-item-link-label" class="gl-grow gl-text-default gl-break-anywhere">
      Members
      <!----></div>  <span class="gl-flex gl-min-w-6 gl-items-start gl-justify-end"><!----></span></a> <button aria-label="Pin Members" data-testid="nav-item-pin" type="button" class="btn show-on-focus-or-hover--target transition-opacity-on-hover--target always-animate gl-absolute gl-right-3 gl-top-1/2 -gl-translate-y-1/2 btn-default btn-sm gl-button btn-default-tertiary btn-icon gl-pointer-events-none"><!----> <svg data-testid="thumbtack-icon" role="img" aria-hidden="true" class="gl-button-icon gl-icon s16 gl-fill-current"><use href="/assets/icons-ca86b3ebff8cbe14f7728864eabad153d00b66986018226fe439015884de11c2.svg#thumbtack"></use></svg>  <!----></button></li><li data-testid="nav-item" class="show-on-focus-or-hover--context hide-on-focus-or-hover--context transition-opacity-on-hover--context gl-relative"><a href="https://gitlab-v2.innotech-vn.com/the-ai-team/innocody/innocody-engine/-/labels" class="super-sidebar-nav-item show-on-focus-or-hover--control hide-on-focus-or-hover--control gl-relative gl-mb-1 gl-flex gl-min-h-7 gl-items-center gl-gap-3 gl-py-2 !gl-text-default !gl-no-underline focus:gl-focus gl-px-3 gl-rounded-base" data-testid="nav-item-link" aria-label="Labels" data-track-action="click_menu_item" data-track-label="labels" data-track-property="nav_panel_project" data-qa-submenu-item="Labels"><div aria-hidden="true" data-testid="active-indicator" class="active-indicator gl-absolute gl-bottom-2 gl-left-2 gl-top-2 gl-transition-all gl-duration-slow gl-opacity-0" style="width: 3px; border-radius: 3px; margin-right: 1px;"></div> <div class="gl-flex gl-w-6 gl-shrink-0"><!----></div> <div data-testid="nav-item-link-label" class="gl-grow gl-text-default gl-break-anywhere">
      Labels
      <!----></div>  <span class="gl-flex gl-min-w-6 gl-items-start gl-justify-end"><!----></span></a> <button aria-label="Pin Labels" data-testid="nav-item-pin" type="button" class="btn show-on-focus-or-hover--target transition-opacity-on-hover--target always-animate gl-absolute gl-right-3 gl-top-1/2 -gl-translate-y-1/2 btn-default btn-sm gl-button btn-default-tertiary btn-icon gl-pointer-events-none"><!----> <svg data-testid="thumbtack-icon" role="img" aria-hidden="true" class="gl-button-icon gl-icon s16 gl-fill-current"><use href="/assets/icons-ca86b3ebff8cbe14f7728864eabad153d00b66986018226fe439015884de11c2.svg#thumbtack"></use></svg>  <!----></button></li></ul></div></li><li><!----> <button id="menu-section-button-plan" data-testid="menu-section-button" data-qa-section-name="Plan" aria-controls="plan" aria-expanded="false" data-qa-menu-item="Plan" class="super-sidebar-nav-item gl-relative gl-mb-2 gl-flex gl-min-h-7 gl-w-full gl-appearance-none gl-items-center gl-gap-3 gl-rounded-base gl-border-0 gl-bg-transparent gl-px-3 gl-py-2 gl-text-left !gl-text-default !gl-no-underline focus:gl-focus"><span aria-hidden="true" class="gl-absolute gl-bottom-2 gl-left-2 gl-top-2 gl-transition-all gl-duration-slow gl-bg-transparent" style="width: 3px; border-radius: 3px; margin-right: 1px;"></span> <span class="gl-flex gl-w-6 gl-shrink-0"><svg data-testid="planning-icon" role="img" aria-hidden="true" class="super-sidebar-nav-item-icon gl-m-auto gl-icon s16 gl-fill-current"><use href="/assets/icons-ca86b3ebff8cbe14f7728864eabad153d00b66986018226fe439015884de11c2.svg#planning"></use></svg></span> <span class="gl-truncate-end gl-grow gl-text-default">
      Plan
    </span> <span class="gl-text-right gl-text-subtle"><svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg" class="gl-animated-icon gl-animated-icon-off gl-animated-icon-current"><path d="M6.75 4.75L10 8L6.75 11.25" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="gl-animated-chevron-right-down-arrow"></path></svg></span></button> <!----> <div class="gl-m-0 gl-list-none gl-p-0 gl-duration-medium gl-ease-ease collapse" id="plan" data-testid="menu-section" data-qa-section-name="Plan" style="display: none;"><ul aria-label="Plan" class="gl-m-0 gl-list-none gl-p-0"><li data-testid="nav-item" class="show-on-focus-or-hover--context hide-on-focus-or-hover--context transition-opacity-on-hover--context gl-relative"><a href="https://gitlab-v2.innotech-vn.com/the-ai-team/innocody/innocody-engine/-/issues" class="super-sidebar-nav-item show-on-focus-or-hover--control hide-on-focus-or-hover--control gl-relative gl-mb-1 gl-flex gl-min-h-7 gl-items-center gl-gap-3 gl-py-2 !gl-text-default !gl-no-underline focus:gl-focus gl-px-3 gl-rounded-base shortcuts-issues has-sub-items" data-testid="nav-item-link" aria-label="Issues" data-track-action="click_menu_item" data-track-label="project_issue_list" data-track-property="nav_panel_project" data-qa-submenu-item="Issues"><div aria-hidden="true" data-testid="active-indicator" class="active-indicator gl-absolute gl-bottom-2 gl-left-2 gl-top-2 gl-transition-all gl-duration-slow gl-opacity-0" style="width: 3px; border-radius: 3px; margin-right: 1px;"></div> <div class="gl-flex gl-w-6 gl-shrink-0"><!----></div> <div data-testid="nav-item-link-label" class="gl-grow gl-text-default gl-break-anywhere">
      Issues
      <!----></div>  <span class="gl-flex gl-min-w-6 gl-items-start gl-justify-end"><span class="gl-badge badge badge-pill badge-neutral hide-on-focus-or-hover--target transition-opacity-on-hover--target"><!----> <span class="gl-badge-content">
        0
      </span></span></span></a> <button aria-label="Unpin Issues" data-testid="nav-item-unpin" type="button" class="btn show-on-focus-or-hover--target transition-opacity-on-hover--target always-animate gl-absolute gl-right-3 gl-top-1/2 -gl-translate-y-1/2 btn-default btn-sm gl-button btn-default-tertiary btn-icon gl-pointer-events-none"><!----> <svg data-testid="thumbtack-solid-icon" role="img" aria-hidden="true" class="gl-button-icon gl-icon s16 gl-fill-current"><use href="/assets/icons-ca86b3ebff8cbe14f7728864eabad153d00b66986018226fe439015884de11c2.svg#thumbtack-solid"></use></svg>  <!----></button></li><li data-testid="nav-item" class="show-on-focus-or-hover--context hide-on-focus-or-hover--context transition-opacity-on-hover--context gl-relative"><a href="https://gitlab-v2.innotech-vn.com/the-ai-team/innocody/innocody-engine/-/boards" class="super-sidebar-nav-item show-on-focus-or-hover--control hide-on-focus-or-hover--control gl-relative gl-mb-1 gl-flex gl-min-h-7 gl-items-center gl-gap-3 gl-py-2 !gl-text-default !gl-no-underline focus:gl-focus gl-px-3 gl-rounded-base shortcuts-issue-boards" data-testid="nav-item-link" aria-label="Issue boards" data-track-action="click_menu_item" data-track-label="boards" data-track-property="nav_panel_project" data-qa-submenu-item="Issue boards"><div aria-hidden="true" data-testid="active-indicator" class="active-indicator gl-absolute gl-bottom-2 gl-left-2 gl-top-2 gl-transition-all gl-duration-slow gl-opacity-0" style="width: 3px; border-radius: 3px; margin-right: 1px;"></div> <div class="gl-flex gl-w-6 gl-shrink-0"><!----></div> <div data-testid="nav-item-link-label" class="gl-grow gl-text-default gl-break-anywhere">
      Issue boards
      <!----></div>  <span class="gl-flex gl-min-w-6 gl-items-start gl-justify-end"><!----></span></a> <button aria-label="Pin Issue boards" data-testid="nav-item-pin" type="button" class="btn show-on-focus-or-hover--target transition-opacity-on-hover--target always-animate gl-absolute gl-right-3 gl-top-1/2 -gl-translate-y-1/2 btn-default btn-sm gl-button btn-default-tertiary btn-icon gl-pointer-events-none"><!----> <svg data-testid="thumbtack-icon" role="img" aria-hidden="true" class="gl-button-icon gl-icon s16 gl-fill-current"><use href="/assets/icons-ca86b3ebff8cbe14f7728864eabad153d00b66986018226fe439015884de11c2.svg#thumbtack"></use></svg>  <!----></button></li><li data-testid="nav-item" class="show-on-focus-or-hover--context hide-on-focus-or-hover--context transition-opacity-on-hover--context gl-relative"><a href="https://gitlab-v2.innotech-vn.com/the-ai-team/innocody/innocody-engine/-/milestones" class="super-sidebar-nav-item show-on-focus-or-hover--control hide-on-focus-or-hover--control gl-relative gl-mb-1 gl-flex gl-min-h-7 gl-items-center gl-gap-3 gl-py-2 !gl-text-default !gl-no-underline focus:gl-focus gl-px-3 gl-rounded-base" data-testid="nav-item-link" aria-label="Milestones" data-track-action="click_menu_item" data-track-label="milestones" data-track-property="nav_panel_project" data-qa-submenu-item="Milestones"><div aria-hidden="true" data-testid="active-indicator" class="active-indicator gl-absolute gl-bottom-2 gl-left-2 gl-top-2 gl-transition-all gl-duration-slow gl-opacity-0" style="width: 3px; border-radius: 3px; margin-right: 1px;"></div> <div class="gl-flex gl-w-6 gl-shrink-0"><!----></div> <div data-testid="nav-item-link-label" class="gl-grow gl-text-default gl-break-anywhere">
      Milestones
      <!----></div>  <span class="gl-flex gl-min-w-6 gl-items-start gl-justify-end"><!----></span></a> <button aria-label="Pin Milestones" data-testid="nav-item-pin" type="button" class="btn show-on-focus-or-hover--target transition-opacity-on-hover--target always-animate gl-absolute gl-right-3 gl-top-1/2 -gl-translate-y-1/2 btn-default btn-sm gl-button btn-default-tertiary btn-icon gl-pointer-events-none"><!----> <svg data-testid="thumbtack-icon" role="img" aria-hidden="true" class="gl-button-icon gl-icon s16 gl-fill-current"><use href="/assets/icons-ca86b3ebff8cbe14f7728864eabad153d00b66986018226fe439015884de11c2.svg#thumbtack"></use></svg>  <!----></button></li><li data-testid="nav-item" class="show-on-focus-or-hover--context hide-on-focus-or-hover--context transition-opacity-on-hover--context gl-relative"><a href="https://gitlab-v2.innotech-vn.com/the-ai-team/innocody/innocody-engine/-/wikis/home" class="super-sidebar-nav-item show-on-focus-or-hover--control hide-on-focus-or-hover--control gl-relative gl-mb-1 gl-flex gl-min-h-7 gl-items-center gl-gap-3 gl-py-2 !gl-text-default !gl-no-underline focus:gl-focus gl-px-3 gl-rounded-base shortcuts-wiki" data-testid="nav-item-link" aria-label="Wiki" data-track-action="click_menu_item" data-track-label="project_wiki" data-track-property="nav_panel_project" data-qa-submenu-item="Wiki"><div aria-hidden="true" data-testid="active-indicator" class="active-indicator gl-absolute gl-bottom-2 gl-left-2 gl-top-2 gl-transition-all gl-duration-slow gl-opacity-0" style="width: 3px; border-radius: 3px; margin-right: 1px;"></div> <div class="gl-flex gl-w-6 gl-shrink-0"><!----></div> <div data-testid="nav-item-link-label" class="gl-grow gl-text-default gl-break-anywhere">
      Wiki
      <!----></div>  <span class="gl-flex gl-min-w-6 gl-items-start gl-justify-end"><!----></span></a> <button aria-label="Pin Wiki" data-testid="nav-item-pin" type="button" class="btn show-on-focus-or-hover--target transition-opacity-on-hover--target always-animate gl-absolute gl-right-3 gl-top-1/2 -gl-translate-y-1/2 btn-default btn-sm gl-button btn-default-tertiary btn-icon gl-pointer-events-none"><!----> <svg data-testid="thumbtack-icon" role="img" aria-hidden="true" class="gl-button-icon gl-icon s16 gl-fill-current"><use href="/assets/icons-ca86b3ebff8cbe14f7728864eabad153d00b66986018226fe439015884de11c2.svg#thumbtack"></use></svg>  <!----></button></li></ul></div></li><li><!----> <button id="menu-section-button-code" data-testid="menu-section-button" data-qa-section-name="Code" aria-controls="code" aria-expanded="true" data-qa-menu-item="Code" class="super-sidebar-nav-item gl-relative gl-mb-2 gl-flex gl-min-h-7 gl-w-full gl-appearance-none gl-items-center gl-gap-3 gl-rounded-base gl-border-0 gl-bg-transparent gl-px-3 gl-py-2 gl-text-left !gl-text-default !gl-no-underline focus:gl-focus"><span aria-hidden="true" class="gl-absolute gl-bottom-2 gl-left-2 gl-top-2 gl-transition-all gl-duration-slow gl-bg-transparent" style="width: 3px; border-radius: 3px; margin-right: 1px;"></span> <span class="gl-flex gl-w-6 gl-shrink-0"><svg data-testid="code-icon" role="img" aria-hidden="true" class="super-sidebar-nav-item-icon gl-m-auto gl-icon s16 gl-fill-current"><use href="/assets/icons-ca86b3ebff8cbe14f7728864eabad153d00b66986018226fe439015884de11c2.svg#code"></use></svg></span> <span class="gl-truncate-end gl-grow gl-text-default">
      Code
    </span> <span class="gl-text-right gl-text-subtle"><svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg" class="gl-animated-icon gl-animated-icon-on gl-animated-icon-current"><path d="M6.75 4.75L10 8L6.75 11.25" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="gl-animated-chevron-right-down-arrow"></path></svg></span></button> <!----> <div class="gl-m-0 gl-list-none gl-p-0 gl-duration-medium gl-ease-ease collapse show" id="code" data-testid="menu-section" data-qa-section-name="Code"><ul aria-label="Code" class="gl-m-0 gl-list-none gl-p-0"><li data-testid="nav-item" class="show-on-focus-or-hover--context hide-on-focus-or-hover--context transition-opacity-on-hover--context gl-relative"><a href="https://gitlab-v2.innotech-vn.com/the-ai-team/innocody/innocody-engine/-/merge_requests" class="super-sidebar-nav-item show-on-focus-or-hover--control hide-on-focus-or-hover--control gl-relative gl-mb-1 gl-flex gl-min-h-7 gl-items-center gl-gap-3 gl-py-2 !gl-text-default !gl-no-underline focus:gl-focus gl-px-3 gl-rounded-base shortcuts-merge_requests" data-testid="nav-item-link" aria-label="Merge requests" data-track-action="click_menu_item" data-track-label="project_merge_request_list" data-track-property="nav_panel_project" data-qa-submenu-item="Merge requests"><div aria-hidden="true" data-testid="active-indicator" class="active-indicator gl-absolute gl-bottom-2 gl-left-2 gl-top-2 gl-transition-all gl-duration-slow gl-opacity-0" style="width: 3px; border-radius: 3px; margin-right: 1px;"></div> <div class="gl-flex gl-w-6 gl-shrink-0"><!----></div> <div data-testid="nav-item-link-label" class="gl-grow gl-text-default gl-break-anywhere">
      Merge requests
      <!----></div>  <span class="gl-flex gl-min-w-6 gl-items-start gl-justify-end"><span class="gl-badge badge badge-pill badge-neutral hide-on-focus-or-hover--target transition-opacity-on-hover--target"><!----> <span class="gl-badge-content">
        1
      </span></span></span></a> <button aria-label="Unpin Merge requests" data-testid="nav-item-unpin" type="button" class="btn show-on-focus-or-hover--target transition-opacity-on-hover--target always-animate gl-absolute gl-right-3 gl-top-1/2 -gl-translate-y-1/2 btn-default btn-sm gl-button btn-default-tertiary btn-icon gl-pointer-events-none"><!----> <svg data-testid="thumbtack-solid-icon" role="img" aria-hidden="true" class="gl-button-icon gl-icon s16 gl-fill-current"><use href="/assets/icons-ca86b3ebff8cbe14f7728864eabad153d00b66986018226fe439015884de11c2.svg#thumbtack-solid"></use></svg>  <!----></button></li><li data-testid="nav-item" class="show-on-focus-or-hover--context hide-on-focus-or-hover--context transition-opacity-on-hover--context gl-relative"><a href="https://gitlab-v2.innotech-vn.com/the-ai-team/innocody/innocody-engine/-/tree/main" class="super-sidebar-nav-item show-on-focus-or-hover--control hide-on-focus-or-hover--control gl-relative gl-mb-1 gl-flex gl-min-h-7 gl-items-center gl-gap-3 gl-py-2 !gl-text-default !gl-no-underline focus:gl-focus gl-px-3 gl-rounded-base shortcuts-tree" data-testid="nav-item-link" aria-label="Repository" data-track-action="click_menu_item" data-track-label="files" data-track-property="nav_panel_project" data-qa-submenu-item="Repository"><div aria-hidden="true" data-testid="active-indicator" class="active-indicator gl-absolute gl-bottom-2 gl-left-2 gl-top-2 gl-transition-all gl-duration-slow gl-opacity-0" style="width: 3px; border-radius: 3px; margin-right: 1px;"></div> <div class="gl-flex gl-w-6 gl-shrink-0"><!----></div> <div data-testid="nav-item-link-label" class="gl-grow gl-text-default gl-break-anywhere">
      Repository
      <!----></div>  <span class="gl-flex gl-min-w-6 gl-items-start gl-justify-end"><!----></span></a> <button aria-label="Pin Repository" data-testid="nav-item-pin" type="button" class="btn show-on-focus-or-hover--target transition-opacity-on-hover--target always-animate gl-absolute gl-right-3 gl-top-1/2 -gl-translate-y-1/2 btn-default btn-sm gl-button btn-default-tertiary btn-icon gl-pointer-events-none"><!----> <svg data-testid="thumbtack-icon" role="img" aria-hidden="true" class="gl-button-icon gl-icon s16 gl-fill-current"><use href="/assets/icons-ca86b3ebff8cbe14f7728864eabad153d00b66986018226fe439015884de11c2.svg#thumbtack"></use></svg>  <!----></button></li><li data-testid="nav-item" class="show-on-focus-or-hover--context hide-on-focus-or-hover--context transition-opacity-on-hover--context gl-relative"><a href="https://gitlab-v2.innotech-vn.com/the-ai-team/innocody/innocody-engine/-/branches" class="super-sidebar-nav-item show-on-focus-or-hover--control hide-on-focus-or-hover--control gl-relative gl-mb-1 gl-flex gl-min-h-7 gl-items-center gl-gap-3 gl-py-2 !gl-text-default !gl-no-underline focus:gl-focus gl-px-3 gl-rounded-base" data-testid="nav-item-link" aria-label="Branches" data-track-action="click_menu_item" data-track-label="branches" data-track-property="nav_panel_project" data-qa-submenu-item="Branches"><div aria-hidden="true" data-testid="active-indicator" class="active-indicator gl-absolute gl-bottom-2 gl-left-2 gl-top-2 gl-transition-all gl-duration-slow gl-opacity-0" style="width: 3px; border-radius: 3px; margin-right: 1px;"></div> <div class="gl-flex gl-w-6 gl-shrink-0"><!----></div> <div data-testid="nav-item-link-label" class="gl-grow gl-text-default gl-break-anywhere">
      Branches
      <!----></div>  <span class="gl-flex gl-min-w-6 gl-items-start gl-justify-end"><!----></span></a> <button aria-label="Pin Branches" data-testid="nav-item-pin" type="button" class="btn show-on-focus-or-hover--target transition-opacity-on-hover--target always-animate gl-absolute gl-right-3 gl-top-1/2 -gl-translate-y-1/2 btn-default btn-sm gl-button btn-default-tertiary btn-icon gl-pointer-events-none"><!----> <svg data-testid="thumbtack-icon" role="img" aria-hidden="true" class="gl-button-icon gl-icon s16 gl-fill-current"><use href="/assets/icons-ca86b3ebff8cbe14f7728864eabad153d00b66986018226fe439015884de11c2.svg#thumbtack"></use></svg>  <!----></button></li><li data-testid="nav-item" class="show-on-focus-or-hover--context hide-on-focus-or-hover--context transition-opacity-on-hover--context gl-relative"><a href="https://gitlab-v2.innotech-vn.com/the-ai-team/innocody/innocody-engine/-/commits/main?ref_type=heads" aria-current="page" class="super-sidebar-nav-item show-on-focus-or-hover--control hide-on-focus-or-hover--control gl-relative gl-mb-1 gl-flex gl-min-h-7 gl-items-center gl-gap-3 gl-py-2 !gl-text-default !gl-no-underline focus:gl-focus super-sidebar-nav-item-current gl-px-3 gl-rounded-base shortcuts-commits" data-testid="nav-item-link" aria-label="Commits" data-track-action="click_menu_item" data-track-label="commits" data-track-property="nav_panel_project" data-qa-submenu-item="Commits"><div aria-hidden="true" data-testid="active-indicator" class="active-indicator gl-absolute gl-bottom-2 gl-left-2 gl-top-2 gl-transition-all gl-duration-slow gl-opacity-10" style="width: 3px; border-radius: 3px; margin-right: 1px;"></div> <div class="gl-flex gl-w-6 gl-shrink-0"><!----></div> <div data-testid="nav-item-link-label" class="gl-grow gl-text-default gl-break-anywhere">
      Commits
      <!----></div>  <span class="gl-flex gl-min-w-6 gl-items-start gl-justify-end"><!----></span></a> <button aria-label="Pin Commits" data-testid="nav-item-pin" type="button" class="btn show-on-focus-or-hover--target transition-opacity-on-hover--target always-animate gl-absolute gl-right-3 gl-top-1/2 -gl-translate-y-1/2 btn-default btn-sm gl-button btn-default-tertiary btn-icon gl-pointer-events-none"><!----> <svg data-testid="thumbtack-icon" role="img" aria-hidden="true" class="gl-button-icon gl-icon s16 gl-fill-current"><use href="/assets/icons-ca86b3ebff8cbe14f7728864eabad153d00b66986018226fe439015884de11c2.svg#thumbtack"></use></svg>  <!----></button></li><li data-testid="nav-item" class="show-on-focus-or-hover--context hide-on-focus-or-hover--context transition-opacity-on-hover--context gl-relative"><a href="https://gitlab-v2.innotech-vn.com/the-ai-team/innocody/innocody-engine/-/tags" class="super-sidebar-nav-item show-on-focus-or-hover--control hide-on-focus-or-hover--control gl-relative gl-mb-1 gl-flex gl-min-h-7 gl-items-center gl-gap-3 gl-py-2 !gl-text-default !gl-no-underline focus:gl-focus gl-px-3 gl-rounded-base" data-testid="nav-item-link" aria-label="Tags" data-track-action="click_menu_item" data-track-label="tags" data-track-property="nav_panel_project" data-qa-submenu-item="Tags"><div aria-hidden="true" data-testid="active-indicator" class="active-indicator gl-absolute gl-bottom-2 gl-left-2 gl-top-2 gl-transition-all gl-duration-slow gl-opacity-0" style="width: 3px; border-radius: 3px; margin-right: 1px;"></div> <div class="gl-flex gl-w-6 gl-shrink-0"><!----></div> <div data-testid="nav-item-link-label" class="gl-grow gl-text-default gl-break-anywhere">
      Tags
      <!----></div>  <span class="gl-flex gl-min-w-6 gl-items-start gl-justify-end"><!----></span></a> <button aria-label="Pin Tags" data-testid="nav-item-pin" type="button" class="btn show-on-focus-or-hover--target transition-opacity-on-hover--target always-animate gl-absolute gl-right-3 gl-top-1/2 -gl-translate-y-1/2 btn-default btn-sm gl-button btn-default-tertiary btn-icon gl-pointer-events-none"><!----> <svg data-testid="thumbtack-icon" role="img" aria-hidden="true" class="gl-button-icon gl-icon s16 gl-fill-current"><use href="/assets/icons-ca86b3ebff8cbe14f7728864eabad153d00b66986018226fe439015884de11c2.svg#thumbtack"></use></svg>  <!----></button></li><li data-testid="nav-item" class="show-on-focus-or-hover--context hide-on-focus-or-hover--context transition-opacity-on-hover--context gl-relative"><a href="https://gitlab-v2.innotech-vn.com/the-ai-team/innocody/innocody-engine/-/network/main?ref_type=heads" class="super-sidebar-nav-item show-on-focus-or-hover--control hide-on-focus-or-hover--control gl-relative gl-mb-1 gl-flex gl-min-h-7 gl-items-center gl-gap-3 gl-py-2 !gl-text-default !gl-no-underline focus:gl-focus gl-px-3 gl-rounded-base shortcuts-network" data-testid="nav-item-link" aria-label="Repository graph" data-track-action="click_menu_item" data-track-label="graphs" data-track-property="nav_panel_project" data-qa-submenu-item="Repository graph"><div aria-hidden="true" data-testid="active-indicator" class="active-indicator gl-absolute gl-bottom-2 gl-left-2 gl-top-2 gl-transition-all gl-duration-slow gl-opacity-0" style="width: 3px; border-radius: 3px; margin-right: 1px;"></div> <div class="gl-flex gl-w-6 gl-shrink-0"><!----></div> <div data-testid="nav-item-link-label" class="gl-grow gl-text-default gl-break-anywhere">
      Repository graph
      <!----></div>  <span class="gl-flex gl-min-w-6 gl-items-start gl-justify-end"><!----></span></a> <button aria-label="Pin Repository graph" data-testid="nav-item-pin" type="button" class="btn show-on-focus-or-hover--target transition-opacity-on-hover--target always-animate gl-absolute gl-right-3 gl-top-1/2 -gl-translate-y-1/2 btn-default btn-sm gl-button btn-default-tertiary btn-icon gl-pointer-events-none"><!----> <svg data-testid="thumbtack-icon" role="img" aria-hidden="true" class="gl-button-icon gl-icon s16 gl-fill-current"><use href="/assets/icons-ca86b3ebff8cbe14f7728864eabad153d00b66986018226fe439015884de11c2.svg#thumbtack"></use></svg>  <!----></button></li><li data-testid="nav-item" class="show-on-focus-or-hover--context hide-on-focus-or-hover--context transition-opacity-on-hover--context gl-relative"><a href="https://gitlab-v2.innotech-vn.com/the-ai-team/innocody/innocody-engine/-/compare?from=main&amp;to=main" class="super-sidebar-nav-item show-on-focus-or-hover--control hide-on-focus-or-hover--control gl-relative gl-mb-1 gl-flex gl-min-h-7 gl-items-center gl-gap-3 gl-py-2 !gl-text-default !gl-no-underline focus:gl-focus gl-px-3 gl-rounded-base" data-testid="nav-item-link" aria-label="Compare revisions" data-track-action="click_menu_item" data-track-label="compare" data-track-property="nav_panel_project" data-qa-submenu-item="Compare revisions"><div aria-hidden="true" data-testid="active-indicator" class="active-indicator gl-absolute gl-bottom-2 gl-left-2 gl-top-2 gl-transition-all gl-duration-slow gl-opacity-0" style="width: 3px; border-radius: 3px; margin-right: 1px;"></div> <div class="gl-flex gl-w-6 gl-shrink-0"><!----></div> <div data-testid="nav-item-link-label" class="gl-grow gl-text-default gl-break-anywhere">
      Compare revisions
      <!----></div>  <span class="gl-flex gl-min-w-6 gl-items-start gl-justify-end"><!----></span></a> <button aria-label="Pin Compare revisions" data-testid="nav-item-pin" type="button" class="btn show-on-focus-or-hover--target transition-opacity-on-hover--target always-animate gl-absolute gl-right-3 gl-top-1/2 -gl-translate-y-1/2 btn-default btn-sm gl-button btn-default-tertiary btn-icon gl-pointer-events-none"><!----> <svg data-testid="thumbtack-icon" role="img" aria-hidden="true" class="gl-button-icon gl-icon s16 gl-fill-current"><use href="/assets/icons-ca86b3ebff8cbe14f7728864eabad153d00b66986018226fe439015884de11c2.svg#thumbtack"></use></svg>  <!----></button></li><li data-testid="nav-item" class="show-on-focus-or-hover--context hide-on-focus-or-hover--context transition-opacity-on-hover--context gl-relative"><a href="https://gitlab-v2.innotech-vn.com/the-ai-team/innocody/innocody-engine/-/snippets" class="super-sidebar-nav-item show-on-focus-or-hover--control hide-on-focus-or-hover--control gl-relative gl-mb-1 gl-flex gl-min-h-7 gl-items-center gl-gap-3 gl-py-2 !gl-text-default !gl-no-underline focus:gl-focus gl-px-3 gl-rounded-base shortcuts-snippets" data-testid="nav-item-link" aria-label="Snippets" data-track-action="click_menu_item" data-track-label="project_snippets" data-track-property="nav_panel_project" data-qa-submenu-item="Snippets"><div aria-hidden="true" data-testid="active-indicator" class="active-indicator gl-absolute gl-bottom-2 gl-left-2 gl-top-2 gl-transition-all gl-duration-slow gl-opacity-0" style="width: 3px; border-radius: 3px; margin-right: 1px;"></div> <div class="gl-flex gl-w-6 gl-shrink-0"><!----></div> <div data-testid="nav-item-link-label" class="gl-grow gl-text-default gl-break-anywhere">
      Snippets
      <!----></div>  <span class="gl-flex gl-min-w-6 gl-items-start gl-justify-end"><!----></span></a> <button aria-label="Pin Snippets" data-testid="nav-item-pin" type="button" class="btn show-on-focus-or-hover--target transition-opacity-on-hover--target always-animate gl-absolute gl-right-3 gl-top-1/2 -gl-translate-y-1/2 btn-default btn-sm gl-button btn-default-tertiary btn-icon gl-pointer-events-none"><!----> <svg data-testid="thumbtack-icon" role="img" aria-hidden="true" class="gl-button-icon gl-icon s16 gl-fill-current"><use href="/assets/icons-ca86b3ebff8cbe14f7728864eabad153d00b66986018226fe439015884de11c2.svg#thumbtack"></use></svg>  <!----></button></li></ul></div></li><li><!----> <button id="menu-section-button-build" data-testid="menu-section-button" data-qa-section-name="Build" aria-controls="build" aria-expanded="false" data-qa-menu-item="Build" class="super-sidebar-nav-item gl-relative gl-mb-2 gl-flex gl-min-h-7 gl-w-full gl-appearance-none gl-items-center gl-gap-3 gl-rounded-base gl-border-0 gl-bg-transparent gl-px-3 gl-py-2 gl-text-left !gl-text-default !gl-no-underline focus:gl-focus"><span aria-hidden="true" class="gl-absolute gl-bottom-2 gl-left-2 gl-top-2 gl-transition-all gl-duration-slow gl-bg-transparent" style="width: 3px; border-radius: 3px; margin-right: 1px;"></span> <span class="gl-flex gl-w-6 gl-shrink-0"><svg data-testid="rocket-icon" role="img" aria-hidden="true" class="super-sidebar-nav-item-icon gl-m-auto gl-icon s16 gl-fill-current"><use href="/assets/icons-ca86b3ebff8cbe14f7728864eabad153d00b66986018226fe439015884de11c2.svg#rocket"></use></svg></span> <span class="gl-truncate-end gl-grow gl-text-default">
      Build
    </span> <span class="gl-text-right gl-text-subtle"><svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg" class="gl-animated-icon gl-animated-icon-off gl-animated-icon-current"><path d="M6.75 4.75L10 8L6.75 11.25" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="gl-animated-chevron-right-down-arrow"></path></svg></span></button> <!----> <div class="gl-m-0 gl-list-none gl-p-0 gl-duration-medium gl-ease-ease collapse" id="build" data-testid="menu-section" data-qa-section-name="Build" style="display: none;"><ul aria-label="Build" class="gl-m-0 gl-list-none gl-p-0"><li data-testid="nav-item" class="show-on-focus-or-hover--context hide-on-focus-or-hover--context transition-opacity-on-hover--context gl-relative"><a href="https://gitlab-v2.innotech-vn.com/the-ai-team/innocody/innocody-engine/-/pipelines" class="super-sidebar-nav-item show-on-focus-or-hover--control hide-on-focus-or-hover--control gl-relative gl-mb-1 gl-flex gl-min-h-7 gl-items-center gl-gap-3 gl-py-2 !gl-text-default !gl-no-underline focus:gl-focus gl-px-3 gl-rounded-base shortcuts-pipelines" data-testid="nav-item-link" aria-label="Pipelines" data-track-action="click_menu_item" data-track-label="pipelines" data-track-property="nav_panel_project" data-qa-submenu-item="Pipelines"><div aria-hidden="true" data-testid="active-indicator" class="active-indicator gl-absolute gl-bottom-2 gl-left-2 gl-top-2 gl-transition-all gl-duration-slow gl-opacity-0" style="width: 3px; border-radius: 3px; margin-right: 1px;"></div> <div class="gl-flex gl-w-6 gl-shrink-0"><!----></div> <div data-testid="nav-item-link-label" class="gl-grow gl-text-default gl-break-anywhere">
      Pipelines
      <!----></div>  <span class="gl-flex gl-min-w-6 gl-items-start gl-justify-end"><!----></span></a> <button aria-label="Unpin Pipelines" data-testid="nav-item-unpin" type="button" class="btn show-on-focus-or-hover--target transition-opacity-on-hover--target always-animate gl-absolute gl-right-3 gl-top-1/2 -gl-translate-y-1/2 btn-default btn-sm gl-button btn-default-tertiary btn-icon gl-pointer-events-none"><!----> <svg data-testid="thumbtack-solid-icon" role="img" aria-hidden="true" class="gl-button-icon gl-icon s16 gl-fill-current"><use href="/assets/icons-ca86b3ebff8cbe14f7728864eabad153d00b66986018226fe439015884de11c2.svg#thumbtack-solid"></use></svg>  <!----></button></li><li data-testid="nav-item" class="show-on-focus-or-hover--context hide-on-focus-or-hover--context transition-opacity-on-hover--context gl-relative"><a href="https://gitlab-v2.innotech-vn.com/the-ai-team/innocody/innocody-engine/-/jobs" class="super-sidebar-nav-item show-on-focus-or-hover--control hide-on-focus-or-hover--control gl-relative gl-mb-1 gl-flex gl-min-h-7 gl-items-center gl-gap-3 gl-py-2 !gl-text-default !gl-no-underline focus:gl-focus gl-px-3 gl-rounded-base shortcuts-builds" data-testid="nav-item-link" aria-label="Jobs" data-track-action="click_menu_item" data-track-label="jobs" data-track-property="nav_panel_project" data-qa-submenu-item="Jobs"><div aria-hidden="true" data-testid="active-indicator" class="active-indicator gl-absolute gl-bottom-2 gl-left-2 gl-top-2 gl-transition-all gl-duration-slow gl-opacity-0" style="width: 3px; border-radius: 3px; margin-right: 1px;"></div> <div class="gl-flex gl-w-6 gl-shrink-0"><!----></div> <div data-testid="nav-item-link-label" class="gl-grow gl-text-default gl-break-anywhere">
      Jobs
      <!----></div>  <span class="gl-flex gl-min-w-6 gl-items-start gl-justify-end"><!----></span></a> <button aria-label="Pin Jobs" data-testid="nav-item-pin" type="button" class="btn show-on-focus-or-hover--target transition-opacity-on-hover--target always-animate gl-absolute gl-right-3 gl-top-1/2 -gl-translate-y-1/2 btn-default btn-sm gl-button btn-default-tertiary btn-icon gl-pointer-events-none"><!----> <svg data-testid="thumbtack-icon" role="img" aria-hidden="true" class="gl-button-icon gl-icon s16 gl-fill-current"><use href="/assets/icons-ca86b3ebff8cbe14f7728864eabad153d00b66986018226fe439015884de11c2.svg#thumbtack"></use></svg>  <!----></button></li><li data-testid="nav-item" class="show-on-focus-or-hover--context hide-on-focus-or-hover--context transition-opacity-on-hover--context gl-relative"><a href="https://gitlab-v2.innotech-vn.com/the-ai-team/innocody/innocody-engine/-/ci/editor?branch_name=main" class="super-sidebar-nav-item show-on-focus-or-hover--control hide-on-focus-or-hover--control gl-relative gl-mb-1 gl-flex gl-min-h-7 gl-items-center gl-gap-3 gl-py-2 !gl-text-default !gl-no-underline focus:gl-focus gl-px-3 gl-rounded-base" data-testid="nav-item-link" aria-label="Pipeline editor" data-track-action="click_menu_item" data-track-label="pipelines_editor" data-track-property="nav_panel_project" data-qa-submenu-item="Pipeline editor"><div aria-hidden="true" data-testid="active-indicator" class="active-indicator gl-absolute gl-bottom-2 gl-left-2 gl-top-2 gl-transition-all gl-duration-slow gl-opacity-0" style="width: 3px; border-radius: 3px; margin-right: 1px;"></div> <div class="gl-flex gl-w-6 gl-shrink-0"><!----></div> <div data-testid="nav-item-link-label" class="gl-grow gl-text-default gl-break-anywhere">
      Pipeline editor
      <!----></div>  <span class="gl-flex gl-min-w-6 gl-items-start gl-justify-end"><!----></span></a> <button aria-label="Pin Pipeline editor" data-testid="nav-item-pin" type="button" class="btn show-on-focus-or-hover--target transition-opacity-on-hover--target always-animate gl-absolute gl-right-3 gl-top-1/2 -gl-translate-y-1/2 btn-default btn-sm gl-button btn-default-tertiary btn-icon gl-pointer-events-none"><!----> <svg data-testid="thumbtack-icon" role="img" aria-hidden="true" class="gl-button-icon gl-icon s16 gl-fill-current"><use href="/assets/icons-ca86b3ebff8cbe14f7728864eabad153d00b66986018226fe439015884de11c2.svg#thumbtack"></use></svg>  <!----></button></li><li data-testid="nav-item" class="show-on-focus-or-hover--context hide-on-focus-or-hover--context transition-opacity-on-hover--context gl-relative"><a href="https://gitlab-v2.innotech-vn.com/the-ai-team/innocody/innocody-engine/-/pipeline_schedules" class="super-sidebar-nav-item show-on-focus-or-hover--control hide-on-focus-or-hover--control gl-relative gl-mb-1 gl-flex gl-min-h-7 gl-items-center gl-gap-3 gl-py-2 !gl-text-default !gl-no-underline focus:gl-focus gl-px-3 gl-rounded-base shortcuts-builds" data-testid="nav-item-link" aria-label="Pipeline schedules" data-track-action="click_menu_item" data-track-label="pipeline_schedules" data-track-property="nav_panel_project" data-qa-submenu-item="Pipeline schedules"><div aria-hidden="true" data-testid="active-indicator" class="active-indicator gl-absolute gl-bottom-2 gl-left-2 gl-top-2 gl-transition-all gl-duration-slow gl-opacity-0" style="width: 3px; border-radius: 3px; margin-right: 1px;"></div> <div class="gl-flex gl-w-6 gl-shrink-0"><!----></div> <div data-testid="nav-item-link-label" class="gl-grow gl-text-default gl-break-anywhere">
      Pipeline schedules
      <!----></div>  <span class="gl-flex gl-min-w-6 gl-items-start gl-justify-end"><!----></span></a> <button aria-label="Pin Pipeline schedules" data-testid="nav-item-pin" type="button" class="btn show-on-focus-or-hover--target transition-opacity-on-hover--target always-animate gl-absolute gl-right-3 gl-top-1/2 -gl-translate-y-1/2 btn-default btn-sm gl-button btn-default-tertiary btn-icon gl-pointer-events-none"><!----> <svg data-testid="thumbtack-icon" role="img" aria-hidden="true" class="gl-button-icon gl-icon s16 gl-fill-current"><use href="/assets/icons-ca86b3ebff8cbe14f7728864eabad153d00b66986018226fe439015884de11c2.svg#thumbtack"></use></svg>  <!----></button></li><li data-testid="nav-item" class="show-on-focus-or-hover--context hide-on-focus-or-hover--context transition-opacity-on-hover--context gl-relative"><a href="https://gitlab-v2.innotech-vn.com/the-ai-team/innocody/innocody-engine/-/artifacts" class="super-sidebar-nav-item show-on-focus-or-hover--control hide-on-focus-or-hover--control gl-relative gl-mb-1 gl-flex gl-min-h-7 gl-items-center gl-gap-3 gl-py-2 !gl-text-default !gl-no-underline focus:gl-focus gl-px-3 gl-rounded-base shortcuts-builds" data-testid="nav-item-link" aria-label="Artifacts" data-track-action="click_menu_item" data-track-label="artifacts" data-track-property="nav_panel_project" data-qa-submenu-item="Artifacts"><div aria-hidden="true" data-testid="active-indicator" class="active-indicator gl-absolute gl-bottom-2 gl-left-2 gl-top-2 gl-transition-all gl-duration-slow gl-opacity-0" style="width: 3px; border-radius: 3px; margin-right: 1px;"></div> <div class="gl-flex gl-w-6 gl-shrink-0"><!----></div> <div data-testid="nav-item-link-label" class="gl-grow gl-text-default gl-break-anywhere">
      Artifacts
      <!----></div>  <span class="gl-flex gl-min-w-6 gl-items-start gl-justify-end"><!----></span></a> <button aria-label="Pin Artifacts" data-testid="nav-item-pin" type="button" class="btn show-on-focus-or-hover--target transition-opacity-on-hover--target always-animate gl-absolute gl-right-3 gl-top-1/2 -gl-translate-y-1/2 btn-default btn-sm gl-button btn-default-tertiary btn-icon gl-pointer-events-none"><!----> <svg data-testid="thumbtack-icon" role="img" aria-hidden="true" class="gl-button-icon gl-icon s16 gl-fill-current"><use href="/assets/icons-ca86b3ebff8cbe14f7728864eabad153d00b66986018226fe439015884de11c2.svg#thumbtack"></use></svg>  <!----></button></li></ul></div></li><li><!----> <button id="menu-section-button-secure" data-testid="menu-section-button" data-qa-section-name="Secure" aria-controls="secure" aria-expanded="false" data-qa-menu-item="Secure" class="super-sidebar-nav-item gl-relative gl-mb-2 gl-flex gl-min-h-7 gl-w-full gl-appearance-none gl-items-center gl-gap-3 gl-rounded-base gl-border-0 gl-bg-transparent gl-px-3 gl-py-2 gl-text-left !gl-text-default !gl-no-underline focus:gl-focus"><span aria-hidden="true" class="gl-absolute gl-bottom-2 gl-left-2 gl-top-2 gl-transition-all gl-duration-slow gl-bg-transparent" style="width: 3px; border-radius: 3px; margin-right: 1px;"></span> <span class="gl-flex gl-w-6 gl-shrink-0"><svg data-testid="shield-icon" role="img" aria-hidden="true" class="super-sidebar-nav-item-icon gl-m-auto gl-icon s16 gl-fill-current"><use href="/assets/icons-ca86b3ebff8cbe14f7728864eabad153d00b66986018226fe439015884de11c2.svg#shield"></use></svg></span> <span class="gl-truncate-end gl-grow gl-text-default">
      Secure
    </span> <span class="gl-text-right gl-text-subtle"><svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg" class="gl-animated-icon gl-animated-icon-off gl-animated-icon-current"><path d="M6.75 4.75L10 8L6.75 11.25" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="gl-animated-chevron-right-down-arrow"></path></svg></span></button> <!----> <div class="gl-m-0 gl-list-none gl-p-0 gl-duration-medium gl-ease-ease collapse" id="secure" data-testid="menu-section" data-qa-section-name="Secure" style="display: none;"><ul aria-label="Secure" class="gl-m-0 gl-list-none gl-p-0"><li data-testid="nav-item" class="show-on-focus-or-hover--context hide-on-focus-or-hover--context transition-opacity-on-hover--context gl-relative"><a href="https://gitlab-v2.innotech-vn.com/the-ai-team/innocody/innocody-engine/-/security/configuration" class="super-sidebar-nav-item show-on-focus-or-hover--control hide-on-focus-or-hover--control gl-relative gl-mb-1 gl-flex gl-min-h-7 gl-items-center gl-gap-3 gl-py-2 !gl-text-default !gl-no-underline focus:gl-focus gl-px-3 gl-rounded-base" data-testid="nav-item-link" aria-label="Security configuration" data-track-action="click_menu_item" data-track-label="configuration" data-track-property="nav_panel_project" data-qa-submenu-item="Security configuration"><div aria-hidden="true" data-testid="active-indicator" class="active-indicator gl-absolute gl-bottom-2 gl-left-2 gl-top-2 gl-transition-all gl-duration-slow gl-opacity-0" style="width: 3px; border-radius: 3px; margin-right: 1px;"></div> <div class="gl-flex gl-w-6 gl-shrink-0"><!----></div> <div data-testid="nav-item-link-label" class="gl-grow gl-text-default gl-break-anywhere">
      Security configuration
      <!----></div>  <span class="gl-flex gl-min-w-6 gl-items-start gl-justify-end"><!----></span></a> <button aria-label="Pin Security configuration" data-testid="nav-item-pin" type="button" class="btn show-on-focus-or-hover--target transition-opacity-on-hover--target always-animate gl-absolute gl-right-3 gl-top-1/2 -gl-translate-y-1/2 btn-default btn-sm gl-button btn-default-tertiary btn-icon gl-pointer-events-none"><!----> <svg data-testid="thumbtack-icon" role="img" aria-hidden="true" class="gl-button-icon gl-icon s16 gl-fill-current"><use href="/assets/icons-ca86b3ebff8cbe14f7728864eabad153d00b66986018226fe439015884de11c2.svg#thumbtack"></use></svg>  <!----></button></li></ul></div></li><li><!----> <button id="menu-section-button-deploy" data-testid="menu-section-button" data-qa-section-name="Deploy" aria-controls="deploy" aria-expanded="false" data-qa-menu-item="Deploy" class="super-sidebar-nav-item gl-relative gl-mb-2 gl-flex gl-min-h-7 gl-w-full gl-appearance-none gl-items-center gl-gap-3 gl-rounded-base gl-border-0 gl-bg-transparent gl-px-3 gl-py-2 gl-text-left !gl-text-default !gl-no-underline focus:gl-focus"><span aria-hidden="true" class="gl-absolute gl-bottom-2 gl-left-2 gl-top-2 gl-transition-all gl-duration-slow gl-bg-transparent" style="width: 3px; border-radius: 3px; margin-right: 1px;"></span> <span class="gl-flex gl-w-6 gl-shrink-0"><svg data-testid="deployments-icon" role="img" aria-hidden="true" class="super-sidebar-nav-item-icon gl-m-auto gl-icon s16 gl-fill-current"><use href="/assets/icons-ca86b3ebff8cbe14f7728864eabad153d00b66986018226fe439015884de11c2.svg#deployments"></use></svg></span> <span class="gl-truncate-end gl-grow gl-text-default">
      Deploy
    </span> <span class="gl-text-right gl-text-subtle"><svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg" class="gl-animated-icon gl-animated-icon-off gl-animated-icon-current"><path d="M6.75 4.75L10 8L6.75 11.25" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="gl-animated-chevron-right-down-arrow"></path></svg></span></button> <!----> <div class="gl-m-0 gl-list-none gl-p-0 gl-duration-medium gl-ease-ease collapse" id="deploy" data-testid="menu-section" data-qa-section-name="Deploy" style="display: none;"><ul aria-label="Deploy" class="gl-m-0 gl-list-none gl-p-0"><li data-testid="nav-item" class="show-on-focus-or-hover--context hide-on-focus-or-hover--context transition-opacity-on-hover--context gl-relative"><a href="https://gitlab-v2.innotech-vn.com/the-ai-team/innocody/innocody-engine/-/releases" class="super-sidebar-nav-item show-on-focus-or-hover--control hide-on-focus-or-hover--control gl-relative gl-mb-1 gl-flex gl-min-h-7 gl-items-center gl-gap-3 gl-py-2 !gl-text-default !gl-no-underline focus:gl-focus gl-px-3 gl-rounded-base shortcuts-deployments-releases" data-testid="nav-item-link" aria-label="Releases" data-track-action="click_menu_item" data-track-label="releases" data-track-property="nav_panel_project" data-qa-submenu-item="Releases"><div aria-hidden="true" data-testid="active-indicator" class="active-indicator gl-absolute gl-bottom-2 gl-left-2 gl-top-2 gl-transition-all gl-duration-slow gl-opacity-0" style="width: 3px; border-radius: 3px; margin-right: 1px;"></div> <div class="gl-flex gl-w-6 gl-shrink-0"><!----></div> <div data-testid="nav-item-link-label" class="gl-grow gl-text-default gl-break-anywhere">
      Releases
      <!----></div>  <span class="gl-flex gl-min-w-6 gl-items-start gl-justify-end"><!----></span></a> <button aria-label="Pin Releases" data-testid="nav-item-pin" type="button" class="btn show-on-focus-or-hover--target transition-opacity-on-hover--target always-animate gl-absolute gl-right-3 gl-top-1/2 -gl-translate-y-1/2 btn-default btn-sm gl-button btn-default-tertiary btn-icon gl-pointer-events-none"><!----> <svg data-testid="thumbtack-icon" role="img" aria-hidden="true" class="gl-button-icon gl-icon s16 gl-fill-current"><use href="/assets/icons-ca86b3ebff8cbe14f7728864eabad153d00b66986018226fe439015884de11c2.svg#thumbtack"></use></svg>  <!----></button></li><li data-testid="nav-item" class="show-on-focus-or-hover--context hide-on-focus-or-hover--context transition-opacity-on-hover--context gl-relative"><a href="https://gitlab-v2.innotech-vn.com/the-ai-team/innocody/innocody-engine/-/feature_flags" class="super-sidebar-nav-item show-on-focus-or-hover--control hide-on-focus-or-hover--control gl-relative gl-mb-1 gl-flex gl-min-h-7 gl-items-center gl-gap-3 gl-py-2 !gl-text-default !gl-no-underline focus:gl-focus gl-px-3 gl-rounded-base shortcuts-feature-flags" data-testid="nav-item-link" aria-label="Feature flags" data-track-action="click_menu_item" data-track-label="feature_flags" data-track-property="nav_panel_project" data-qa-submenu-item="Feature flags"><div aria-hidden="true" data-testid="active-indicator" class="active-indicator gl-absolute gl-bottom-2 gl-left-2 gl-top-2 gl-transition-all gl-duration-slow gl-opacity-0" style="width: 3px; border-radius: 3px; margin-right: 1px;"></div> <div class="gl-flex gl-w-6 gl-shrink-0"><!----></div> <div data-testid="nav-item-link-label" class="gl-grow gl-text-default gl-break-anywhere">
      Feature flags
      <!----></div>  <span class="gl-flex gl-min-w-6 gl-items-start gl-justify-end"><!----></span></a> <button aria-label="Pin Feature flags" data-testid="nav-item-pin" type="button" class="btn show-on-focus-or-hover--target transition-opacity-on-hover--target always-animate gl-absolute gl-right-3 gl-top-1/2 -gl-translate-y-1/2 btn-default btn-sm gl-button btn-default-tertiary btn-icon gl-pointer-events-none"><!----> <svg data-testid="thumbtack-icon" role="img" aria-hidden="true" class="gl-button-icon gl-icon s16 gl-fill-current"><use href="/assets/icons-ca86b3ebff8cbe14f7728864eabad153d00b66986018226fe439015884de11c2.svg#thumbtack"></use></svg>  <!----></button></li><li data-testid="nav-item" class="show-on-focus-or-hover--context hide-on-focus-or-hover--context transition-opacity-on-hover--context gl-relative"><a href="https://gitlab-v2.innotech-vn.com/the-ai-team/innocody/innocody-engine/-/packages" class="super-sidebar-nav-item show-on-focus-or-hover--control hide-on-focus-or-hover--control gl-relative gl-mb-1 gl-flex gl-min-h-7 gl-items-center gl-gap-3 gl-py-2 !gl-text-default !gl-no-underline focus:gl-focus gl-px-3 gl-rounded-base shortcuts-container-registry" data-testid="nav-item-link" aria-label="Package registry" data-track-action="click_menu_item" data-track-label="packages_registry" data-track-property="nav_panel_project" data-qa-submenu-item="Package registry"><div aria-hidden="true" data-testid="active-indicator" class="active-indicator gl-absolute gl-bottom-2 gl-left-2 gl-top-2 gl-transition-all gl-duration-slow gl-opacity-0" style="width: 3px; border-radius: 3px; margin-right: 1px;"></div> <div class="gl-flex gl-w-6 gl-shrink-0"><!----></div> <div data-testid="nav-item-link-label" class="gl-grow gl-text-default gl-break-anywhere">
      Package registry
      <!----></div>  <span class="gl-flex gl-min-w-6 gl-items-start gl-justify-end"><!----></span></a> <button aria-label="Pin Package registry" data-testid="nav-item-pin" type="button" class="btn show-on-focus-or-hover--target transition-opacity-on-hover--target always-animate gl-absolute gl-right-3 gl-top-1/2 -gl-translate-y-1/2 btn-default btn-sm gl-button btn-default-tertiary btn-icon gl-pointer-events-none"><!----> <svg data-testid="thumbtack-icon" role="img" aria-hidden="true" class="gl-button-icon gl-icon s16 gl-fill-current"><use href="/assets/icons-ca86b3ebff8cbe14f7728864eabad153d00b66986018226fe439015884de11c2.svg#thumbtack"></use></svg>  <!----></button></li><li data-testid="nav-item" class="show-on-focus-or-hover--context hide-on-focus-or-hover--context transition-opacity-on-hover--context gl-relative"><a href="https://gitlab-v2.innotech-vn.com/the-ai-team/innocody/innocody-engine/container_registry" class="super-sidebar-nav-item show-on-focus-or-hover--control hide-on-focus-or-hover--control gl-relative gl-mb-1 gl-flex gl-min-h-7 gl-items-center gl-gap-3 gl-py-2 !gl-text-default !gl-no-underline focus:gl-focus gl-px-3 gl-rounded-base" data-testid="nav-item-link" aria-label="Container registry" data-track-action="click_menu_item" data-track-label="container_registry" data-track-property="nav_panel_project" data-qa-submenu-item="Container registry"><div aria-hidden="true" data-testid="active-indicator" class="active-indicator gl-absolute gl-bottom-2 gl-left-2 gl-top-2 gl-transition-all gl-duration-slow gl-opacity-0" style="width: 3px; border-radius: 3px; margin-right: 1px;"></div> <div class="gl-flex gl-w-6 gl-shrink-0"><!----></div> <div data-testid="nav-item-link-label" class="gl-grow gl-text-default gl-break-anywhere">
      Container registry
      <!----></div>  <span class="gl-flex gl-min-w-6 gl-items-start gl-justify-end"><!----></span></a> <button aria-label="Unpin Container registry" data-testid="nav-item-unpin" type="button" class="btn show-on-focus-or-hover--target transition-opacity-on-hover--target always-animate gl-absolute gl-right-3 gl-top-1/2 -gl-translate-y-1/2 btn-default btn-sm gl-button btn-default-tertiary btn-icon gl-pointer-events-none"><!----> <svg data-testid="thumbtack-solid-icon" role="img" aria-hidden="true" class="gl-button-icon gl-icon s16 gl-fill-current"><use href="/assets/icons-ca86b3ebff8cbe14f7728864eabad153d00b66986018226fe439015884de11c2.svg#thumbtack-solid"></use></svg>  <!----></button></li><li data-testid="nav-item" class="show-on-focus-or-hover--context hide-on-focus-or-hover--context transition-opacity-on-hover--context gl-relative"><a href="https://gitlab-v2.innotech-vn.com/the-ai-team/innocody/innocody-engine/-/ml/models" class="super-sidebar-nav-item show-on-focus-or-hover--control hide-on-focus-or-hover--control gl-relative gl-mb-1 gl-flex gl-min-h-7 gl-items-center gl-gap-3 gl-py-2 !gl-text-default !gl-no-underline focus:gl-focus gl-px-3 gl-rounded-base" data-testid="nav-item-link" aria-label="Model registry" data-track-action="click_menu_item" data-track-label="model_registry" data-track-property="nav_panel_project" data-qa-submenu-item="Model registry"><div aria-hidden="true" data-testid="active-indicator" class="active-indicator gl-absolute gl-bottom-2 gl-left-2 gl-top-2 gl-transition-all gl-duration-slow gl-opacity-0" style="width: 3px; border-radius: 3px; margin-right: 1px;"></div> <div class="gl-flex gl-w-6 gl-shrink-0"><!----></div> <div data-testid="nav-item-link-label" class="gl-grow gl-text-default gl-break-anywhere">
      Model registry
      <!----></div>  <span class="gl-flex gl-min-w-6 gl-items-start gl-justify-end"><!----></span></a> <button aria-label="Pin Model registry" data-testid="nav-item-pin" type="button" class="btn show-on-focus-or-hover--target transition-opacity-on-hover--target always-animate gl-absolute gl-right-3 gl-top-1/2 -gl-translate-y-1/2 btn-default btn-sm gl-button btn-default-tertiary btn-icon gl-pointer-events-none"><!----> <svg data-testid="thumbtack-icon" role="img" aria-hidden="true" class="gl-button-icon gl-icon s16 gl-fill-current"><use href="/assets/icons-ca86b3ebff8cbe14f7728864eabad153d00b66986018226fe439015884de11c2.svg#thumbtack"></use></svg>  <!----></button></li></ul></div></li><li><!----> <button id="menu-section-button-operate" data-testid="menu-section-button" data-qa-section-name="Operate" aria-controls="operate" aria-expanded="false" data-qa-menu-item="Operate" class="super-sidebar-nav-item gl-relative gl-mb-2 gl-flex gl-min-h-7 gl-w-full gl-appearance-none gl-items-center gl-gap-3 gl-rounded-base gl-border-0 gl-bg-transparent gl-px-3 gl-py-2 gl-text-left !gl-text-default !gl-no-underline focus:gl-focus"><span aria-hidden="true" class="gl-absolute gl-bottom-2 gl-left-2 gl-top-2 gl-transition-all gl-duration-slow gl-bg-transparent" style="width: 3px; border-radius: 3px; margin-right: 1px;"></span> <span class="gl-flex gl-w-6 gl-shrink-0"><svg data-testid="cloud-pod-icon" role="img" aria-hidden="true" class="super-sidebar-nav-item-icon gl-m-auto gl-icon s16 gl-fill-current"><use href="/assets/icons-ca86b3ebff8cbe14f7728864eabad153d00b66986018226fe439015884de11c2.svg#cloud-pod"></use></svg></span> <span class="gl-truncate-end gl-grow gl-text-default">
      Operate
    </span> <span class="gl-text-right gl-text-subtle"><svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg" class="gl-animated-icon gl-animated-icon-off gl-animated-icon-current"><path d="M6.75 4.75L10 8L6.75 11.25" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="gl-animated-chevron-right-down-arrow"></path></svg></span></button> <!----> <div class="gl-m-0 gl-list-none gl-p-0 gl-duration-medium gl-ease-ease collapse" id="operate" data-testid="menu-section" data-qa-section-name="Operate" style="display: none;"><ul aria-label="Operate" class="gl-m-0 gl-list-none gl-p-0"><li data-testid="nav-item" class="show-on-focus-or-hover--context hide-on-focus-or-hover--context transition-opacity-on-hover--context gl-relative"><a href="https://gitlab-v2.innotech-vn.com/the-ai-team/innocody/innocody-engine/-/environments" class="super-sidebar-nav-item show-on-focus-or-hover--control hide-on-focus-or-hover--control gl-relative gl-mb-1 gl-flex gl-min-h-7 gl-items-center gl-gap-3 gl-py-2 !gl-text-default !gl-no-underline focus:gl-focus gl-px-3 gl-rounded-base shortcuts-environments" data-testid="nav-item-link" aria-label="Environments" data-track-action="click_menu_item" data-track-label="environments" data-track-property="nav_panel_project" data-qa-submenu-item="Environments"><div aria-hidden="true" data-testid="active-indicator" class="active-indicator gl-absolute gl-bottom-2 gl-left-2 gl-top-2 gl-transition-all gl-duration-slow gl-opacity-0" style="width: 3px; border-radius: 3px; margin-right: 1px;"></div> <div class="gl-flex gl-w-6 gl-shrink-0"><!----></div> <div data-testid="nav-item-link-label" class="gl-grow gl-text-default gl-break-anywhere">
      Environments
      <!----></div>  <span class="gl-flex gl-min-w-6 gl-items-start gl-justify-end"><!----></span></a> <button aria-label="Pin Environments" data-testid="nav-item-pin" type="button" class="btn show-on-focus-or-hover--target transition-opacity-on-hover--target always-animate gl-absolute gl-right-3 gl-top-1/2 -gl-translate-y-1/2 btn-default btn-sm gl-button btn-default-tertiary btn-icon gl-pointer-events-none"><!----> <svg data-testid="thumbtack-icon" role="img" aria-hidden="true" class="gl-button-icon gl-icon s16 gl-fill-current"><use href="/assets/icons-ca86b3ebff8cbe14f7728864eabad153d00b66986018226fe439015884de11c2.svg#thumbtack"></use></svg>  <!----></button></li><li data-testid="nav-item" class="show-on-focus-or-hover--context hide-on-focus-or-hover--context transition-opacity-on-hover--context gl-relative"><a href="https://gitlab-v2.innotech-vn.com/the-ai-team/innocody/innocody-engine/-/clusters" class="super-sidebar-nav-item show-on-focus-or-hover--control hide-on-focus-or-hover--control gl-relative gl-mb-1 gl-flex gl-min-h-7 gl-items-center gl-gap-3 gl-py-2 !gl-text-default !gl-no-underline focus:gl-focus gl-px-3 gl-rounded-base shortcuts-kubernetes" data-testid="nav-item-link" aria-label="Kubernetes clusters" data-track-action="click_menu_item" data-track-label="kubernetes" data-track-property="nav_panel_project" data-qa-submenu-item="Kubernetes clusters"><div aria-hidden="true" data-testid="active-indicator" class="active-indicator gl-absolute gl-bottom-2 gl-left-2 gl-top-2 gl-transition-all gl-duration-slow gl-opacity-0" style="width: 3px; border-radius: 3px; margin-right: 1px;"></div> <div class="gl-flex gl-w-6 gl-shrink-0"><!----></div> <div data-testid="nav-item-link-label" class="gl-grow gl-text-default gl-break-anywhere">
      Kubernetes clusters
      <!----></div>  <span class="gl-flex gl-min-w-6 gl-items-start gl-justify-end"><!----></span></a> <button aria-label="Pin Kubernetes clusters" data-testid="nav-item-pin" type="button" class="btn show-on-focus-or-hover--target transition-opacity-on-hover--target always-animate gl-absolute gl-right-3 gl-top-1/2 -gl-translate-y-1/2 btn-default btn-sm gl-button btn-default-tertiary btn-icon gl-pointer-events-none"><!----> <svg data-testid="thumbtack-icon" role="img" aria-hidden="true" class="gl-button-icon gl-icon s16 gl-fill-current"><use href="/assets/icons-ca86b3ebff8cbe14f7728864eabad153d00b66986018226fe439015884de11c2.svg#thumbtack"></use></svg>  <!----></button></li><li data-testid="nav-item" class="show-on-focus-or-hover--context hide-on-focus-or-hover--context transition-opacity-on-hover--context gl-relative"><a href="https://gitlab-v2.innotech-vn.com/the-ai-team/innocody/innocody-engine/-/terraform" class="super-sidebar-nav-item show-on-focus-or-hover--control hide-on-focus-or-hover--control gl-relative gl-mb-1 gl-flex gl-min-h-7 gl-items-center gl-gap-3 gl-py-2 !gl-text-default !gl-no-underline focus:gl-focus gl-px-3 gl-rounded-base" data-testid="nav-item-link" aria-label="Terraform states" data-track-action="click_menu_item" data-track-label="terraform_states" data-track-property="nav_panel_project" data-qa-submenu-item="Terraform states"><div aria-hidden="true" data-testid="active-indicator" class="active-indicator gl-absolute gl-bottom-2 gl-left-2 gl-top-2 gl-transition-all gl-duration-slow gl-opacity-0" style="width: 3px; border-radius: 3px; margin-right: 1px;"></div> <div class="gl-flex gl-w-6 gl-shrink-0"><!----></div> <div data-testid="nav-item-link-label" class="gl-grow gl-text-default gl-break-anywhere">
      Terraform states
      <!----></div>  <span class="gl-flex gl-min-w-6 gl-items-start gl-justify-end"><!----></span></a> <button aria-label="Pin Terraform states" data-testid="nav-item-pin" type="button" class="btn show-on-focus-or-hover--target transition-opacity-on-hover--target always-animate gl-absolute gl-right-3 gl-top-1/2 -gl-translate-y-1/2 btn-default btn-sm gl-button btn-default-tertiary btn-icon gl-pointer-events-none"><!----> <svg data-testid="thumbtack-icon" role="img" aria-hidden="true" class="gl-button-icon gl-icon s16 gl-fill-current"><use href="/assets/icons-ca86b3ebff8cbe14f7728864eabad153d00b66986018226fe439015884de11c2.svg#thumbtack"></use></svg>  <!----></button></li><li data-testid="nav-item" class="show-on-focus-or-hover--context hide-on-focus-or-hover--context transition-opacity-on-hover--context gl-relative"><a href="https://gitlab-v2.innotech-vn.com/the-ai-team/innocody/innocody-engine/-/terraform_module_registry" class="super-sidebar-nav-item show-on-focus-or-hover--control hide-on-focus-or-hover--control gl-relative gl-mb-1 gl-flex gl-min-h-7 gl-items-center gl-gap-3 gl-py-2 !gl-text-default !gl-no-underline focus:gl-focus gl-px-3 gl-rounded-base" data-testid="nav-item-link" aria-label="Terraform modules" data-track-action="click_menu_item" data-track-label="infrastructure_registry" data-track-property="nav_panel_project" data-qa-submenu-item="Terraform modules"><div aria-hidden="true" data-testid="active-indicator" class="active-indicator gl-absolute gl-bottom-2 gl-left-2 gl-top-2 gl-transition-all gl-duration-slow gl-opacity-0" style="width: 3px; border-radius: 3px; margin-right: 1px;"></div> <div class="gl-flex gl-w-6 gl-shrink-0"><!----></div> <div data-testid="nav-item-link-label" class="gl-grow gl-text-default gl-break-anywhere">
      Terraform modules
      <!----></div>  <span class="gl-flex gl-min-w-6 gl-items-start gl-justify-end"><!----></span></a> <button aria-label="Pin Terraform modules" data-testid="nav-item-pin" type="button" class="btn show-on-focus-or-hover--target transition-opacity-on-hover--target always-animate gl-absolute gl-right-3 gl-top-1/2 -gl-translate-y-1/2 btn-default btn-sm gl-button btn-default-tertiary btn-icon gl-pointer-events-none"><!----> <svg data-testid="thumbtack-icon" role="img" aria-hidden="true" class="gl-button-icon gl-icon s16 gl-fill-current"><use href="/assets/icons-ca86b3ebff8cbe14f7728864eabad153d00b66986018226fe439015884de11c2.svg#thumbtack"></use></svg>  <!----></button></li></ul></div></li><li><!----> <button id="menu-section-button-monitor" data-testid="menu-section-button" data-qa-section-name="Monitor" aria-controls="monitor" aria-expanded="false" data-qa-menu-item="Monitor" class="super-sidebar-nav-item gl-relative gl-mb-2 gl-flex gl-min-h-7 gl-w-full gl-appearance-none gl-items-center gl-gap-3 gl-rounded-base gl-border-0 gl-bg-transparent gl-px-3 gl-py-2 gl-text-left !gl-text-default !gl-no-underline focus:gl-focus"><span aria-hidden="true" class="gl-absolute gl-bottom-2 gl-left-2 gl-top-2 gl-transition-all gl-duration-slow gl-bg-transparent" style="width: 3px; border-radius: 3px; margin-right: 1px;"></span> <span class="gl-flex gl-w-6 gl-shrink-0"><svg data-testid="monitor-icon" role="img" aria-hidden="true" class="super-sidebar-nav-item-icon gl-m-auto gl-icon s16 gl-fill-current"><use href="/assets/icons-ca86b3ebff8cbe14f7728864eabad153d00b66986018226fe439015884de11c2.svg#monitor"></use></svg></span> <span class="gl-truncate-end gl-grow gl-text-default">
      Monitor
    </span> <span class="gl-text-right gl-text-subtle"><svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg" class="gl-animated-icon gl-animated-icon-off gl-animated-icon-current"><path d="M6.75 4.75L10 8L6.75 11.25" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="gl-animated-chevron-right-down-arrow"></path></svg></span></button> <!----> <div class="gl-m-0 gl-list-none gl-p-0 gl-duration-medium gl-ease-ease collapse" id="monitor" data-testid="menu-section" data-qa-section-name="Monitor" style="display: none;"><ul aria-label="Monitor" class="gl-m-0 gl-list-none gl-p-0"><li data-testid="nav-item" class="show-on-focus-or-hover--context hide-on-focus-or-hover--context transition-opacity-on-hover--context gl-relative"><a href="https://gitlab-v2.innotech-vn.com/the-ai-team/innocody/innocody-engine/-/error_tracking" class="super-sidebar-nav-item show-on-focus-or-hover--control hide-on-focus-or-hover--control gl-relative gl-mb-1 gl-flex gl-min-h-7 gl-items-center gl-gap-3 gl-py-2 !gl-text-default !gl-no-underline focus:gl-focus gl-px-3 gl-rounded-base" data-testid="nav-item-link" aria-label="Error Tracking" data-track-action="click_menu_item" data-track-label="error_tracking" data-track-property="nav_panel_project" data-qa-submenu-item="Error Tracking"><div aria-hidden="true" data-testid="active-indicator" class="active-indicator gl-absolute gl-bottom-2 gl-left-2 gl-top-2 gl-transition-all gl-duration-slow gl-opacity-0" style="width: 3px; border-radius: 3px; margin-right: 1px;"></div> <div class="gl-flex gl-w-6 gl-shrink-0"><!----></div> <div data-testid="nav-item-link-label" class="gl-grow gl-text-default gl-break-anywhere">
      Error Tracking
      <!----></div>  <span class="gl-flex gl-min-w-6 gl-items-start gl-justify-end"><!----></span></a> <button aria-label="Pin Error Tracking" data-testid="nav-item-pin" type="button" class="btn show-on-focus-or-hover--target transition-opacity-on-hover--target always-animate gl-absolute gl-right-3 gl-top-1/2 -gl-translate-y-1/2 btn-default btn-sm gl-button btn-default-tertiary btn-icon gl-pointer-events-none"><!----> <svg data-testid="thumbtack-icon" role="img" aria-hidden="true" class="gl-button-icon gl-icon s16 gl-fill-current"><use href="/assets/icons-ca86b3ebff8cbe14f7728864eabad153d00b66986018226fe439015884de11c2.svg#thumbtack"></use></svg>  <!----></button></li><li data-testid="nav-item" class="show-on-focus-or-hover--context hide-on-focus-or-hover--context transition-opacity-on-hover--context gl-relative"><a href="https://gitlab-v2.innotech-vn.com/the-ai-team/innocody/innocody-engine/-/alert_management" class="super-sidebar-nav-item show-on-focus-or-hover--control hide-on-focus-or-hover--control gl-relative gl-mb-1 gl-flex gl-min-h-7 gl-items-center gl-gap-3 gl-py-2 !gl-text-default !gl-no-underline focus:gl-focus gl-px-3 gl-rounded-base" data-testid="nav-item-link" aria-label="Alerts" data-track-action="click_menu_item" data-track-label="alert_management" data-track-property="nav_panel_project" data-qa-submenu-item="Alerts"><div aria-hidden="true" data-testid="active-indicator" class="active-indicator gl-absolute gl-bottom-2 gl-left-2 gl-top-2 gl-transition-all gl-duration-slow gl-opacity-0" style="width: 3px; border-radius: 3px; margin-right: 1px;"></div> <div class="gl-flex gl-w-6 gl-shrink-0"><!----></div> <div data-testid="nav-item-link-label" class="gl-grow gl-text-default gl-break-anywhere">
      Alerts
      <!----></div>  <span class="gl-flex gl-min-w-6 gl-items-start gl-justify-end"><!----></span></a> <button aria-label="Pin Alerts" data-testid="nav-item-pin" type="button" class="btn show-on-focus-or-hover--target transition-opacity-on-hover--target always-animate gl-absolute gl-right-3 gl-top-1/2 -gl-translate-y-1/2 btn-default btn-sm gl-button btn-default-tertiary btn-icon gl-pointer-events-none"><!----> <svg data-testid="thumbtack-icon" role="img" aria-hidden="true" class="gl-button-icon gl-icon s16 gl-fill-current"><use href="/assets/icons-ca86b3ebff8cbe14f7728864eabad153d00b66986018226fe439015884de11c2.svg#thumbtack"></use></svg>  <!----></button></li><li data-testid="nav-item" class="show-on-focus-or-hover--context hide-on-focus-or-hover--context transition-opacity-on-hover--context gl-relative"><a href="https://gitlab-v2.innotech-vn.com/the-ai-team/innocody/innocody-engine/-/incidents" class="super-sidebar-nav-item show-on-focus-or-hover--control hide-on-focus-or-hover--control gl-relative gl-mb-1 gl-flex gl-min-h-7 gl-items-center gl-gap-3 gl-py-2 !gl-text-default !gl-no-underline focus:gl-focus gl-px-3 gl-rounded-base" data-testid="nav-item-link" aria-label="Incidents" data-track-action="click_menu_item" data-track-label="incidents" data-track-property="nav_panel_project" data-qa-submenu-item="Incidents"><div aria-hidden="true" data-testid="active-indicator" class="active-indicator gl-absolute gl-bottom-2 gl-left-2 gl-top-2 gl-transition-all gl-duration-slow gl-opacity-0" style="width: 3px; border-radius: 3px; margin-right: 1px;"></div> <div class="gl-flex gl-w-6 gl-shrink-0"><!----></div> <div data-testid="nav-item-link-label" class="gl-grow gl-text-default gl-break-anywhere">
      Incidents
      <!----></div>  <span class="gl-flex gl-min-w-6 gl-items-start gl-justify-end"><!----></span></a> <button aria-label="Pin Incidents" data-testid="nav-item-pin" type="button" class="btn show-on-focus-or-hover--target transition-opacity-on-hover--target always-animate gl-absolute gl-right-3 gl-top-1/2 -gl-translate-y-1/2 btn-default btn-sm gl-button btn-default-tertiary btn-icon gl-pointer-events-none"><!----> <svg data-testid="thumbtack-icon" role="img" aria-hidden="true" class="gl-button-icon gl-icon s16 gl-fill-current"><use href="/assets/icons-ca86b3ebff8cbe14f7728864eabad153d00b66986018226fe439015884de11c2.svg#thumbtack"></use></svg>  <!----></button></li></ul></div></li><li><!----> <button id="menu-section-button-analyze" data-testid="menu-section-button" data-qa-section-name="Analyze" aria-controls="analyze" aria-expanded="false" data-qa-menu-item="Analyze" class="super-sidebar-nav-item gl-relative gl-mb-2 gl-flex gl-min-h-7 gl-w-full gl-appearance-none gl-items-center gl-gap-3 gl-rounded-base gl-border-0 gl-bg-transparent gl-px-3 gl-py-2 gl-text-left !gl-text-default !gl-no-underline focus:gl-focus"><span aria-hidden="true" class="gl-absolute gl-bottom-2 gl-left-2 gl-top-2 gl-transition-all gl-duration-slow gl-bg-transparent" style="width: 3px; border-radius: 3px; margin-right: 1px;"></span> <span class="gl-flex gl-w-6 gl-shrink-0"><svg data-testid="chart-icon" role="img" aria-hidden="true" class="super-sidebar-nav-item-icon gl-m-auto gl-icon s16 gl-fill-current"><use href="/assets/icons-ca86b3ebff8cbe14f7728864eabad153d00b66986018226fe439015884de11c2.svg#chart"></use></svg></span> <span class="gl-truncate-end gl-grow gl-text-default">
      Analyze
    </span> <span class="gl-text-right gl-text-subtle"><svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg" class="gl-animated-icon gl-animated-icon-off gl-animated-icon-current"><path d="M6.75 4.75L10 8L6.75 11.25" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="gl-animated-chevron-right-down-arrow"></path></svg></span></button> <!----> <div class="gl-m-0 gl-list-none gl-p-0 gl-duration-medium gl-ease-ease collapse" id="analyze" data-testid="menu-section" data-qa-section-name="Analyze" style="display: none;"><ul aria-label="Analyze" class="gl-m-0 gl-list-none gl-p-0"><li data-testid="nav-item" class="show-on-focus-or-hover--context hide-on-focus-or-hover--context transition-opacity-on-hover--context gl-relative"><a href="https://gitlab-v2.innotech-vn.com/the-ai-team/innocody/innocody-engine/-/value_stream_analytics" class="super-sidebar-nav-item show-on-focus-or-hover--control hide-on-focus-or-hover--control gl-relative gl-mb-1 gl-flex gl-min-h-7 gl-items-center gl-gap-3 gl-py-2 !gl-text-default !gl-no-underline focus:gl-focus gl-px-3 gl-rounded-base shortcuts-project-cycle-analytics" data-testid="nav-item-link" aria-label="Value stream analytics" data-track-action="click_menu_item" data-track-label="cycle_analytics" data-track-property="nav_panel_project" data-qa-submenu-item="Value stream analytics"><div aria-hidden="true" data-testid="active-indicator" class="active-indicator gl-absolute gl-bottom-2 gl-left-2 gl-top-2 gl-transition-all gl-duration-slow gl-opacity-0" style="width: 3px; border-radius: 3px; margin-right: 1px;"></div> <div class="gl-flex gl-w-6 gl-shrink-0"><!----></div> <div data-testid="nav-item-link-label" class="gl-grow gl-text-default gl-break-anywhere">
      Value stream analytics
      <!----></div>  <span class="gl-flex gl-min-w-6 gl-items-start gl-justify-end"><!----></span></a> <button aria-label="Pin Value stream analytics" data-testid="nav-item-pin" type="button" class="btn show-on-focus-or-hover--target transition-opacity-on-hover--target always-animate gl-absolute gl-right-3 gl-top-1/2 -gl-translate-y-1/2 btn-default btn-sm gl-button btn-default-tertiary btn-icon gl-pointer-events-none"><!----> <svg data-testid="thumbtack-icon" role="img" aria-hidden="true" class="gl-button-icon gl-icon s16 gl-fill-current"><use href="/assets/icons-ca86b3ebff8cbe14f7728864eabad153d00b66986018226fe439015884de11c2.svg#thumbtack"></use></svg>  <!----></button></li><li data-testid="nav-item" class="show-on-focus-or-hover--context hide-on-focus-or-hover--context transition-opacity-on-hover--context gl-relative"><a href="https://gitlab-v2.innotech-vn.com/the-ai-team/innocody/innocody-engine/-/graphs/main?ref_type=heads" class="super-sidebar-nav-item show-on-focus-or-hover--control hide-on-focus-or-hover--control gl-relative gl-mb-1 gl-flex gl-min-h-7 gl-items-center gl-gap-3 gl-py-2 !gl-text-default !gl-no-underline focus:gl-focus gl-px-3 gl-rounded-base" data-testid="nav-item-link" aria-label="Contributor analytics" data-track-action="click_menu_item" data-track-label="contributors" data-track-property="nav_panel_project" data-qa-submenu-item="Contributor analytics"><div aria-hidden="true" data-testid="active-indicator" class="active-indicator gl-absolute gl-bottom-2 gl-left-2 gl-top-2 gl-transition-all gl-duration-slow gl-opacity-0" style="width: 3px; border-radius: 3px; margin-right: 1px;"></div> <div class="gl-flex gl-w-6 gl-shrink-0"><!----></div> <div data-testid="nav-item-link-label" class="gl-grow gl-text-default gl-break-anywhere">
      Contributor analytics
      <!----></div>  <span class="gl-flex gl-min-w-6 gl-items-start gl-justify-end"><!----></span></a> <button aria-label="Pin Contributor analytics" data-testid="nav-item-pin" type="button" class="btn show-on-focus-or-hover--target transition-opacity-on-hover--target always-animate gl-absolute gl-right-3 gl-top-1/2 -gl-translate-y-1/2 btn-default btn-sm gl-button btn-default-tertiary btn-icon gl-pointer-events-none"><!----> <svg data-testid="thumbtack-icon" role="img" aria-hidden="true" class="gl-button-icon gl-icon s16 gl-fill-current"><use href="/assets/icons-ca86b3ebff8cbe14f7728864eabad153d00b66986018226fe439015884de11c2.svg#thumbtack"></use></svg>  <!----></button></li><li data-testid="nav-item" class="show-on-focus-or-hover--context hide-on-focus-or-hover--context transition-opacity-on-hover--context gl-relative"><a href="https://gitlab-v2.innotech-vn.com/the-ai-team/innocody/innocody-engine/-/pipelines/charts" class="super-sidebar-nav-item show-on-focus-or-hover--control hide-on-focus-or-hover--control gl-relative gl-mb-1 gl-flex gl-min-h-7 gl-items-center gl-gap-3 gl-py-2 !gl-text-default !gl-no-underline focus:gl-focus gl-px-3 gl-rounded-base" data-testid="nav-item-link" aria-label="CI/CD analytics" data-track-action="click_menu_item" data-track-label="ci_cd_analytics" data-track-property="nav_panel_project" data-qa-submenu-item="CI/CD analytics"><div aria-hidden="true" data-testid="active-indicator" class="active-indicator gl-absolute gl-bottom-2 gl-left-2 gl-top-2 gl-transition-all gl-duration-slow gl-opacity-0" style="width: 3px; border-radius: 3px; margin-right: 1px;"></div> <div class="gl-flex gl-w-6 gl-shrink-0"><!----></div> <div data-testid="nav-item-link-label" class="gl-grow gl-text-default gl-break-anywhere">
      CI/CD analytics
      <!----></div>  <span class="gl-flex gl-min-w-6 gl-items-start gl-justify-end"><!----></span></a> <button aria-label="Pin CI/CD analytics" data-testid="nav-item-pin" type="button" class="btn show-on-focus-or-hover--target transition-opacity-on-hover--target always-animate gl-absolute gl-right-3 gl-top-1/2 -gl-translate-y-1/2 btn-default btn-sm gl-button btn-default-tertiary btn-icon gl-pointer-events-none"><!----> <svg data-testid="thumbtack-icon" role="img" aria-hidden="true" class="gl-button-icon gl-icon s16 gl-fill-current"><use href="/assets/icons-ca86b3ebff8cbe14f7728864eabad153d00b66986018226fe439015884de11c2.svg#thumbtack"></use></svg>  <!----></button></li><li data-testid="nav-item" class="show-on-focus-or-hover--context hide-on-focus-or-hover--context transition-opacity-on-hover--context gl-relative"><a href="https://gitlab-v2.innotech-vn.com/the-ai-team/innocody/innocody-engine/-/graphs/main/charts" class="super-sidebar-nav-item show-on-focus-or-hover--control hide-on-focus-or-hover--control gl-relative gl-mb-1 gl-flex gl-min-h-7 gl-items-center gl-gap-3 gl-py-2 !gl-text-default !gl-no-underline focus:gl-focus gl-px-3 gl-rounded-base shortcuts-repository-charts" data-testid="nav-item-link" aria-label="Repository analytics" data-track-action="click_menu_item" data-track-label="repository_analytics" data-track-property="nav_panel_project" data-qa-submenu-item="Repository analytics"><div aria-hidden="true" data-testid="active-indicator" class="active-indicator gl-absolute gl-bottom-2 gl-left-2 gl-top-2 gl-transition-all gl-duration-slow gl-opacity-0" style="width: 3px; border-radius: 3px; margin-right: 1px;"></div> <div class="gl-flex gl-w-6 gl-shrink-0"><!----></div> <div data-testid="nav-item-link-label" class="gl-grow gl-text-default gl-break-anywhere">
      Repository analytics
      <!----></div>  <span class="gl-flex gl-min-w-6 gl-items-start gl-justify-end"><!----></span></a> <button aria-label="Pin Repository analytics" data-testid="nav-item-pin" type="button" class="btn show-on-focus-or-hover--target transition-opacity-on-hover--target always-animate gl-absolute gl-right-3 gl-top-1/2 -gl-translate-y-1/2 btn-default btn-sm gl-button btn-default-tertiary btn-icon gl-pointer-events-none"><!----> <svg data-testid="thumbtack-icon" role="img" aria-hidden="true" class="gl-button-icon gl-icon s16 gl-fill-current"><use href="/assets/icons-ca86b3ebff8cbe14f7728864eabad153d00b66986018226fe439015884de11c2.svg#thumbtack"></use></svg>  <!----></button></li><li data-testid="nav-item" class="show-on-focus-or-hover--context hide-on-focus-or-hover--context transition-opacity-on-hover--context gl-relative"><a href="https://gitlab-v2.innotech-vn.com/the-ai-team/innocody/innocody-engine/-/ml/experiments" class="super-sidebar-nav-item show-on-focus-or-hover--control hide-on-focus-or-hover--control gl-relative gl-mb-1 gl-flex gl-min-h-7 gl-items-center gl-gap-3 gl-py-2 !gl-text-default !gl-no-underline focus:gl-focus gl-px-3 gl-rounded-base" data-testid="nav-item-link" aria-label="Model experiments" data-track-action="click_menu_item" data-track-label="model_experiments" data-track-property="nav_panel_project" data-qa-submenu-item="Model experiments"><div aria-hidden="true" data-testid="active-indicator" class="active-indicator gl-absolute gl-bottom-2 gl-left-2 gl-top-2 gl-transition-all gl-duration-slow gl-opacity-0" style="width: 3px; border-radius: 3px; margin-right: 1px;"></div> <div class="gl-flex gl-w-6 gl-shrink-0"><!----></div> <div data-testid="nav-item-link-label" class="gl-grow gl-text-default gl-break-anywhere">
      Model experiments
      <!----></div>  <span class="gl-flex gl-min-w-6 gl-items-start gl-justify-end"><!----></span></a> <button aria-label="Pin Model experiments" data-testid="nav-item-pin" type="button" class="btn show-on-focus-or-hover--target transition-opacity-on-hover--target always-animate gl-absolute gl-right-3 gl-top-1/2 -gl-translate-y-1/2 btn-default btn-sm gl-button btn-default-tertiary btn-icon gl-pointer-events-none"><!----> <svg data-testid="thumbtack-icon" role="img" aria-hidden="true" class="gl-button-icon gl-icon s16 gl-fill-current"><use href="/assets/icons-ca86b3ebff8cbe14f7728864eabad153d00b66986018226fe439015884de11c2.svg#thumbtack"></use></svg>  <!----></button></li></ul></div></li><li><hr aria-hidden="true" class="gl-mx-4 gl-my-2"> <button id="menu-section-button-settings" data-testid="menu-section-button" data-qa-section-name="Settings" aria-controls="settings" aria-expanded="false" data-qa-menu-item="Settings" class="super-sidebar-nav-item gl-relative gl-mb-2 gl-flex gl-min-h-7 gl-w-full gl-appearance-none gl-items-center gl-gap-3 gl-rounded-base gl-border-0 gl-bg-transparent gl-px-3 gl-py-2 gl-text-left !gl-text-default !gl-no-underline focus:gl-focus"><span aria-hidden="true" class="gl-absolute gl-bottom-2 gl-left-2 gl-top-2 gl-transition-all gl-duration-slow gl-bg-transparent" style="width: 3px; border-radius: 3px; margin-right: 1px;"></span> <span class="gl-flex gl-w-6 gl-shrink-0"><svg data-testid="settings-icon" role="img" aria-hidden="true" class="super-sidebar-nav-item-icon gl-m-auto gl-icon s16 gl-fill-current"><use href="/assets/icons-ca86b3ebff8cbe14f7728864eabad153d00b66986018226fe439015884de11c2.svg#settings"></use></svg></span> <span class="gl-truncate-end gl-grow gl-text-default">
      Settings
    </span> <span class="gl-text-right gl-text-subtle"><svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg" class="gl-animated-icon gl-animated-icon-off gl-animated-icon-current"><path d="M6.75 4.75L10 8L6.75 11.25" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="gl-animated-chevron-right-down-arrow"></path></svg></span></button> <!----> <div class="gl-m-0 gl-list-none gl-p-0 gl-duration-medium gl-ease-ease collapse" id="settings" data-testid="menu-section" data-qa-section-name="Settings" style="display: none;"><ul aria-label="Settings" class="gl-m-0 gl-list-none gl-p-0"><li data-testid="nav-item" class="show-on-focus-or-hover--context hide-on-focus-or-hover--context transition-opacity-on-hover--context gl-relative"><a href="https://gitlab-v2.innotech-vn.com/the-ai-team/innocody/innocody-engine/edit" class="super-sidebar-nav-item show-on-focus-or-hover--control hide-on-focus-or-hover--control gl-relative gl-mb-1 gl-flex gl-min-h-7 gl-items-center gl-gap-3 gl-py-2 !gl-text-default !gl-no-underline focus:gl-focus gl-px-3 gl-rounded-base" data-testid="nav-item-link" aria-label="General" data-track-action="click_menu_item" data-track-label="general" data-track-property="nav_panel_project" data-qa-submenu-item="General"><div aria-hidden="true" data-testid="active-indicator" class="active-indicator gl-absolute gl-bottom-2 gl-left-2 gl-top-2 gl-transition-all gl-duration-slow gl-opacity-0" style="width: 3px; border-radius: 3px; margin-right: 1px;"></div> <div class="gl-flex gl-w-6 gl-shrink-0"><!----></div> <div data-testid="nav-item-link-label" class="gl-grow gl-text-default gl-break-anywhere">
      General
      <!----></div>  <span class="gl-flex gl-min-w-6 gl-items-start gl-justify-end"><!----></span></a> <button aria-label="Pin General" data-testid="nav-item-pin" type="button" class="btn show-on-focus-or-hover--target transition-opacity-on-hover--target always-animate gl-absolute gl-right-3 gl-top-1/2 -gl-translate-y-1/2 btn-default btn-sm gl-button btn-default-tertiary btn-icon gl-pointer-events-none"><!----> <svg data-testid="thumbtack-icon" role="img" aria-hidden="true" class="gl-button-icon gl-icon s16 gl-fill-current"><use href="/assets/icons-ca86b3ebff8cbe14f7728864eabad153d00b66986018226fe439015884de11c2.svg#thumbtack"></use></svg>  <!----></button></li><li data-testid="nav-item" class="show-on-focus-or-hover--context hide-on-focus-or-hover--context transition-opacity-on-hover--context gl-relative"><a href="https://gitlab-v2.innotech-vn.com/the-ai-team/innocody/innocody-engine/-/settings/integrations" class="super-sidebar-nav-item show-on-focus-or-hover--control hide-on-focus-or-hover--control gl-relative gl-mb-1 gl-flex gl-min-h-7 gl-items-center gl-gap-3 gl-py-2 !gl-text-default !gl-no-underline focus:gl-focus gl-px-3 gl-rounded-base" data-testid="nav-item-link" aria-label="Integrations" data-track-action="click_menu_item" data-track-label="integrations" data-track-property="nav_panel_project" data-qa-submenu-item="Integrations"><div aria-hidden="true" data-testid="active-indicator" class="active-indicator gl-absolute gl-bottom-2 gl-left-2 gl-top-2 gl-transition-all gl-duration-slow gl-opacity-0" style="width: 3px; border-radius: 3px; margin-right: 1px;"></div> <div class="gl-flex gl-w-6 gl-shrink-0"><!----></div> <div data-testid="nav-item-link-label" class="gl-grow gl-text-default gl-break-anywhere">
      Integrations
      <!----></div>  <span class="gl-flex gl-min-w-6 gl-items-start gl-justify-end"><!----></span></a> <button aria-label="Pin Integrations" data-testid="nav-item-pin" type="button" class="btn show-on-focus-or-hover--target transition-opacity-on-hover--target always-animate gl-absolute gl-right-3 gl-top-1/2 -gl-translate-y-1/2 btn-default btn-sm gl-button btn-default-tertiary btn-icon gl-pointer-events-none"><!----> <svg data-testid="thumbtack-icon" role="img" aria-hidden="true" class="gl-button-icon gl-icon s16 gl-fill-current"><use href="/assets/icons-ca86b3ebff8cbe14f7728864eabad153d00b66986018226fe439015884de11c2.svg#thumbtack"></use></svg>  <!----></button></li><li data-testid="nav-item" class="show-on-focus-or-hover--context hide-on-focus-or-hover--context transition-opacity-on-hover--context gl-relative"><a href="https://gitlab-v2.innotech-vn.com/the-ai-team/innocody/innocody-engine/-/hooks" class="super-sidebar-nav-item show-on-focus-or-hover--control hide-on-focus-or-hover--control gl-relative gl-mb-1 gl-flex gl-min-h-7 gl-items-center gl-gap-3 gl-py-2 !gl-text-default !gl-no-underline focus:gl-focus gl-px-3 gl-rounded-base" data-testid="nav-item-link" aria-label="Webhooks" data-track-action="click_menu_item" data-track-label="webhooks" data-track-property="nav_panel_project" data-qa-submenu-item="Webhooks"><div aria-hidden="true" data-testid="active-indicator" class="active-indicator gl-absolute gl-bottom-2 gl-left-2 gl-top-2 gl-transition-all gl-duration-slow gl-opacity-0" style="width: 3px; border-radius: 3px; margin-right: 1px;"></div> <div class="gl-flex gl-w-6 gl-shrink-0"><!----></div> <div data-testid="nav-item-link-label" class="gl-grow gl-text-default gl-break-anywhere">
      Webhooks
      <!----></div>  <span class="gl-flex gl-min-w-6 gl-items-start gl-justify-end"><!----></span></a> <button aria-label="Pin Webhooks" data-testid="nav-item-pin" type="button" class="btn show-on-focus-or-hover--target transition-opacity-on-hover--target always-animate gl-absolute gl-right-3 gl-top-1/2 -gl-translate-y-1/2 btn-default btn-sm gl-button btn-default-tertiary btn-icon gl-pointer-events-none"><!----> <svg data-testid="thumbtack-icon" role="img" aria-hidden="true" class="gl-button-icon gl-icon s16 gl-fill-current"><use href="/assets/icons-ca86b3ebff8cbe14f7728864eabad153d00b66986018226fe439015884de11c2.svg#thumbtack"></use></svg>  <!----></button></li><li data-testid="nav-item" class="show-on-focus-or-hover--context hide-on-focus-or-hover--context transition-opacity-on-hover--context gl-relative"><a href="https://gitlab-v2.innotech-vn.com/the-ai-team/innocody/innocody-engine/-/settings/access_tokens" class="super-sidebar-nav-item show-on-focus-or-hover--control hide-on-focus-or-hover--control gl-relative gl-mb-1 gl-flex gl-min-h-7 gl-items-center gl-gap-3 gl-py-2 !gl-text-default !gl-no-underline focus:gl-focus gl-px-3 gl-rounded-base" data-testid="nav-item-link" aria-label="Access tokens" data-track-action="click_menu_item" data-track-label="access_tokens" data-track-property="nav_panel_project" data-qa-submenu-item="Access tokens"><div aria-hidden="true" data-testid="active-indicator" class="active-indicator gl-absolute gl-bottom-2 gl-left-2 gl-top-2 gl-transition-all gl-duration-slow gl-opacity-0" style="width: 3px; border-radius: 3px; margin-right: 1px;"></div> <div class="gl-flex gl-w-6 gl-shrink-0"><!----></div> <div data-testid="nav-item-link-label" class="gl-grow gl-text-default gl-break-anywhere">
      Access tokens
      <!----></div>  <span class="gl-flex gl-min-w-6 gl-items-start gl-justify-end"><!----></span></a> <button aria-label="Pin Access tokens" data-testid="nav-item-pin" type="button" class="btn show-on-focus-or-hover--target transition-opacity-on-hover--target always-animate gl-absolute gl-right-3 gl-top-1/2 -gl-translate-y-1/2 btn-default btn-sm gl-button btn-default-tertiary btn-icon gl-pointer-events-none"><!----> <svg data-testid="thumbtack-icon" role="img" aria-hidden="true" class="gl-button-icon gl-icon s16 gl-fill-current"><use href="/assets/icons-ca86b3ebff8cbe14f7728864eabad153d00b66986018226fe439015884de11c2.svg#thumbtack"></use></svg>  <!----></button></li><li data-testid="nav-item" class="show-on-focus-or-hover--context hide-on-focus-or-hover--context transition-opacity-on-hover--context gl-relative"><a href="https://gitlab-v2.innotech-vn.com/the-ai-team/innocody/innocody-engine/-/settings/repository" class="super-sidebar-nav-item show-on-focus-or-hover--control hide-on-focus-or-hover--control gl-relative gl-mb-1 gl-flex gl-min-h-7 gl-items-center gl-gap-3 gl-py-2 !gl-text-default !gl-no-underline focus:gl-focus gl-px-3 gl-rounded-base" data-testid="nav-item-link" aria-label="Repository" data-track-action="click_menu_item" data-track-label="repository" data-track-property="nav_panel_project" data-qa-submenu-item="Repository"><div aria-hidden="true" data-testid="active-indicator" class="active-indicator gl-absolute gl-bottom-2 gl-left-2 gl-top-2 gl-transition-all gl-duration-slow gl-opacity-0" style="width: 3px; border-radius: 3px; margin-right: 1px;"></div> <div class="gl-flex gl-w-6 gl-shrink-0"><!----></div> <div data-testid="nav-item-link-label" class="gl-grow gl-text-default gl-break-anywhere">
      Repository
      <!----></div>  <span class="gl-flex gl-min-w-6 gl-items-start gl-justify-end"><!----></span></a> <button aria-label="Pin Repository" data-testid="nav-item-pin" type="button" class="btn show-on-focus-or-hover--target transition-opacity-on-hover--target always-animate gl-absolute gl-right-3 gl-top-1/2 -gl-translate-y-1/2 btn-default btn-sm gl-button btn-default-tertiary btn-icon gl-pointer-events-none"><!----> <svg data-testid="thumbtack-icon" role="img" aria-hidden="true" class="gl-button-icon gl-icon s16 gl-fill-current"><use href="/assets/icons-ca86b3ebff8cbe14f7728864eabad153d00b66986018226fe439015884de11c2.svg#thumbtack"></use></svg>  <!----></button></li><li data-testid="nav-item" class="show-on-focus-or-hover--context hide-on-focus-or-hover--context transition-opacity-on-hover--context gl-relative"><a href="https://gitlab-v2.innotech-vn.com/the-ai-team/innocody/innocody-engine/-/settings/merge_requests" class="super-sidebar-nav-item show-on-focus-or-hover--control hide-on-focus-or-hover--control gl-relative gl-mb-1 gl-flex gl-min-h-7 gl-items-center gl-gap-3 gl-py-2 !gl-text-default !gl-no-underline focus:gl-focus gl-px-3 gl-rounded-base" data-testid="nav-item-link" aria-label="Merge requests" data-track-action="click_menu_item" data-track-label="merge_request_settings" data-track-property="nav_panel_project" data-qa-submenu-item="Merge requests"><div aria-hidden="true" data-testid="active-indicator" class="active-indicator gl-absolute gl-bottom-2 gl-left-2 gl-top-2 gl-transition-all gl-duration-slow gl-opacity-0" style="width: 3px; border-radius: 3px; margin-right: 1px;"></div> <div class="gl-flex gl-w-6 gl-shrink-0"><!----></div> <div data-testid="nav-item-link-label" class="gl-grow gl-text-default gl-break-anywhere">
      Merge requests
      <!----></div>  <span class="gl-flex gl-min-w-6 gl-items-start gl-justify-end"><!----></span></a> <button aria-label="Pin Merge requests" data-testid="nav-item-pin" type="button" class="btn show-on-focus-or-hover--target transition-opacity-on-hover--target always-animate gl-absolute gl-right-3 gl-top-1/2 -gl-translate-y-1/2 btn-default btn-sm gl-button btn-default-tertiary btn-icon gl-pointer-events-none"><!----> <svg data-testid="thumbtack-icon" role="img" aria-hidden="true" class="gl-button-icon gl-icon s16 gl-fill-current"><use href="/assets/icons-ca86b3ebff8cbe14f7728864eabad153d00b66986018226fe439015884de11c2.svg#thumbtack"></use></svg>  <!----></button></li><li data-testid="nav-item" class="show-on-focus-or-hover--context hide-on-focus-or-hover--context transition-opacity-on-hover--context gl-relative"><a href="https://gitlab-v2.innotech-vn.com/the-ai-team/innocody/innocody-engine/-/settings/ci_cd" class="super-sidebar-nav-item show-on-focus-or-hover--control hide-on-focus-or-hover--control gl-relative gl-mb-1 gl-flex gl-min-h-7 gl-items-center gl-gap-3 gl-py-2 !gl-text-default !gl-no-underline focus:gl-focus gl-px-3 gl-rounded-base" data-testid="nav-item-link" aria-label="CI/CD" data-track-action="click_menu_item" data-track-label="ci_cd" data-track-property="nav_panel_project" data-qa-submenu-item="CI/CD"><div aria-hidden="true" data-testid="active-indicator" class="active-indicator gl-absolute gl-bottom-2 gl-left-2 gl-top-2 gl-transition-all gl-duration-slow gl-opacity-0" style="width: 3px; border-radius: 3px; margin-right: 1px;"></div> <div class="gl-flex gl-w-6 gl-shrink-0"><!----></div> <div data-testid="nav-item-link-label" class="gl-grow gl-text-default gl-break-anywhere">
      CI/CD
      <!----></div>  <span class="gl-flex gl-min-w-6 gl-items-start gl-justify-end"><!----></span></a> <button aria-label="Pin CI/CD" data-testid="nav-item-pin" type="button" class="btn show-on-focus-or-hover--target transition-opacity-on-hover--target always-animate gl-absolute gl-right-3 gl-top-1/2 -gl-translate-y-1/2 btn-default btn-sm gl-button btn-default-tertiary btn-icon gl-pointer-events-none"><!----> <svg data-testid="thumbtack-icon" role="img" aria-hidden="true" class="gl-button-icon gl-icon s16 gl-fill-current"><use href="/assets/icons-ca86b3ebff8cbe14f7728864eabad153d00b66986018226fe439015884de11c2.svg#thumbtack"></use></svg>  <!----></button></li><li data-testid="nav-item" class="show-on-focus-or-hover--context hide-on-focus-or-hover--context transition-opacity-on-hover--context gl-relative"><a href="https://gitlab-v2.innotech-vn.com/the-ai-team/innocody/innocody-engine/-/settings/packages_and_registries" class="super-sidebar-nav-item show-on-focus-or-hover--control hide-on-focus-or-hover--control gl-relative gl-mb-1 gl-flex gl-min-h-7 gl-items-center gl-gap-3 gl-py-2 !gl-text-default !gl-no-underline focus:gl-focus gl-px-3 gl-rounded-base" data-testid="nav-item-link" aria-label="Packages and registries" data-track-action="click_menu_item" data-track-label="packages_and_registries" data-track-property="nav_panel_project" data-qa-submenu-item="Packages and registries"><div aria-hidden="true" data-testid="active-indicator" class="active-indicator gl-absolute gl-bottom-2 gl-left-2 gl-top-2 gl-transition-all gl-duration-slow gl-opacity-0" style="width: 3px; border-radius: 3px; margin-right: 1px;"></div> <div class="gl-flex gl-w-6 gl-shrink-0"><!----></div> <div data-testid="nav-item-link-label" class="gl-grow gl-text-default gl-break-anywhere">
      Packages and registries
      <!----></div>  <span class="gl-flex gl-min-w-6 gl-items-start gl-justify-end"><!----></span></a> <button aria-label="Pin Packages and registries" data-testid="nav-item-pin" type="button" class="btn show-on-focus-or-hover--target transition-opacity-on-hover--target always-animate gl-absolute gl-right-3 gl-top-1/2 -gl-translate-y-1/2 btn-default btn-sm gl-button btn-default-tertiary btn-icon gl-pointer-events-none"><!----> <svg data-testid="thumbtack-icon" role="img" aria-hidden="true" class="gl-button-icon gl-icon s16 gl-fill-current"><use href="/assets/icons-ca86b3ebff8cbe14f7728864eabad153d00b66986018226fe439015884de11c2.svg#thumbtack"></use></svg>  <!----></button></li><li data-testid="nav-item" class="show-on-focus-or-hover--context hide-on-focus-or-hover--context transition-opacity-on-hover--context gl-relative"><a href="https://gitlab-v2.innotech-vn.com/the-ai-team/innocody/innocody-engine/-/settings/operations" class="super-sidebar-nav-item show-on-focus-or-hover--control hide-on-focus-or-hover--control gl-relative gl-mb-1 gl-flex gl-min-h-7 gl-items-center gl-gap-3 gl-py-2 !gl-text-default !gl-no-underline focus:gl-focus gl-px-3 gl-rounded-base" data-testid="nav-item-link" aria-label="Monitor" data-track-action="click_menu_item" data-track-label="monitor" data-track-property="nav_panel_project" data-qa-submenu-item="Monitor"><div aria-hidden="true" data-testid="active-indicator" class="active-indicator gl-absolute gl-bottom-2 gl-left-2 gl-top-2 gl-transition-all gl-duration-slow gl-opacity-0" style="width: 3px; border-radius: 3px; margin-right: 1px;"></div> <div class="gl-flex gl-w-6 gl-shrink-0"><!----></div> <div data-testid="nav-item-link-label" class="gl-grow gl-text-default gl-break-anywhere">
      Monitor
      <!----></div>  <span class="gl-flex gl-min-w-6 gl-items-start gl-justify-end"><!----></span></a> <button aria-label="Pin Monitor" data-testid="nav-item-pin" type="button" class="btn show-on-focus-or-hover--target transition-opacity-on-hover--target always-animate gl-absolute gl-right-3 gl-top-1/2 -gl-translate-y-1/2 btn-default btn-sm gl-button btn-default-tertiary btn-icon gl-pointer-events-none"><!----> <svg data-testid="thumbtack-icon" role="img" aria-hidden="true" class="gl-button-icon gl-icon s16 gl-fill-current"><use href="/assets/icons-ca86b3ebff8cbe14f7728864eabad153d00b66986018226fe439015884de11c2.svg#thumbtack"></use></svg>  <!----></button></li><li data-testid="nav-item" class="show-on-focus-or-hover--context hide-on-focus-or-hover--context transition-opacity-on-hover--context gl-relative"><a href="https://gitlab-v2.innotech-vn.com/the-ai-team/innocody/innocody-engine/-/usage_quotas" class="super-sidebar-nav-item show-on-focus-or-hover--control hide-on-focus-or-hover--control gl-relative gl-mb-1 gl-flex gl-min-h-7 gl-items-center gl-gap-3 gl-py-2 !gl-text-default !gl-no-underline focus:gl-focus gl-px-3 gl-rounded-base" data-testid="nav-item-link" aria-label="Usage Quotas" data-track-action="click_menu_item" data-track-label="usage_quotas" data-track-property="nav_panel_project" data-qa-submenu-item="Usage Quotas"><div aria-hidden="true" data-testid="active-indicator" class="active-indicator gl-absolute gl-bottom-2 gl-left-2 gl-top-2 gl-transition-all gl-duration-slow gl-opacity-0" style="width: 3px; border-radius: 3px; margin-right: 1px;"></div> <div class="gl-flex gl-w-6 gl-shrink-0"><!----></div> <div data-testid="nav-item-link-label" class="gl-grow gl-text-default gl-break-anywhere">
      Usage Quotas
      <!----></div>  <span class="gl-flex gl-min-w-6 gl-items-start gl-justify-end"><!----></span></a> <button aria-label="Pin Usage Quotas" data-testid="nav-item-pin" type="button" class="btn show-on-focus-or-hover--target transition-opacity-on-hover--target always-animate gl-absolute gl-right-3 gl-top-1/2 -gl-translate-y-1/2 btn-default btn-sm gl-button btn-default-tertiary btn-icon gl-pointer-events-none"><!----> <svg data-testid="thumbtack-icon" role="img" aria-hidden="true" class="gl-button-icon gl-icon s16 gl-fill-current"><use href="/assets/icons-ca86b3ebff8cbe14f7728864eabad153d00b66986018226fe439015884de11c2.svg#thumbtack"></use></svg>  <!----></button></li></ul></div></li></ul></div> <div id="sidebar-portal-mount"></div> <div></div> <div class="bottom-scrim-wrapper"><div class="bottom-scrim"></div></div></div> <!----> <div class="gl-p-2"><div class="gl-disclosure-dropdown gl-new-dropdown"><div id="dropdown-toggle-btn-18" data-testid="base-dropdown-toggle" class="gl-new-dropdown-custom-toggle"><button data-testid="sidebar-help-button" type="button" class="btn super-sidebar-help-center-toggle btn-with-notification btn-default btn-md gl-button btn-default-tertiary"><!----> <svg data-testid="question-o-icon" role="img" aria-hidden="true" class="gl-button-icon gl-icon s16 gl-fill-current"><use href="/assets/icons-ca86b3ebff8cbe14f7728864eabad153d00b66986018226fe439015884de11c2.svg#question-o"></use></svg>  <span class="gl-button-text"><span data-testid="notification-dot" class="notification-dot-info"></span>
      Help
    </span></button></div> <div id="base-dropdown-20" data-testid="base-dropdown-menu" class="gl-new-dropdown-panel !gl-w-31"><div class="gl-new-dropdown-arrow"></div> <div class="gl-new-dropdown-inner">  <ul id="disclosure-19" aria-labelledby="dropdown-toggle-btn-18" data-testid="disclosure-content" tabindex="-1" class="gl-new-dropdown-contents"> <!----> <li><!----> <ul class="gl-mb-0 gl-list-none gl-pl-0"><li tabindex="0" data-testid="disclosure-dropdown-item" class="gl-new-dropdown-item"><a href="https://gitlab-v2.innotech-vn.com/help" tabindex="-1" data-track-property="nav_help_menu" data-track-action="click_link" data-track-label="help" class="gl-new-dropdown-item-content"><span class="gl-new-dropdown-item-text-wrapper">
          Help
        </span></a></li><li tabindex="0" data-testid="disclosure-dropdown-item" class="gl-new-dropdown-item"><a href="https://about.gitlab.com/get-help/" tabindex="-1" data-track-property="nav_help_menu" data-track-action="click_link" data-track-label="support" class="gl-new-dropdown-item-content"><span class="gl-new-dropdown-item-text-wrapper">
          Support
        </span></a></li><li tabindex="0" data-testid="disclosure-dropdown-item" class="gl-new-dropdown-item"><a href="https://gitlab-v2.innotech-vn.com/help/docs" tabindex="-1" data-track-property="nav_help_menu" data-track-action="click_link" data-track-label="gitlab_documentation" class="gl-new-dropdown-item-content"><span class="gl-new-dropdown-item-text-wrapper">
          GitLab documentation
        </span></a></li><li tabindex="0" data-testid="disclosure-dropdown-item" class="gl-new-dropdown-item"><a href="https://about.gitlab.com/pricing" tabindex="-1" data-track-property="nav_help_menu" data-track-action="click_link" data-track-label="compare_gitlab_plans" class="gl-new-dropdown-item-content"><span class="gl-new-dropdown-item-text-wrapper">
          Compare GitLab plans
        </span></a></li><li tabindex="0" data-testid="disclosure-dropdown-item" class="gl-new-dropdown-item"><a href="https://forum.gitlab.com/" tabindex="-1" data-track-property="nav_help_menu" data-track-action="click_link" data-track-label="community_forum" class="gl-new-dropdown-item-content"><span class="gl-new-dropdown-item-text-wrapper">
          Community forum
        </span></a></li><li tabindex="0" data-testid="disclosure-dropdown-item" class="gl-new-dropdown-item"><a href="https://contributors.gitlab.com/" tabindex="-1" data-track-property="nav_help_menu" data-track-action="click_link" data-track-label="contribute_to_gitlab" class="gl-new-dropdown-item-content"><span class="gl-new-dropdown-item-text-wrapper">
          Contribute to GitLab
        </span></a></li><li tabindex="0" data-testid="disclosure-dropdown-item" class="gl-new-dropdown-item"><a href="https://about.gitlab.com/submit-feedback" tabindex="-1" data-track-property="nav_help_menu" data-track-action="click_link" data-track-label="submit_feedback" class="gl-new-dropdown-item-content"><span class="gl-new-dropdown-item-text-wrapper">
          Provide feedback
        </span></a></li></ul></li> <li class="gl-border-t gl-border-t-dropdown-divider gl-pt-2 gl-mt-2"><!----> <ul class="gl-mb-0 gl-list-none gl-pl-0"><li tabindex="0" data-testid="disclosure-dropdown-item" class="gl-new-dropdown-item"><button tabindex="-1" data-track-action="click_button" data-track-label="keyboard_shortcuts_help" data-track-property="nav_help_menu" type="button" class="gl-new-dropdown-item-content js-shortcuts-modal-trigger"><span class="gl-new-dropdown-item-text-wrapper"><span class="-gl-my-1 gl-flex gl-items-center gl-justify-between">
        Keyboard shortcuts
        <kbd aria-hidden="true" class="flat">?</kbd></span></span></button></li><li tabindex="0" data-testid="disclosure-dropdown-item" class="gl-new-dropdown-item"><button tabindex="-1" data-track-action="click_button" data-track-label="whats_new" data-track-property="nav_help_menu" type="button" class="gl-new-dropdown-item-content"><span class="gl-new-dropdown-item-text-wrapper"><span class="-gl-my-1 gl-flex gl-items-center gl-justify-between">
        What's new
        <span class="gl-badge badge badge-pill badge-info" pill="" aria-hidden="true"><!----> <span class="gl-badge-content">6</span></span></span></span></button></li></ul></li></ul> </div></div></div> <!----> <div></div></div></div></nav> <a href="https://gitlab-v2.innotech-vn.com/dashboard/milestones" class="gl-hidden dashboard-shortcuts-milestones">
    Milestones
  </a><a href="https://gitlab-v2.innotech-vn.com/dashboard/snippets" class="gl-hidden dashboard-shortcuts-snippets">
    Snippets
  </a><a href="https://gitlab-v2.innotech-vn.com/dashboard/activity" class="gl-hidden dashboard-shortcuts-activity">
    Activity
  </a><a href="https://gitlab-v2.innotech-vn.com/dashboard/groups" class="gl-hidden dashboard-shortcuts-groups">
    Groups
  </a><a href="https://gitlab-v2.innotech-vn.com/dashboard/projects" class="gl-hidden dashboard-shortcuts-projects">
    Projects
  </a><a href="https://gitlab-v2.innotech-vn.com/the-ai-team/innocody/innocody-engine/-/issues/new" class="gl-hidden shortcuts-new-issue">
    Create a new issue
  </a> <!----> <!----></div>


<div class="content-wrapper">
<div class="broadcast-wrapper">



</div>
<div class="alert-wrapper alert-wrapper-top-space gl-flex gl-flex-col gl-gap-3 container-fluid container-limited">

























</div>
<div class="top-bar-fixed container-fluid" data-testid="top-bar">
<div class="top-bar-container gl-flex gl-items-center gl-gap-2">
<div class="gl-grow gl-basis-0 gl-flex gl-items-center gl-justify-start gl-gap-3">
<button aria-controls="super-sidebar" aria-expanded="false" aria-label="Primary navigation sidebar" type="button" class="btn btn-default btn-md gl-button btn-default-tertiary btn-icon gl-button btn btn-icon btn-md btn-default btn-default-tertiary js-super-sidebar-toggle-expand super-sidebar-toggle -gl-ml-3"><!----> <svg data-testid="sidebar-icon" role="img" aria-hidden="true" class="gl-button-icon gl-icon s16 gl-fill-current"><use href="/assets/icons-ca86b3ebff8cbe14f7728864eabad153d00b66986018226fe439015884de11c2.svg#sidebar"></use></svg>  <!----></button>
<script type="application/ld+json">
{"@context":"https://schema.org","@type":"BreadcrumbList","itemListElement":[{"@type":"ListItem","position":1,"name":"The AI Team","item":"https://gitlab-v2.innotech-vn.com/the-ai-team"},{"@type":"ListItem","position":2,"name":"innocody","item":"https://gitlab-v2.innotech-vn.com/the-ai-team/innocody"},{"@type":"ListItem","position":3,"name":"innocody-engine","item":"https://gitlab-v2.innotech-vn.com/the-ai-team/innocody/innocody-engine"},{"@type":"ListItem","position":4,"name":"Commits","item":"https://gitlab-v2.innotech-vn.com/the-ai-team/innocody/innocody-engine/-/commit/84acef7360827487fbc4ee5895d38b2c8c04a0c5/pipelines"}]}


</script>
<div data-testid="breadcrumb-links" id="js-vue-page-breadcrumbs-wrapper">
<nav aria-label="Breadcrumb" class="gl-breadcrumbs" style=""><ol class="gl-breadcrumb-list breadcrumb"><!----> <li class="gl-breadcrumb-item gl-breadcrumb-item-sm"><a href="https://gitlab-v2.innotech-vn.com/the-ai-team" class=""><!----><span class="gl-align-middle">The AI Team</span></a></li><li class="gl-breadcrumb-item gl-breadcrumb-item-sm"><a href="https://gitlab-v2.innotech-vn.com/the-ai-team/innocody" class=""><!----><span class="gl-align-middle">innocody</span></a></li><li class="gl-breadcrumb-item gl-breadcrumb-item-sm"><a href="https://gitlab-v2.innotech-vn.com/the-ai-team/innocody/innocody-engine" class=""><!----><span class="gl-align-middle">innocody-engine</span></a></li><li class="gl-breadcrumb-item gl-breadcrumb-item-sm"><a href="https://gitlab-v2.innotech-vn.com/the-ai-team/innocody/innocody-engine/-/commit/84acef7360827487fbc4ee5895d38b2c8c04a0c5/pipelines" aria-current="page" class=""><!----><span class="gl-align-middle">Commits</span></a></li></ol></nav>
<div id="js-injected-page-breadcrumbs"></div>
</div>


</div>
<div class="gl-flex-none gl-flex gl-items-center gl-justify-center gl-gap-3">
<div id="js-work-item-feedback"></div>

<!---->


</div>
</div>
</div>

<div class="container-fluid container-limited project-highlight-puc">
<main class="content" id="content-body" itemscope="" itemtype="http://schema.org/SoftwareSourceCode">
<div class="flash-container flash-container-page sticky" data-testid="flash-container">
<!---->
</div>


<!---->




<div class="page-content-header">
<div class="header-main-content">
<strong>
Commit
<span class="commit-sha" data-testid="commit-sha-content">84acef73</span>
</strong>
<button class="gl-button btn btn-icon btn-sm btn-default btn-default-tertiary " title="Copy commit SHA" aria-label="Copy commit SHA" aria-live="polite" data-toggle="tooltip" data-placement="bottom" data-container="body" data-html="true" data-clipboard-text="84acef7360827487fbc4ee5895d38b2c8c04a0c5" type="button"><svg class="s16 gl-icon gl-button-icon " data-testid="copy-to-clipboard-icon"><use href="/assets/icons-ca86b3ebff8cbe14f7728864eabad153d00b66986018226fe439015884de11c2.svg#copy-to-clipboard"></use></svg>

</button>
<span class="gl-hidden sm:gl-inline">authored</span>
<time class="js-timeago" title="August 6, 2025 at 4:14:11 PM GMT+7" datetime="2025-08-06T09:14:11Z" tabindex="0" aria-label="Aug 6, 2025 4:14pm" data-toggle="tooltip" data-placement="top" data-container="body">10 minutes ago</time>
<span>by</span>
<strong>
<a href="mailto:<EMAIL>"><img alt="khangnh&#39;s avatar" src="./test_files/ac836b6a430d7f3a15fd4987c270c03d6aaf9c285b1f9237b1555252a5194c11" class="avatar s24 gl-hidden sm:gl-inline-block" title="khangnh"></a>
<a class="commit-author-link" href="mailto:<EMAIL>"><span class="commit-author-name">khangnh</span></a>
</strong>

</div>
<a class="gl-button btn btn-md btn-default gl-mr-3 gl-w-full sm:gl-w-auto gl-mb-3 sm:gl-mb-0" href="https://gitlab-v2.innotech-vn.com/the-ai-team/innocody/innocody-engine/-/tree/84acef7360827487fbc4ee5895d38b2c8c04a0c5"><span class="gl-button-text">
Browse files
</span>

</a>
<div class="gl-disclosure-dropdown gl-leading-20 gl-new-dropdown" right="" data-testid="commit-options-dropdown"><button id="dropdown-toggle-btn-36" data-testid="base-dropdown-toggle" aria-expanded="false" aria-controls="base-dropdown-38" type="button" class="btn btn-default btn-md gl-button gl-new-dropdown-toggle"><!----> <!---->  <span class="gl-button-text"><span class="gl-new-dropdown-button-text">
        Options
      </span> <svg data-testid="chevron-down-icon" role="img" aria-hidden="true" class="gl-button-icon gl-new-dropdown-chevron gl-icon s16 gl-fill-current"><use href="/assets/icons-ca86b3ebff8cbe14f7728864eabad153d00b66986018226fe439015884de11c2.svg#chevron-down"></use></svg></span></button> <div id="base-dropdown-38" data-testid="base-dropdown-menu" class="gl-new-dropdown-panel !gl-w-31"><div class="gl-new-dropdown-arrow"></div> <div class="gl-new-dropdown-inner">  <ul id="disclosure-37" aria-labelledby="dropdown-toggle-btn-36" data-testid="disclosure-content" tabindex="-1" class="gl-new-dropdown-contents"><li><!----> <ul class="gl-mb-0 gl-list-none gl-pl-0"><li tabindex="0" data-testid="disclosure-dropdown-item" class="gl-new-dropdown-item"><button tabindex="-1" data-testid="revert-link" type="button" class="gl-new-dropdown-item-content"><span class="gl-new-dropdown-item-text-wrapper">
          Revert
        </span></button></li><li tabindex="0" data-testid="disclosure-dropdown-item" class="gl-new-dropdown-item"><button tabindex="-1" data-testid="cherry-pick-link" type="button" class="gl-new-dropdown-item-content"><span class="gl-new-dropdown-item-text-wrapper">
          Cherry-pick
        </span></button></li><li tabindex="0" data-testid="disclosure-dropdown-item" class="gl-new-dropdown-item"><a href="https://gitlab-v2.innotech-vn.com/the-ai-team/innocody/innocody-engine/-/tags/new?ref=84acef7360827487fbc4ee5895d38b2c8c04a0c5" tabindex="-1" data-testid="tag-link" class="gl-new-dropdown-item-content"><span class="gl-new-dropdown-item-text-wrapper">
          Tag
        </span></a></li></ul></li> <li class="gl-border-t gl-border-t-dropdown-divider gl-pt-2 gl-mt-2"><div id="gl-disclosure-dropdown-group-43" class="gl-py-2 gl-pl-4 gl-text-sm gl-font-bold gl-text-strong">Downloads</div> <ul aria-labelledby="gl-disclosure-dropdown-group-43" class="gl-mb-0 gl-list-none gl-pl-0"><li tabindex="0" data-testid="disclosure-dropdown-item" class="gl-new-dropdown-item"><a rel="nofollow" href="https://gitlab-v2.innotech-vn.com/the-ai-team/innocody/innocody-engine/-/commit/84acef7360827487fbc4ee5895d38b2c8c04a0c5.patch" tabindex="-1" download="" data-testid="email-patches-link" class="gl-new-dropdown-item-content"><span class="gl-new-dropdown-item-text-wrapper">
          Patches
        </span></a></li><li tabindex="0" data-testid="disclosure-dropdown-item" class="gl-new-dropdown-item"><a rel="nofollow" href="https://gitlab-v2.innotech-vn.com/the-ai-team/innocody/innocody-engine/-/commit/84acef7360827487fbc4ee5895d38b2c8c04a0c5.diff" tabindex="-1" download="" data-testid="plain-diff-link" class="gl-new-dropdown-item-content"><span class="gl-new-dropdown-item-text-wrapper">
          Plain Diff
        </span></a></li></ul></li></ul> </div></div></div>
</div>
<div class="commit-box" data-project-path="/the-ai-team/innocody/innocody-engine">
<div class="gl-flex gl-flex-wrap gl-items-center gl-justify-between gl-gap-y-3 gl-my-5 commit-title">
<div class="gl-flex gl-flex-wrap md:gl-flex-nowrap gl-justify-between gl-gap-x-5 gl-gap-y-3 gl-w-full">
<h1 class="gl-heading-1 !gl-m-0" data-testid="page-heading">
feat: html parser enhance init
</h1>
</div>
</div>
</div>
<div class="info-well">
<div class="well-segment">
<div class="icon-container commit-icon">
<svg class="s16 gl-fill-icon-default" data-testid="commit-icon"><use href="/assets/icons-ca86b3ebff8cbe14f7728864eabad153d00b66986018226fe439015884de11c2.svg#commit"></use></svg>
</div>
<span class="gl-text-default">parent</span>
<a class="commit-sha" href="https://gitlab-v2.innotech-vn.com/the-ai-team/innocody/innocody-engine/-/commit/c5f18294ba7e477306cc58c4a3fbc0e0c361b687">c5f18294</a>
</div>
<div class="gl-border-t gl-border-t-section"><div class="well-segment"><div><svg data-testid="branch-icon" role="img" aria-hidden="true" class="gl-ml-2 gl-mr-3 gl-icon s16 gl-fill-icon-default"><use href="/assets/icons-ca86b3ebff8cbe14f7728864eabad153d00b66986018226fe439015884de11c2.svg#branch"></use></svg> <!----> <span data-testid="title" class="gl-mr-2">Branches</span> <a target="_self" href="https://gitlab-v2.innotech-vn.com/the-ai-team/innocody/innocody-engine/-/commits/html-parser?ref_type=heads" class="gl-mr-2 gl-mt-2 gl-badge badge badge-pill badge-muted"><!----> <span class="gl-badge-content">html-parser</span></a> <!----> <div class="collapse" style="display: none;"></div></div></div> <div class="well-segment"><div><svg data-testid="tag-icon" role="img" aria-hidden="true" class="gl-ml-2 gl-mr-3 gl-icon s16 gl-fill-icon-default"><use href="/assets/icons-ca86b3ebff8cbe14f7728864eabad153d00b66986018226fe439015884de11c2.svg#tag"></use></svg> <!----> <span>No related tags found</span>  <!----> <div class="collapse" style="display: none;"></div></div></div></div>
<div class="well-segment merge-request-info">
<div class="icon-container">
<svg class="s16 gl-fill-icon-default" data-testid="merge-request-icon"><use href="/assets/icons-ca86b3ebff8cbe14f7728864eabad153d00b66986018226fe439015884de11c2.svg#merge-request"></use></svg>
</div>
<span class="commit-info merge-requests" data-project-commit-path="/the-ai-team/innocody/innocody-engine/-/commit/84acef7360827487fbc4ee5895d38b2c8c04a0c5/merge_requests.json"><span><span class="gl-mr-2">1 merge request</span><span><a class="gl-mr-2" href="https://gitlab-v2.innotech-vn.com/the-ai-team/innocody/innocody-engine/-/merge_requests/9">!9</a><span>[SCRUM-209] Feat: add html-parser</span></span></span></span>
</div>
<div class="well-segment">
<div class="w-100"><div class="align-items-center w-100 gl-flex"><div class="flex-grow-1 justify-space-between gl-flex gl-flex-wrap gl-gap-3 align-items-center"><a variant="info" title="Running" aria-label="Status: Running" href="https://gitlab-v2.innotech-vn.com/the-ai-team/innocody/innocody-engine/-/pipelines/687" data-testid="ci-icon" class="ci-icon gl-inline-flex gl-items-center gl-text-sm gl-mt-1 gl-self-start ci-icon-variant-info"><span class="ci-icon-gl-icon-wrapper"><svg data-testid="status_running_borderless-icon" role="img" aria-hidden="true" class="gl-icon s16 gl-fill-current"><use href="/assets/icons-ca86b3ebff8cbe14f7728864eabad153d00b66986018226fe439015884de11c2.svg#status_running_borderless"></use></svg></span><!----></a> <div class="flex-grow-1 gl-flex gl-flex-col"><span>Pipeline <a href="https://gitlab-v2.innotech-vn.com/the-ai-team/innocody/innocody-engine/-/pipelines/687" data-testid="pipeline-path" class="gl-link">#687</a> running</span> <span class="align-items-center gl-flex gl-text-sm gl-text-subtle"><!----> <!----></span></div> <div data-testid="pipeline-summary-pipeline-mini-graph"><!----> <!----> <div class="gl-inline gl-align-middle"><div class="pipeline-mini-graph-stage-container dropdown gl-my-1 gl-mr-2 gl-inline-flex gl-align-middle"><div class="gl-disclosure-dropdown gl-new-dropdown" data-testid="pipeline-mini-graph-dropdown" aria-label="View Stage: test"><div id="dropdown-toggle-btn-46" data-testid="base-dropdown-toggle" class="gl-new-dropdown-custom-toggle"><button data-testid="pipeline-mini-graph-dropdown-toggle" title="test: running" type="button" class="btn !gl-rounded-full btn-link btn-md gl-button"><!----> <!---->  <span class="gl-button-text"><span variant="info" aria-label="Status: " data-testid="ci-icon" class="ci-icon gl-inline-flex gl-items-center gl-text-sm ci-icon-variant-info"><span class="ci-icon-gl-icon-wrapper"><svg data-testid="status_running_borderless-icon" role="img" aria-hidden="true" class="gl-icon s16 gl-fill-current"><use href="/assets/icons-ca86b3ebff8cbe14f7728864eabad153d00b66986018226fe439015884de11c2.svg#status_running_borderless"></use></svg></span><!----></span></span></button></div> <div id="base-dropdown-48" data-testid="base-dropdown-menu" class="gl-new-dropdown-panel"><div class="gl-new-dropdown-arrow"></div> <div class="gl-new-dropdown-inner"> <div data-testid="pipeline-stage-dropdown-menu-title" class="gl-border-b gl-flex gl-min-h-8 gl-items-center !gl-p-4 gl-text-sm gl-font-bold gl-leading-1 gl-border-b-solid"><span>Stage: test</span></div> <!----> <div id="disclosure-47" aria-labelledby="dropdown-toggle-btn-46" data-testid="disclosure-content" tabindex="-1" class="gl-new-dropdown-contents">  <ul data-testid="pipeline-mini-graph-dropdown-menu-list" class="gl-m-0 gl-w-34 gl-overflow-y-auto gl-p-0"><!----> <!----></ul></div> </div></div></div></div></div> <!----> <!----></div></div></div></div>
</div>
</div>

<ul class="commit-ci-menu gl-flex gl-grow nav gl-tabs-nav" role="tablist"><li role="presentation" class="nav-item"><a role="tab" class="nav-link gl-tab-nav-item" href="https://gitlab-v2.innotech-vn.com/the-ai-team/innocody/innocody-engine/-/commit/84acef7360827487fbc4ee5895d38b2c8c04a0c5">Changes
<span class="gl-badge badge badge-pill badge-muted gl-tab-counter-badge"><span class="gl-badge-content">9</span></span>
</a></li><li role="presentation" class="nav-item"><a role="tab" class="nav-link gl-tab-nav-item active gl-tab-nav-item-active" href="https://gitlab-v2.innotech-vn.com/the-ai-team/innocody/innocody-engine/-/commit/84acef7360827487fbc4ee5895d38b2c8c04a0c5/pipelines">Pipelines
<span class="gl-badge badge badge-pill badge-muted gl-tab-counter-badge js-pipelines-mr-count">1</span>
</a></li></ul>
<div data-artifacts-endpoint="/the-ai-team/innocody/innocody-engine/-/pipelines/:pipeline_artifacts_id/downloadable_artifacts.json" data-artifacts-endpoint-placeholder=":pipeline_artifacts_id" data-empty-state-svg-path="/assets/illustrations/empty-state/empty-pipeline-md-da65fef91300a7624533aef4ed0ccc9a4e26aea98447f3b3f38214f0b6179c54.svg" data-endpoint="/the-ai-team/innocody/innocody-engine/-/commit/84acef7360827487fbc4ee5895d38b2c8c04a0c5/pipelines" data-error-state-svg-path="/assets/illustrations/empty-state/empty-job-failed-md-de6ea5f513519f451925880fe046b96bb15e72638ea39b35795c5eeda04f4f09.svg" data-full-path="the-ai-team/innocody/innocody-engine" data-graphql-path="/api/graphql" data-project-id="8" id="commit-pipeline-table-view"><div class="content-list pipelines"><div><button data-testid="run_pipeline_button_mobile" type="button" class="btn gl-mb-3 gl-mt-3 lg:gl-hidden btn-confirm btn-md btn-block gl-button"><!----> <!---->  <span class="gl-button-text">
      Run pipeline
    </span></button> <div class="ci-table" view-type="root"><table role="table" aria-busy="" aria-colcount="5" class="table b-table gl-table b-table-fixed b-table-stacked-lg" id="__BVID__538"><!----><colgroup><col class="gl-w-3/20"><col class="gl-w-6/20"><col class="gl-w-3/20"><col class="gl-w-5/20"><col class="gl-w-4/20"></colgroup><thead role="rowgroup" class=""><!----><tr role="row" class=""><th role="columnheader" scope="col" aria-colindex="1" data-testid="status-th" class=""><div>Status</div></th><th role="columnheader" scope="col" aria-colindex="2" data-testid="pipeline-th" class=""><div>Pipeline</div></th><th role="columnheader" scope="col" aria-colindex="3" data-testid="triggerer-th" class=""><div>Created by</div></th><th role="columnheader" scope="col" aria-colindex="4" data-testid="stages-th" class=""><div>Stages</div></th><th role="columnheader" scope="col" aria-colindex="5" data-testid="actions-th" class=""><div class="gl-text-right"><button data-testid="run_pipeline_button" type="button" class="btn btn-default btn-md gl-button"><!----> <!---->  <span class="gl-button-text">
            Run pipeline
          </span></button></div></th></tr></thead><tbody role="rowgroup"><!----><tr role="row" data-testid="pipeline-table-row" class="!gl-border-b"><td aria-colindex="1" data-label="Status" role="cell" class="!gl-border-none"><div><div><a variant="info" aria-label="Status: Running" href="https://gitlab-v2.innotech-vn.com/the-ai-team/innocody/innocody-engine/-/pipelines/687" data-testid="ci-icon" class="ci-icon gl-inline-flex gl-items-center gl-text-sm gl-mb-2 ci-icon-variant-info"><span class="ci-icon-gl-icon-wrapper"><svg data-testid="status_running_borderless-icon" role="img" aria-hidden="true" class="gl-icon s16 gl-fill-current"><use href="/assets/icons-ca86b3ebff8cbe14f7728864eabad153d00b66986018226fe439015884de11c2.svg#status_running_borderless"></use></svg></span><span data-testid="ci-icon-text" class="gl-ml-2 gl-mr-3 gl-self-center gl-whitespace-nowrap gl-leading-1">Running</span></a> <div class="gl-flex gl-flex-col gl-items-end lg:gl-items-start gl-text-sm"><!----> <!----></div></div></div></td><td aria-colindex="2" data-label="Pipeline" role="cell" class="!gl-border-none"><div><div data-testid="pipeline-url-table-cell" class="pipeline-tags" ref-color="gl-text-default"><!----> <div data-testid="commit-title-container" class="commit-title gl-mb-2"><span class="gl-flex"><span class="gl-min-w-0 -gl-mb-3 -gl-ml-3 -gl-mr-3 -gl-mt-3 gl-grow gl-truncate gl-p-3"><a href="https://gitlab-v2.innotech-vn.com/the-ai-team/innocody/innocody-engine/-/commit/84acef7360827487fbc4ee5895d38b2c8c04a0c5" data-testid="commit-title" class="commit-row-message gl-link">feat: html parser enhance init</a></span></span></div> <div class="gl-mb-2"><a href="https://gitlab-v2.innotech-vn.com/the-ai-team/innocody/innocody-engine/-/pipelines/687" data-testid="pipeline-url-link" class="gl-mr-1 gl-link">#687</a> <div class="gl-inline-flex gl-rounded-base gl-bg-strong gl-px-2"><span class="gl-min-w-0"><svg data-testid="commit-icon-type" role="img" aria-hidden="true" class="gl-icon s12 gl-fill-icon-subtle" title="Merge Request"><use href="/assets/icons-ca86b3ebff8cbe14f7728864eabad153d00b66986018226fe439015884de11c2.svg#merge-request"></use></svg> <a href="https://gitlab-v2.innotech-vn.com/the-ai-team/innocody/innocody-engine/-/merge_requests/9" data-testid="merge-request-ref" class="gl-text-sm gl-text-subtle gl-font-monospace hover:gl-text-subtle gl-link">9</a></span></div> <div class="gl-inline-block gl-rounded-base gl-bg-strong gl-px-2 gl-text-sm"><svg data-testid="commit-icon" role="img" aria-hidden="true" class="gl-mr-1 gl-icon s12 gl-fill-icon-subtle" title="Commit"><use href="/assets/icons-ca86b3ebff8cbe14f7728864eabad153d00b66986018226fe439015884de11c2.svg#commit"></use></svg> <a href="https://gitlab-v2.innotech-vn.com/the-ai-team/innocody/innocody-engine/-/commit/84acef7360827487fbc4ee5895d38b2c8c04a0c5" data-testid="commit-short-sha" class="gl-mr-0 gl-text-sm gl-text-subtle gl-font-monospace hover:gl-text-subtle gl-link">84acef73</a></div> <a href="mailto:<EMAIL>" data-username="" class="gl-avatar-link user-avatar-link js-user-link gl-ml-1 gl-link gl-link-meta"><span class=""><img src="./test_files/ac836b6a430d7f3a15fd4987c270c03d6aaf9c285b1f9237b1555252a5194c11(1)" alt="" class="gl-bg-cover gl-avatar gl-avatar-circle gl-avatar-s16" data-src="https://secure.gravatar.com/avatar/ac836b6a430d7f3a15fd4987c270c03d6aaf9c285b1f9237b1555252a5194c11?s=80&amp;d=identicon" data-testid="user-avatar-image"> <!----></span> <!----> </a></div> <div class="label-container gl-mt-1 gl-flex gl-flex-wrap gl-gap-2"><!----> <!----> <button title="Latest pipeline for the most recent commit on this ref" data-testid="pipeline-url-latest" class="!gl-cursor-default gl-rounded-pill gl-border-none gl-bg-transparent gl-p-0"><span class="gl-badge badge badge-pill badge-success"><!----> <span class="gl-badge-content">latest</span></span></button> <!----> <!----> <!----> <!----> <!----> <!----> <!----> <button title="This pipeline ran on the contents of the merge request&#39;s source branch, not the target branch." data-testid="pipeline-url-detached" class="!gl-cursor-default gl-rounded-pill gl-border-none gl-bg-transparent gl-p-0"><span class="gl-badge badge badge-pill badge-info"><!----> <span class="gl-badge-content">merge request</span></span></button> <!----> <!----> <!----></div></div></div></td><td aria-colindex="3" data-label="Created by" role="cell" class="!gl-border-none !gl-hidden lg:!gl-table-cell"><div><div data-testid="pipeline-triggerer" class="pipeline-triggerer"><a href="https://gitlab-v2.innotech-vn.com/khangnh" title="Nguyen Hoang Khang" class="gl-avatar-link gl-ml-3 gl-link gl-link-meta"><img src="./test_files/e5f52437484dc7deba0f9392a01ad4a1929ce99d3379b7674061c774ed6c7441" alt="avatar" class="gl-avatar gl-avatar-circle gl-avatar-s32"></a></div></div></td><td aria-colindex="4" data-label="Stages" role="cell" class="!gl-border-none"><div><div data-testid="pipeline-mini-graph"><!----> <!----> <div class="gl-inline gl-align-middle"><div class="pipeline-mini-graph-stage-container dropdown gl-my-1 gl-mr-2 gl-inline-flex gl-align-middle"><div class="gl-disclosure-dropdown gl-new-dropdown" data-testid="pipeline-mini-graph-dropdown" aria-label="View Stage: test"><div id="dropdown-toggle-btn-49" data-testid="base-dropdown-toggle" class="gl-new-dropdown-custom-toggle"><button data-testid="pipeline-mini-graph-dropdown-toggle" title="test: running" type="button" class="btn !gl-rounded-full btn-link btn-md gl-button" aria-haspopup="false" aria-expanded="false" aria-controls="base-dropdown-51" aria-labelledby="undefined"><!----> <!---->  <span class="gl-button-text"><span variant="info" aria-label="Status: " data-testid="ci-icon" class="ci-icon gl-inline-flex gl-items-center gl-text-sm ci-icon-variant-info"><span class="ci-icon-gl-icon-wrapper"><svg data-testid="status_running_borderless-icon" role="img" aria-hidden="true" class="gl-icon s16 gl-fill-current"><use href="/assets/icons-ca86b3ebff8cbe14f7728864eabad153d00b66986018226fe439015884de11c2.svg#status_running_borderless"></use></svg></span><!----></span></span></button></div> <div id="base-dropdown-51" data-testid="base-dropdown-menu" class="gl-new-dropdown-panel gl-absolute" style="left: 0px; top: -192px;"><div class="gl-new-dropdown-arrow" style="left: 7px; bottom: -4px; transform: rotate(225deg);"></div> <div class="gl-new-dropdown-inner"> <div data-testid="pipeline-stage-dropdown-menu-title" class="gl-border-b gl-flex gl-min-h-8 gl-items-center !gl-p-4 gl-text-sm gl-font-bold gl-leading-1 gl-border-b-solid"><span>Stage: test</span></div> <!----> <div id="disclosure-50" aria-labelledby="dropdown-toggle-btn-49" data-testid="disclosure-content" tabindex="-1" class="gl-new-dropdown-contents" style="max-height: 547.609px;">  <ul data-testid="pipeline-mini-graph-dropdown-menu-list" class="gl-m-0 gl-w-34 gl-overflow-y-auto gl-p-0"><!----> <li data-testid="passed-jobs"><!----> <ul class="gl-mb-0 gl-list-none gl-pl-0"><li tabindex="0" data-testid="ci-job-item" class="ci-job-component gl-new-dropdown-item"><a href="https://gitlab-v2.innotech-vn.com/the-ai-team/innocody/innocody-engine/-/jobs/1793" tabindex="-1" class="gl-new-dropdown-item-content"><span class="gl-new-dropdown-item-text-wrapper"><div class="-gl-my-2 gl-flex gl-items-center gl-justify-between"><span class="gl-flex gl-items-center" title="Running" data-testid="job-name"><span variant="info" aria-label="Status: " data-testid="ci-icon" class="ci-icon gl-inline-flex gl-items-center gl-text-sm gl-leading-0 ci-icon-variant-info" size="16"><span class="ci-icon-gl-icon-wrapper"><svg data-testid="status_running_borderless-icon" role="img" aria-hidden="true" class="gl-icon s16 gl-fill-current"><use href="/assets/icons-ca86b3ebff8cbe14f7728864eabad153d00b66986018226fe439015884de11c2.svg#status_running_borderless"></use></svg></span><!----></span> <span title="test-linux-x64" class="gl-line-clamp-2 gl-pl-3 gl-wrap-anywhere">
    test-linux-x64
  </span></span> <div class="gl-ml-6"><button title="Cancel" aria-label="Cancel" data-testid="ci-action-button" type="button" class="btn gl-h-6 gl-w-6 !gl-rounded-full !gl-p-0 btn-default btn-sm gl-button"><!----> <!---->  <span class="gl-button-text"><svg data-testid="cancel-icon" role="img" aria-hidden="true" class="gl-icon s12 gl-fill-current"><use href="/assets/icons-ca86b3ebff8cbe14f7728864eabad153d00b66986018226fe439015884de11c2.svg#cancel"></use></svg></span></button> <!----></div></div></span></a></li><li tabindex="0" data-testid="ci-job-item" class="ci-job-component gl-new-dropdown-item"><a href="https://gitlab-v2.innotech-vn.com/the-ai-team/innocody/innocody-engine/-/jobs/1796" tabindex="-1" class="gl-new-dropdown-item-content"><span class="gl-new-dropdown-item-text-wrapper"><div class="-gl-my-2 gl-flex gl-items-center gl-justify-between"><span class="gl-flex gl-items-center" title="Running" data-testid="job-name"><span variant="info" aria-label="Status: " data-testid="ci-icon" class="ci-icon gl-inline-flex gl-items-center gl-text-sm gl-leading-0 ci-icon-variant-info" size="16"><span class="ci-icon-gl-icon-wrapper"><svg data-testid="status_running_borderless-icon" role="img" aria-hidden="true" class="gl-icon s16 gl-fill-current"><use href="/assets/icons-ca86b3ebff8cbe14f7728864eabad153d00b66986018226fe439015884de11c2.svg#status_running_borderless"></use></svg></span><!----></span> <span title="test-win32-x64" class="gl-line-clamp-2 gl-pl-3 gl-wrap-anywhere">
    test-win32-x64
  </span></span> <div class="gl-ml-6"><button title="Cancel" aria-label="Cancel" data-testid="ci-action-button" type="button" class="btn gl-h-6 gl-w-6 !gl-rounded-full !gl-p-0 btn-default btn-sm gl-button"><!----> <!---->  <span class="gl-button-text"><svg data-testid="cancel-icon" role="img" aria-hidden="true" class="gl-icon s12 gl-fill-current"><use href="/assets/icons-ca86b3ebff8cbe14f7728864eabad153d00b66986018226fe439015884de11c2.svg#cancel"></use></svg></span></button> <!----></div></div></span></a></li><li tabindex="0" data-testid="ci-job-item" class="ci-job-component gl-new-dropdown-item"><a href="https://gitlab-v2.innotech-vn.com/the-ai-team/innocody/innocody-engine/-/jobs/1794" tabindex="-1" class="gl-new-dropdown-item-content"><span class="gl-new-dropdown-item-text-wrapper"><div class="-gl-my-2 gl-flex gl-items-center gl-justify-between"><span class="gl-flex gl-items-center" title="Passed" data-testid="job-name"><span variant="success" aria-label="Status: " data-testid="ci-icon" class="ci-icon gl-inline-flex gl-items-center gl-text-sm gl-leading-0 ci-icon-variant-success" size="16"><span class="ci-icon-gl-icon-wrapper"><svg data-testid="status_success_borderless-icon" role="img" aria-hidden="true" class="gl-icon s16 gl-fill-current"><use href="/assets/icons-ca86b3ebff8cbe14f7728864eabad153d00b66986018226fe439015884de11c2.svg#status_success_borderless"></use></svg></span><!----></span> <span title="test-darwin-aarch64" class="gl-line-clamp-2 gl-pl-3 gl-wrap-anywhere">
    test-darwin-aarch64
  </span></span> <div class="gl-ml-6"><button title="Run again" aria-label="Run again" data-testid="ci-action-button" type="button" class="btn gl-h-6 gl-w-6 !gl-rounded-full !gl-p-0 btn-default btn-sm gl-button"><!----> <!---->  <span class="gl-button-text"><svg data-testid="retry-icon" role="img" aria-hidden="true" class="gl-icon s12 gl-fill-current"><use href="/assets/icons-ca86b3ebff8cbe14f7728864eabad153d00b66986018226fe439015884de11c2.svg#retry"></use></svg></span></button> <!----></div></div></span></a></li><li tabindex="0" data-testid="ci-job-item" class="ci-job-component gl-new-dropdown-item"><a href="https://gitlab-v2.innotech-vn.com/the-ai-team/innocody/innocody-engine/-/jobs/1795" tabindex="-1" class="gl-new-dropdown-item-content"><span class="gl-new-dropdown-item-text-wrapper"><div class="-gl-my-2 gl-flex gl-items-center gl-justify-between"><span class="gl-flex gl-items-center" title="Passed" data-testid="job-name"><span variant="success" aria-label="Status: " data-testid="ci-icon" class="ci-icon gl-inline-flex gl-items-center gl-text-sm gl-leading-0 ci-icon-variant-success" size="16"><span class="ci-icon-gl-icon-wrapper"><svg data-testid="status_success_borderless-icon" role="img" aria-hidden="true" class="gl-icon s16 gl-fill-current"><use href="/assets/icons-ca86b3ebff8cbe14f7728864eabad153d00b66986018226fe439015884de11c2.svg#status_success_borderless"></use></svg></span><!----></span> <span title="test-darwin-x64" class="gl-line-clamp-2 gl-pl-3 gl-wrap-anywhere">
    test-darwin-x64
  </span></span> <div class="gl-ml-6"><button title="Run again" aria-label="Run again" data-testid="ci-action-button" type="button" class="btn gl-h-6 gl-w-6 !gl-rounded-full !gl-p-0 btn-default btn-sm gl-button"><!----> <!---->  <span class="gl-button-text"><svg data-testid="retry-icon" role="img" aria-hidden="true" class="gl-icon s12 gl-fill-current"><use href="/assets/icons-ca86b3ebff8cbe14f7728864eabad153d00b66986018226fe439015884de11c2.svg#retry"></use></svg></span></button> <!----></div></div></span></a></li></ul></li></ul></div> </div></div></div></div></div> <!----> <!----></div></div></td><td aria-colindex="5" data-label="Actions" role="cell" class="!gl-border-none"><div><div class="gl-text-right"><!----> <div class="btn-group"><!----> <!----> <button aria-label="Cancel the running pipeline" title="Cancel the running pipeline" data-testid="pipelines-cancel-button" type="button" class="btn js-pipelines-cancel-button gl-ml-1 btn-danger btn-md gl-button btn-icon"><!----> <svg data-testid="cancel-icon" role="img" aria-hidden="true" class="gl-button-icon gl-icon s16 gl-fill-current"><use href="/assets/icons-ca86b3ebff8cbe14f7728864eabad153d00b66986018226fe439015884de11c2.svg#cancel"></use></svg>  <!----></button> <div class="gl-disclosure-dropdown gl-text-left gl-new-dropdown" title="Download artifacts" aria-label="Download artifacts" data-testid="pipeline-multi-actions-dropdown"><button id="dropdown-toggle-btn-52" data-testid="base-dropdown-toggle" aria-expanded="false" aria-controls="base-dropdown-54" type="button" class="btn btn-default btn-md gl-button gl-new-dropdown-toggle gl-new-dropdown-icon-only btn-icon"><!----> <svg data-testid="download-icon" role="img" aria-hidden="true" class="gl-button-icon gl-icon s16 gl-fill-current"><use href="/assets/icons-ca86b3ebff8cbe14f7728864eabad153d00b66986018226fe439015884de11c2.svg#download"></use></svg>  <span class="gl-button-text"><span class="gl-new-dropdown-button-text gl-sr-only">
        Download artifacts
      </span> <svg data-testid="chevron-down-icon" role="img" aria-hidden="true" class="gl-button-icon gl-new-dropdown-chevron gl-icon s16 gl-fill-current"><use href="/assets/icons-ca86b3ebff8cbe14f7728864eabad153d00b66986018226fe439015884de11c2.svg#chevron-down"></use></svg></span></button> <div id="base-dropdown-54" data-testid="base-dropdown-menu" class="gl-new-dropdown-panel !gl-w-31"><div class="gl-new-dropdown-arrow"></div> <div class="gl-new-dropdown-inner"> <div class="gl-flex gl-min-h-8 gl-items-center gl-border-b-1 gl-border-b-dropdown !gl-p-4 gl-text-sm gl-font-bold gl-text-strong gl-border-b-solid">
      Download artifacts
    </div> <!----> <!----> <div id="disclosure-53" aria-labelledby="dropdown-toggle-btn-52" data-testid="disclosure-content" tabindex="-1" class="gl-new-dropdown-contents"> <p data-testid="artifacts-empty-message" class="gl-m-0 gl-px-4 gl-py-3 gl-text-subtle">
    No artifacts found
  </p></div> </div></div></div></div></div></div></td></tr><!----><!----><!----></tbody><!----></table></div></div> <!----> <!----></div></div>

<!---->

<!---->


</main>
</div>


</div>
</div>


<script>
//<![CDATA[
if ('loading' in HTMLImageElement.prototype) {
  document.querySelectorAll('img.lazy').forEach(img => {
    img.loading = 'lazy';
    let imgUrl = img.dataset.src;
    // Only adding width + height for avatars for now
    if (imgUrl.indexOf('/avatar/') > -1 && imgUrl.indexOf('?') === -1) {
      const targetWidth = img.getAttribute('width') || img.width;
      imgUrl += `?width=${targetWidth}`;
    }
    img.src = imgUrl;
    img.removeAttribute('data-src');
    img.classList.remove('lazy');
    img.classList.add('js-lazy-loaded');
    img.dataset.testid = 'js-lazy-loaded-content';
  });
}

//]]>
</script>
<script>
//<![CDATA[
gl = window.gl || {};
gl.experiments = {};


//]]>
</script>




<div></div><div></div></body></html>