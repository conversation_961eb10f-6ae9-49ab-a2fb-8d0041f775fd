[build.env]
passthrough = [
    "RUSTFLAGS",
    "OPENSSL_STATIC=1",
    "OPENSSL_LIB_DIR=/opt/openssl/lib",
    "OPENSSL_INCLUDE_DIR=/opt/openssl/include",
    "PKG_CONFIG_ALL_STATIC=1",
    "PKG_CONFIG_PATH=/opt/openssl/lib/pkgconfig",
    "PATH=/opt/mold/bin:/opt/openssl/bin:/root/.cargo/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin"
]

[target.aarch64-unknown-linux-gnu]
image = "ghcr.io/cross-rs/aarch64-unknown-linux-gnu:main"
pre-build = [
    # Install base dependencies
    "apt-get update && apt-get install --assume-yes curl wget unzip gnupg software-properties-common lsb-release",

    # Add official LLVM APT repository and install Clang 20
    "wget https://apt.llvm.org/llvm.sh && chmod +x llvm.sh && ./llvm.sh 20",

    # Install Clang 20, LLD, and dev libraries
    "apt-get install --assume-yes clang-20 lld-20 llvm-20-dev libssl-dev",

    # Set clang-20 as default
    "update-alternatives --install /usr/bin/clang clang /usr/bin/clang-20 100",
    "update-alternatives --install /usr/bin/clang++ clang++ /usr/bin/clang++-20 100",

    "clang --version",

    # Install mold
    "curl -LO https://github.com/rui314/mold/releases/download/v2.40.1/mold-2.40.1-aarch64-linux.tar.gz",
    "tar xzf mold-2.40.1-aarch64-linux.tar.gz && mkdir -p /opt/mold && cp -r mold-2.40.1-aarch64-linux/bin /opt/mold/",
    "chmod +x /opt/mold/bin/mold",
    "ln -sf /opt/mold/bin/mold /usr/local/bin/mold",
    "ln -sf /opt/mold/bin/ld.mold /usr/local/bin/ld.mold",

    # Verify mold
    "echo '[Check] Verifying mold installation...'",
    "which mold && echo '[OK] mold is in PATH' || (echo '[ERROR] mold not found in PATH' && exit 1)",
    "mold --version && echo '[OK] mold version check passed' || (echo '[ERROR] mold failed to run' && exit 1)",

    "echo '[Check] Verifying if clang uses mold as linker...'",
    "echo 'int main() { return 0; }' > test.c",
    "clang --target=aarch64-unknown-linux-gnu -fuse-ld=mold test.c -o test.out && echo '[OK] clang successfully used mold' || (echo '[ERROR] clang failed to link with mold' && exit 1)",
    "rm -f test.c test.out",

    # Install OpenSSL
    "curl -LO https://github.com/openssl/openssl/releases/download/openssl-3.5.1/openssl-3.5.1.tar.gz",
    "tar xzf openssl-3.5.1.tar.gz",
    "cd openssl-3.5.1 && CC='clang --target=aarch64-unknown-linux-gnu' CFLAGS='-fPIC' LDFLAGS='-fuse-ld=mold' ./Configure linux-aarch64 no-shared -fPIC --prefix=/opt/openssl && make -j$(nproc) && make install_sw install_dev install_ssldirs",
    "ln -s /opt/openssl/lib64 /opt/openssl/lib",

    # Verify OpenSSL
    "echo '[Check] Verifying OpenSSL installation...'",
    "test -f /opt/openssl/lib/libssl.a && echo '[OK] libssl.a found' || (echo '[ERROR] Missing libssl.a' && exit 1)",
    "test -f /opt/openssl/lib/libcrypto.a && echo '[OK] libcrypto.a found' || (echo '[ERROR] Missing libcrypto.a' && exit 1)",
    "test -d /opt/openssl/include/openssl && echo '[OK] OpenSSL headers found' || (echo '[ERROR] Missing OpenSSL headers' && exit 1)",

    # Install rustup & rustfmt
    "curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh -s -- -y --no-modify-path",
    "bash -c '. \"$HOME/.cargo/env\" && rustup component add rustfmt && echo \"[OK] rustfmt installed\" && rustup target add aarch64-unknown-linux-gnu'"
]

[target.x86_64-unknown-linux-gnu]
image = "ghcr.io/cross-rs/x86_64-unknown-linux-gnu:main"
pre-build = [
    # Update and install base packages
    "apt-get update && apt-get install --assume-yes curl wget unzip gnupg software-properties-common lsb-release",

    # Add LLVM APT repository for latest Clang
    "wget https://apt.llvm.org/llvm.sh && chmod +x llvm.sh && ./llvm.sh 20",

    # Install Clang 20 and required development tools
    "apt-get install --assume-yes clang-20 lld-20 lldb-20 llvm-20-dev libssl-dev",

    # Set clang-20 as default
    "update-alternatives --install /usr/bin/clang clang /usr/bin/clang-20 100",
    "update-alternatives --install /usr/bin/clang++ clang++ /usr/bin/clang++-20 100",

    "clang --version",

    # Install mold
    "curl -LO https://github.com/rui314/mold/releases/download/v2.40.1/mold-2.40.1-x86_64-linux.tar.gz",
    "tar xzf mold-2.40.1-x86_64-linux.tar.gz && mkdir -p /opt/mold && cp -r mold-2.40.1-x86_64-linux/bin /opt/mold/",
    "chmod +x /opt/mold/bin/mold",
    "ln -sf /opt/mold/bin/mold /usr/local/bin/mold",
    "ln -sf /opt/mold/bin/ld.mold /usr/local/bin/ld.mold",

    # Verify mold
    "echo '[Check] Verifying mold installation...'",
    "which mold && echo '[OK] mold is in PATH' || (echo '[ERROR] mold not found in PATH' && exit 1)",
    "mold --version && echo '[OK] mold version check passed' || (echo '[ERROR] mold failed to run' && exit 1)",

    "echo '[Check] Verifying if clang uses mold as linker...'",
    "echo 'int main() { return 0; }' > test.c",
    "clang --target=x86_64-unknown-linux-gnu -fuse-ld=mold test.c -o test.out && echo '[OK] clang successfully used mold' || (echo '[ERROR] clang failed to link with mold' && exit 1)",
    "rm -f test.c test.out",

    # Install OpenSSL
    "curl -LO https://github.com/openssl/openssl/releases/download/openssl-3.5.1/openssl-3.5.1.tar.gz",
    "tar xzf openssl-3.5.1.tar.gz",
    "cd openssl-3.5.1 && CC='clang --target=x86_64-unknown-linux-gnu' CFLAGS='-fPIC' LDFLAGS='-fuse-ld=mold' ./Configure linux-x86_64 no-shared -fPIC --prefix=/opt/openssl && make -j$(nproc) && make install_sw install_dev install_ssldirs",
    "ln -s /opt/openssl/lib64 /opt/openssl/lib",

    # Verify OpenSSL
    "echo '[Check] Verifying OpenSSL installation...'",
    "test -f /opt/openssl/lib/libssl.a && echo '[OK] libssl.a found' || (echo '[ERROR] Missing libssl.a' && exit 1)",
    "test -f /opt/openssl/lib/libcrypto.a && echo '[OK] libcrypto.a found' || (echo '[ERROR] Missing libcrypto.a' && exit 1)",
    "test -d /opt/openssl/include/openssl && echo '[OK] OpenSSL headers found' || (echo '[ERROR] Missing OpenSSL headers' && exit 1)",

    # Install rustup & rustfmt
    "curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh -s -- -y --no-modify-path",
    "bash -c '. \"$HOME/.cargo/env\" && rustup component add rustfmt && echo \"[OK] rustfmt installed\" && rustup target add x86_64-unknown-linux-gnu'"
]