#!/bin/bash

# Configurable names
IMAGE_NAME="inno-server"
CONTAINER_NAME="inno-server"

# Cleanup function on Ctrl+C
cleanup() {
    echo ">> Cleaning up..."
    docker stop "$CONTAINER_NAME" >/dev/null 2>&1
    docker rm "$CONTAINER_NAME" >/dev/null 2>&1
    echo ">> Container stopped and removed."
    exit 0
}
trap cleanup SIGINT

# Build step
echo ">> Building image..."
if docker build -t "$IMAGE_NAME" .; then
    echo ">> Build successful."
else
    echo ">> Build failed. Exiting."
    exit 1
fi

# Run the container in detached mode
echo ">> Running container..."
docker run --rm -p 8008:8008 \
  -v perm-storage:/perm_storage \
  --name "$CONTAINER_NAME" \
  "$IMAGE_NAME" & 

CONTAINER_PID=$!

# Wait until process is interrupted
wait $CONTAINER_PID